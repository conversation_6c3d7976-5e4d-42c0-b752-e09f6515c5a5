@extends('layouts/layoutMaster')


@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">الإدارة /</span> إعدادات متطلبات التوثيق
    </h4>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">إعدادات متطلبات التوثيق</h5>
        </div>
        <div class="card-body">
            @if (session()->has('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <form action="{{ route('admin.verifications.requirements.store') }}" method="POST" id="requirementsForm">
                @csrf
                <div id="requirements-container">
                    @foreach($requirementsData as $index => $requirement)
                        <div class="requirement-item border rounded p-3 mb-3" id="requirement-{{ $index }}">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">نوع الحقل</label>
                                    <select name="requirements[{{ $index }}][type]" class="form-select">
                                        <option value="text" {{ $requirement['type'] == 'text' ? 'selected' : '' }}>نص</option>
                                        <option value="textarea" {{ $requirement['type'] == 'textarea' ? 'selected' : '' }}>نص طويل</option>
                                        <option value="file" {{ $requirement['type'] == 'file' ? 'selected' : '' }}>ملف</option>
                                        <option value="select" {{ $requirement['type'] == 'select' ? 'selected' : '' }}>قائمة اختيار</option>
                                    </select>
                                    @error("requirements.{$index}.type")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">عنوان الحقل</label>
                                    <input type="text" name="requirements[{{ $index }}][title]" value="{{ $requirement['title'] }}" class="form-control" placeholder="مثال: رقم الهوية">
                                    @error("requirements.{$index}.title")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">الوصف</label>
                                    <input type="text" name="requirements[{{ $index }}][description]" value="{{ $requirement['description'] ?? '' }}" class="form-control" placeholder="وصف اختياري للحقل">
                                    @error("requirements.{$index}.description")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-1">
                                    <label class="form-label">مطلوب</label>
                                    <div class="form-check">
                                        <input type="checkbox" name="requirements[{{ $index }}][is_required]" value="1" class="form-check-input" id="required-{{ $index }}" {{ isset($requirement['is_required']) && $requirement['is_required'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="required-{{ $index }}"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="select-options-container mt-2 {{ $requirement['type'] === 'select' ? '' : 'd-none' }}" id="options-container-{{ $index }}">
                                <div class="row">
                                    <div class="col-md-12">
                                        <label class="form-label">خيارات القائمة (مفصولة بفاصلة)</label>
                                        <input type="text" name="requirements[{{ $index }}][options]" value="{{ $requirement['options'] ?? '' }}" class="form-control" placeholder="فرد,شركة,متجر">
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-12 text-end">
                                    @if(count($requirementsData) > 1)
                                        <button type="button" onclick="removeRequirement({{ $index }})" class="btn btn-sm btn-outline-danger">
                                            <i class="ti ti-trash"></i> حذف
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button type="button" onclick="addRequirement()" class="btn btn-outline-primary me-2">
                            <i class="ti ti-plus"></i> إضافة حقل جديد
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-device-floppy"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    let requirementCount = {{ count($requirementsData) }};

    
    function addRequirement() {
        const index = requirementCount;
        const container = document.getElementById('requirements-container');
        
        const requirementHtml = `
            <div class="requirement-item border rounded p-3 mb-3" id="requirement-${index}">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">نوع الحقل</label>
                        <select name="requirements[${index}][type]" class="form-select" onchange="toggleOptionsContainer(${index}, this.value)">
                            <option value="text">نص</option>
                            <option value="textarea">نص طويل</option>
                            <option value="file">ملف</option>
                            <option value="select">قائمة اختيار</option>
                        </select>
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">عنوان الحقل</label>
                        <input type="text" name="requirements[${index}][title]" class="form-control" placeholder="مثال: رقم الهوية">
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">الوصف</label>
                        <input type="text" name="requirements[${index}][description]" class="form-control" placeholder="وصف اختياري للحقل">
                    </div>

                    <div class="col-md-1">
                        <label class="form-label">مطلوب</label>
                        <div class="form-check">
                            <input type="checkbox" name="requirements[${index}][is_required]" value="1" class="form-check-input" id="required-${index}" checked>
                            <label class="form-check-label" for="required-${index}"></label>
                        </div>
                    </div>
                </div>

                <div class="select-options-container mt-2 d-none" id="options-container-${index}">
                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">خيارات القائمة (مفصولة بفاصلة)</label>
                            <input type="text" name="requirements[${index}][options]" class="form-control" placeholder="فرد,شركة,متجر">
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-md-12 text-end">
                        <button type="button" onclick="removeRequirement(${index})" class="btn btn-sm btn-outline-danger">
                            <i class="ti ti-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Append the new requirement to the container
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = requirementHtml;
        container.appendChild(tempDiv.firstElementChild);
        
        requirementCount++;
    }
    
    function removeRequirement(index) {
        const requirement = document.getElementById(`requirement-${index}`);
        if (requirement) {
            requirement.remove();
        }
    }
    
    function toggleOptionsContainer(index, type) {
        const optionsContainer = document.getElementById(`options-container-${index}`);
        if (optionsContainer) {
            if (type === 'select') {
                optionsContainer.classList.remove('d-none');
            } else {
                optionsContainer.classList.add('d-none');
            }
        }
    }
    
    // Initialize the select fields to show/hide options containers
    document.addEventListener('DOMContentLoaded', function() {
        const selects = document.querySelectorAll('select[name^="requirements"][name$="[type]"]');
        selects.forEach(select => {
            const name = select.getAttribute('name');
            const match = name.match(/requirements\[(\d+)\]/);
            if (match) {
                const index = match[1];
                toggleOptionsContainer(index, select.value);
                
                // Add change event listener
                select.addEventListener('change', function() {
                    toggleOptionsContainer(index, this.value);
                });
            }
        });
    });
</script>

<style>
    .requirement-item {
        background-color: var(--bs-light-bg-subtle);
        border: 1px solid var(--bs-border-color);
        transition: background-color 0.2s ease-in-out;
    }
    .requirement-item:hover {
        background-color: var(--bs-light-bg-subtle-hover);
    }
    @media (prefers-color-scheme: dark) {
        .requirement-item {
            background-color: var(--bs-light-bg-subtle-hover);
            border-color: var(--bs-border-color);
        }
    }
</style>
@endsection
