@extends('web.layouts.layout')
@section('title', $seller->name)

@section('page-meta')
<meta name="description" content="{{ __('الملف الشخصي للبائع') }} {{ $seller->name }} {{ __('في موقع حراجي') }}">
<meta name="keywords" content="{{ $seller->name }}, {{ __('حراجي, بائع, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li>{{ __('الملف الشخصي للبائع') }}</li>
                <li>{{ $seller->name }}</li>
            </ul>
        </div>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar -->
            <div class="w-full lg:w-1/4">
                <div class="bg-base-100 rounded-lg shadow-md p-6 sticky top-24">
                    <div class="flex flex-col items-center mb-6">
                        <div class="avatar mb-4">
                            <div class="w-24 h-24 rounded-full">
                                @php
                                    $avatar = $seller->avatar(true);
                                @endphp
                                
                                @if(is_array($avatar) && $avatar['type'] === 'initials')
                                    <div class="w-full h-full flex items-center justify-center text-white text-xl font-bold {{ $avatar['color'] }}">
                                        {{ $avatar['initials'] }}
                                    </div>
                                @else
                                    <img src="{{ $avatar }}" alt="{{ $seller->name }}">
                                @endif
                            </div>
                        </div>
                        <h1 class="text-2xl font-bold text-center">{{ $seller->name }}</h1>
                        <p class="text-base-content/70 text-center">{{ __('عضو منذ') }} {{ $seller->created_at->format('Y-m-d') }}</p>
                    </div>

                    <div class="space-y-4 mb-6">
                        @if($seller->userInfo && $seller->userInfo->phone)
                            <div class="flex items-center">
                                <i class="fas fa-phone-alt mr-3 rtl:ml-3 rtl:mr-0 text-primary"></i>
                                <span>{{ $seller->userInfo->phone }}</span>
                            </div>
                        @endif

                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-3 rtl:ml-3 rtl:mr-0 text-primary"></i>
                            <span>{{ $seller->email }}</span>
                        </div>

                        <div class="flex items-center">
                            <i class="fas fa-tag mr-3 rtl:ml-3 rtl:mr-0 text-primary"></i>
                            <span>{{ $seller->products->count() }} {{ __('إعلان') }}</span>
                        </div>
                    </div>

                    @if($seller->userInfo && $seller->userInfo->bio)
                        <div class="mb-6">
                            <h2 class="text-lg font-bold mb-2">{{ __('نبذة') }}</h2>
                            <p>{{ $seller->userInfo->bio }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Main Content -->
            <div class="w-full lg:w-3/4">
                <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">{{ __('إعلانات') }} {{ $seller->name }}</h2>
                        <span class="text-base-content/70">{{ $seller->products->count() }} {{ __('إعلان') }}</span>
                    </div>

                    <!-- Products Component -->
                    @livewire('products', [
                        'pageType' => 'seller',
                        'query' => $seller->id,
                        'filters' => [
                            'sort' => request('sort', 'newest')
                        ],
                        'showCategories' => false,
                        'showFilters' => false
                    ])
                </div>
            </div>
        </div>
    </div>
</main>
@endsection
