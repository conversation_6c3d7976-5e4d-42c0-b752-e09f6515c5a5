@extends('web.layouts.layout')
@section('title', $category->title)

@section('page-meta')
<meta name="description" content="{{ __('تصفح إعلانات قسم') }} {{ $category->title }} {{ __('في موقع حراجي') }}">
<meta name="keywords" content="{{ $category->title }}, {{ __('حراجي, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.categories') }}">{{ __('الأقسام') }}</a></li>
                @if($category->parent)
                    <li><a href="{{ route('web.category', $category->parent->slug) }}">{{ $category->parent->title }}</a></li>
                @endif
                <li>{{ $category->title }}</li>
            </ul>
        </div>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <div class="w-full lg:w-1/4">
                <div class="bg-base-200 rounded-lg p-6 sticky top-24">
                    <h2 class="text-xl font-bold mb-4">{{ __('تصفية النتائج') }}</h2>

                    <form action="{{ route('web.category', $category->slug) }}" method="GET" id="filter-form">
                        <!-- Subcategories -->
                        @if(count($childCategories) > 0)
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold">{{ __('الأقسام الفرعية') }}</h3>
                                <button type="button" id="clear-subcategories" class="text-xs text-primary hover:underline">{{ __('مسح الكل') }}</button>
                            </div>

                            <!-- Search subcategories -->
                            <div class="relative mb-2">
                                <input type="text" id="subcategory-search" placeholder="{{ __('بحث في الأقسام الفرعية...') }}" class="input input-bordered input-sm w-full">
                                <button type="button" class="absolute inset-y-0 right-0 rtl:right-auto rtl:left-0 flex items-center px-3">
                                    <i class="fas fa-search text-gray-400 text-sm"></i>
                                </button>
                            </div>

                            <div class="max-h-48 overflow-y-auto pr-2 subcategories-container">
                                @foreach($childCategories as $childCategory)
                                    <label class="cursor-pointer flex items-center gap-2 mb-2 subcategory-item">
                                        <input type="checkbox" name="subcategories[]" value="{{ $childCategory->id }}" class="checkbox checkbox-sm checkbox-primary"
                                            {{ in_array($childCategory->id, request('subcategories', [])) ? 'checked' : '' }}>
                                        <span class="subcategory-name">{{ $childCategory->title }}</span>
                                        <span class="text-xs text-base-content/70 ml-auto">({{ $childCategory->products_count ?? 0 }})</span>
                                    </label>
                                @endforeach
                            </div>

                            <!-- No results message -->
                            <div id="no-subcategories-found" class="hidden text-center py-2 text-sm text-base-content/70">
                                {{ __('لا توجد أقسام فرعية مطابقة') }}
                            </div>
                        </div>
                        <div class="divider my-4"></div>
                        @endif

                        <!-- Price Range -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('نطاق السعر') }}</h3>
                            <div class="flex items-center gap-2">
                                <input type="number" name="price_min" placeholder="{{ __('من') }}" class="input input-bordered w-full" value="{{ request('price_min') }}">
                                <span>-</span>
                                <input type="number" name="price_max" placeholder="{{ __('إلى') }}" class="input input-bordered w-full" value="{{ request('price_max') }}">
                            </div>
                        </div>

                        <!-- Condition -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('الحالة') }}</h3>
                            <div class="flex flex-col gap-2">
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="new" class="radio radio-primary" {{ request('condition') == 'new' ? 'checked' : '' }}>
                                    <span>{{ __('جديد') }}</span>
                                </label>
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="used" class="radio radio-primary" {{ request('condition') == 'used' ? 'checked' : '' }}>
                                    <span>{{ __('مستعمل') }}</span>
                                </label>
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="" class="radio radio-primary" {{ request('condition') == '' ? 'checked' : '' }}>
                                    <span>{{ __('الكل') }}</span>
                                </label>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('الموقع') }}</h3>
                            <select name="location_id" class="select select-bordered w-full">
                                <option value="">{{ __('جميع المواقع') }}</option>
                                @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ request('location_id') == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Sort By -->
                        <div class="mb-6">
                            <h3 class="font-bold mb-2">{{ __('ترتيب حسب') }}</h3>
                            <select name="sort" class="select select-bordered w-full">
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>{{ __('الأحدث') }}</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>{{ __('السعر: من الأقل للأعلى') }}</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>{{ __('السعر: من الأعلى للأقل') }}</option>
                                <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>{{ __('الأكثر مشاهدة') }}</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-full">{{ __('تطبيق الفلتر') }}</button>
                    </form>
                </div>
            </div>

            <!-- Main Content -->
            <div class="w-full lg:w-3/4">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold flex items-center">
                        @if($category->icon)
                            <i class="{{ $category->icon }} mr-2 rtl:ml-2 rtl:mr-0"></i>
                        @endif
                        {{ $category->title }}
                    </h1>
                    <span class="text-base-content/70">{{ $category->title }}</span>
                </div>



                <!-- Products using Livewire component -->
                @php
                    // Prepare filters from request
                    $filters = [
                        'price_min' => request('price_min'),
                        'price_max' => request('price_max'),
                        'condition' => request('condition'),
                        'location_id' => request('location_id'),
                        'sort' => request('sort', 'newest'),
                    ];

                    // Add subcategories filter if present
                    if (request()->has('subcategories')) {
                        $filters['subcategories'] = request('subcategories');
                    }
                @endphp

                <livewire:products
                    :page-type="'category'"
                    :key="$category->id"
                    :filters="$filters"
                    :show-categories="false"
                    wire:key="category-products-{{ $category->id }}"
                />
            </div>
        </div>
    </div>
</main>
@endsection

@section('page-script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Subcategory search functionality
        const subcategorySearch = document.getElementById('subcategory-search');
        const subcategoryItems = document.querySelectorAll('.subcategory-item');
        const noSubcategoriesFound = document.getElementById('no-subcategories-found');
        const clearSubcategoriesBtn = document.getElementById('clear-subcategories');

        if (subcategorySearch) {
            subcategorySearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                let visibleCount = 0;

                subcategoryItems.forEach(item => {
                    const subcategoryName = item.querySelector('.subcategory-name').textContent.toLowerCase();
                    if (subcategoryName.includes(searchTerm)) {
                        item.style.display = '';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show/hide "no results" message
                if (visibleCount === 0 && searchTerm !== '') {
                    noSubcategoriesFound.classList.remove('hidden');
                } else {
                    noSubcategoriesFound.classList.add('hidden');
                }
            });
        }

        // Clear all subcategory checkboxes
        if (clearSubcategoriesBtn) {
            clearSubcategoriesBtn.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('input[name="subcategories[]"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Auto-submit the form when clearing filters
                if (document.getElementById('filter-form')) {
                    document.getElementById('filter-form').submit();
                }
            });
        }

        // Auto-submit form when subcategory checkbox is clicked
        const subcategoryCheckboxes = document.querySelectorAll('input[name="subcategories[]"]');
        subcategoryCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (document.getElementById('filter-form')) {
                    document.getElementById('filter-form').submit();
                }
            });
        });
    });
</script>
@endsection