@extends('web.layouts.layout')
@section('title', __('نتائج البحث'))

@section('page-meta')
<meta name="description" content="{{ __('نتائج البحث في موقع حراجي للبيع والشراء') }}">
<meta name="keywords" content="{{ __('حراجي, بحث, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li>{{ __('نتائج البحث') }}</li>
            </ul>
        </div>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <div class="w-full lg:w-1/4">
                <div class="bg-base-200 rounded-lg p-6 sticky top-24">
                    <h2 class="text-xl font-bold mb-4">{{ __('تصفية النتائج') }}</h2>

                    <form action="{{ route('web.search') }}" method="GET">
                        <!-- Search Query -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('البحث') }}</h3>
                            <input type="text" name="q" placeholder="{{ __('كلمات البحث...') }}" class="input input-bordered w-full" value="{{ request('q') }}">
                        </div>

                        <!-- Category -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('القسم') }}</h3>
                            <select name="category_id" class="select select-bordered w-full">
                                <option value="">{{ __('جميع الأقسام') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Price Range -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('نطاق السعر') }}</h3>
                            <div class="flex items-center gap-2">
                                <input type="number" name="price_min" placeholder="{{ __('من') }}" class="input input-bordered w-full" value="{{ request('price_min') }}">
                                <span>-</span>
                                <input type="number" name="price_max" placeholder="{{ __('إلى') }}" class="input input-bordered w-full" value="{{ request('price_max') }}">
                            </div>
                        </div>

                        <!-- Condition -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('الحالة') }}</h3>
                            <div class="flex flex-col gap-2">
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="new" class="radio radio-primary" {{ request('condition') == 'new' ? 'checked' : '' }}>
                                    <span>{{ __('جديد') }}</span>
                                </label>
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="used" class="radio radio-primary" {{ request('condition') == 'used' ? 'checked' : '' }}>
                                    <span>{{ __('مستعمل') }}</span>
                                </label>
                                <label class="cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="condition" value="" class="radio radio-primary" {{ request('condition') == '' ? 'checked' : '' }}>
                                    <span>{{ __('الكل') }}</span>
                                </label>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="mb-4">
                            <h3 class="font-bold mb-2">{{ __('الموقع') }}</h3>
                            <select name="location_id" class="select select-bordered w-full">
                                <option value="">{{ __('جميع المواقع') }}</option>
                                @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ request('location_id') == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Sort By -->
                        <div class="mb-6">
                            <h3 class="font-bold mb-2">{{ __('ترتيب حسب') }}</h3>
                            <select name="sort" class="select select-bordered w-full">
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>{{ __('الأحدث') }}</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>{{ __('السعر: من الأقل للأعلى') }}</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>{{ __('السعر: من الأعلى للأقل') }}</option>
                                <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>{{ __('الأكثر مشاهدة') }}</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-full">{{ __('تطبيق الفلتر') }}</button>
                    </form>
                </div>
            </div>

            <!-- Main Content -->
            <div class="w-full lg:w-3/4">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold">
                        @if(request('q'))
                            {{ __('نتائج البحث عن:') }} "{{ request('q') }}"
                        @else
                            {{ __('نتائج البحث') }}
                        @endif
                    </h1>
                    <span class="text-base-content/70">{{ __('نتائج البحث') }}</span>
                </div>

                <!-- Products using Livewire component -->
                @php
                    // Prepare filters from request
                    $filters = [
                        'price_min' => request('price_min'),
                        'price_max' => request('price_max'),
                        'condition' => request('condition'),
                        'location_id' => request('location_id'),
                        'sort' => request('sort', 'newest'),
                        'category_id' => request('category_id'),
                    ];
                @endphp

                <livewire:products
                    :page-type="'search'"
                    :query="request('q') ?? ''"
                    :filters="$filters"
                    wire:key="search-products-{{ md5(request('q') . json_encode($filters)) }}"
                />
            </div>
        </div>
    </div>
</main>
@endsection
