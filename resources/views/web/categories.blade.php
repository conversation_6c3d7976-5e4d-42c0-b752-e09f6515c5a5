@extends('web.layouts.layout')
@section('title', __('الأقسام'))

@section('page-meta')
<meta name="description" content="{{ __('تصفح جميع أقسام موقع حراجي للبيع والشراء') }}">
<meta name="keywords" content="{{ __('حراجي, أقسام, تصنيفات, بيع, شراء') }}">
@endsection

@section('page-style')
<style>
    .category-parent {
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }
    .category-parent:hover {
        border-color: hsl(var(--p) / 0.2);
    }
    .category-parent.active {
        border-color: hsl(var(--p));
        background-color: hsl(var(--p) / 0.05);
    }
    .subcategory-card {
        transition: all 0.25s ease;
    }
    .subcategory-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px -3px rgba(0, 0, 0, 0.1);
    }
    .category-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        background-color: hsl(var(--p) / 0.1);
        color: hsl(var(--p));
        font-size: 1.5rem;
    }
    .subcategory-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background-color: hsl(var(--p) / 0.1);
        color: hsl(var(--p));
        font-size: 1rem;
    }
    .category-children {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    .category-children.open {
        max-height: 1000px;
    }
    .toggle-btn i {
        transition: transform 0.3s ease;
    }
    .toggle-btn.open i {
        transform: rotate(180deg);
    }
</style>
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li>{{ __('الأقسام') }}</li>
            </ul>
        </div>

        <h1 class="text-3xl font-bold mb-2">{{ __('تصفح الأقسام') }}</h1>
        <p class="text-base-content/70 mb-8">{{ __('اختر من بين مجموعة متنوعة من الأقسام لتجد ما تبحث عنه') }}</p>

        <!-- Categories Tree View -->
        <div class="grid grid-cols-1 gap-6 mb-12">
            @foreach($parentCategories as $parent)
                <div class="category-container">
                    <!-- Parent Category -->
                    <div class="category-parent bg-base-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-4 flex items-center cursor-pointer" onclick="toggleCategory('{{ $parent->id }}')">
                            <div class="category-icon mr-4 rtl:ml-4 rtl:mr-0">
                                @if($parent->icon)
                                    <i class="{{ $parent->icon }}"></i>
                                @else
                                    <i class="fas fa-folder"></i>
                                @endif
                            </div>
                            <div class="flex-1">
                                <h2 class="text-xl font-bold">{{ $parent->title }}</h2>
                                <p class="text-sm text-base-content/70">
                                    {{ $parent->total_products_count }} {{ __('إعلان') }}
                                </p>
                            </div>
                            <div class="flex items-center gap-3">
                                <a href="{{ route('web.category', $parent->slug) }}" class="btn btn-sm btn-primary" onclick="event.stopPropagation();">
                                    {{ __('عرض الإعلانات') }}
                                </a>
                                <button class="btn btn-sm btn-circle btn-ghost toggle-btn" data-category-id="{{ $parent->id }}">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Child Categories (Hidden by default) -->
                    <div id="children-{{ $parent->id }}" class="category-children mt-2 ml-12 rtl:mr-12 rtl:ml-0 hidden">
                        @if($parent->children && $parent->children->count() > 0)
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 border-l-2 rtl:border-r-2 rtl:border-l-0 border-primary/20 pl-4 rtl:pr-4 rtl:pl-0 py-2">
                                @foreach($parent->children as $child)
                                    <a href="{{ route('web.category', $child->slug) }}"
                                       class="subcategory-card flex items-center p-3 rounded-lg bg-base-200 hover:bg-base-300">
                                        <div class="subcategory-icon mr-3 rtl:ml-3 rtl:mr-0">
                                            @if($child->icon)
                                                <i class="{{ $child->icon }}"></i>
                                            @else
                                                <i class="fas fa-folder"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <h3 class="font-bold">{{ $child->title }}</h3>
                                            <p class="text-xs text-base-content/70">
                                                {{ $child->products_count }} {{ __('إعلان') }}
                                            </p>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4 border-l-2 rtl:border-r-2 rtl:border-l-0 border-primary/20 pl-4 rtl:pr-4 rtl:pl-0">
                                <p class="text-base-content/70">{{ __('لا توجد أقسام فرعية لهذا القسم') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Featured Categories Section -->
        <div class="bg-base-100 rounded-xl shadow-sm p-6 mb-8">
            <h2 class="text-2xl font-bold mb-6">{{ __('الأقسام المميزة') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($parentCategories->take(3) as $featuredCategory)
                    <div class="bg-base-200 rounded-xl p-5 flex items-start">
                        <div class="category-icon mr-4 rtl:ml-4 rtl:mr-0 flex-shrink-0">
                            @if($featuredCategory->icon)
                                <i class="{{ $featuredCategory->icon }}"></i>
                            @else
                                <i class="fas fa-folder"></i>
                            @endif
                        </div>
                        <div>
                            <h3 class="font-bold text-lg mb-1">{{ $featuredCategory->title }}</h3>
                            <p class="text-sm text-base-content/70 mb-3">
                                {{ $featuredCategory->total_products_count }} {{ __('إعلان') }}
                            </p>
                            <button onclick="toggleCategory('{{ $featuredCategory->id }}')"
                                   class="text-primary text-sm font-medium hover:underline">
                                {{ __('استعرض الأقسام الفرعية') }} <i class="fas fa-chevron-down ml-1 rtl:mr-1 rtl:ml-0"></i>
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</main>
@endsection

@section('page-script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the categories
    });

    function toggleCategory(categoryId) {
        // Get the children container
        const childrenContainer = document.getElementById('children-' + categoryId);
        if (!childrenContainer) return;

        // Get the toggle button
        const toggleBtn = document.querySelector(`.toggle-btn[data-category-id="${categoryId}"]`);

        // Get the parent container
        const parentContainer = childrenContainer.previousElementSibling;

        // Toggle the open class
        childrenContainer.classList.toggle('hidden');
        childrenContainer.classList.toggle('open');

        if (toggleBtn) {
            toggleBtn.classList.toggle('open');
        }

        if (parentContainer) {
            parentContainer.classList.toggle('active');
        }

        // Scroll to the category if it's not in view
        if (!childrenContainer.classList.contains('hidden')) {
            setTimeout(() => {
                parentContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);
        }

        // Close other open categories
        const allChildren = document.querySelectorAll('.category-children');
        const allToggles = document.querySelectorAll('.toggle-btn');
        const allParents = document.querySelectorAll('.category-parent');

        allChildren.forEach(container => {
            if (container.id !== 'children-' + categoryId && container.classList.contains('open')) {
                container.classList.remove('open');
                container.classList.add('hidden');
            }
        });

        allToggles.forEach(btn => {
            if (btn.dataset.categoryId !== categoryId && btn.classList.contains('open')) {
                btn.classList.remove('open');
            }
        });

        allParents.forEach(parent => {
            if (parent !== parentContainer && parent.classList.contains('active')) {
                parent.classList.remove('active');
            }
        });
    }
</script>
@endsection
