@extends('web.layouts.layout')

@section('title', 'طلب التوثيق')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="ti ti-shield-check text-2xl text-blue-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">طلب التوثيق</h1>
                <p class="text-gray-600">احصل على علامة التوثيق لزيادة الثقة في حسابك</p>
            </div>

            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            @if(session('info'))
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
                    {{ session('info') }}
                </div>
            @endif

            <form method="POST" action="{{ route('web.verification.submit') }}" enctype="multipart/form-data">
                @csrf
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع المستخدم</label>
                    <select name="user_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر نوع المستخدم</option>
                        <option value="individual">فرد</option>
                        <option value="business">شركة/متجر</option>
                        <option value="organization">مؤسسة</option>
                    </select>
                    @error('user_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                @if(!empty($requirementsData))
                    @foreach($requirementsData as $requirement)
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $requirement['title'] }}
                                @if($requirement['is_required'])
                                    <span class="text-red-500">*</span>
                                @endif
                            </label>
                            
                            @if(!empty($requirement['description']))
                                <p class="text-sm text-gray-500 mb-2">{{ $requirement['description'] }}</p>
                            @endif

                            @if($requirement['type'] === 'text')
                                <input type="text" 
                                       name="{{ $requirement['title'] }}" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       @if($requirement['is_required']) required @endif>
                            @elseif($requirement['type'] === 'textarea')
                                <textarea name="{{ $requirement['title'] }}" 
                                          rows="4"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          @if($requirement['is_required']) required @endif></textarea>
                            @elseif($requirement['type'] === 'file')
                                <input type="file" 
                                       name="{{ $requirement['title'] }}" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                       @if($requirement['is_required']) required @endif>
                                <p class="text-xs text-gray-500 mt-1">الملفات المدعومة: JPG, PNG, PDF, DOC, DOCX (حد أقصى 10 ميجابايت)</p>
                            @elseif($requirement['type'] === 'select' && !empty($requirement['options']))
                                <select name="{{ $requirement['title'] }}" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        @if($requirement['is_required']) required @endif>
                                    <option value="">اختر...</option>
                                    @foreach(explode(',', $requirement['options']) as $option)
                                        <option value="{{ trim($option) }}">{{ trim($option) }}</option>
                                    @endforeach
                                </select>
                            @endif

                            @error($requirement['title'])
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-500">لم يتم تكوين متطلبات التوثيق بعد. يرجى المحاولة لاحقاً.</p>
                    </div>
                @endif

                @if(!empty($requirementsData))
                    <div class="flex justify-between items-center pt-6 border-t">
                        <a href="{{ route('web.profile.show') }}" class="text-gray-600 hover:text-gray-800">
                            العودة للملف الشخصي
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            إرسال طلب التوثيق
                        </button>
                    </div>
                @endif
            </form>
        </div>
    </div>
</div>
@endsection
