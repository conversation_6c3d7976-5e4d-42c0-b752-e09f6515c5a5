@extends('web.layouts.layout')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-center">
            {{ __('تواصل معنا') }}
        </h1>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Contact Card: Email -->
            <div class="bg-base-200 p-6 rounded-lg shadow-lg text-center">
                <div class="flex justify-center mb-4">
                    <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-envelope text-primary text-2xl"></i>
                    </div>
                </div>
                <h3 class="font-semibold text-lg mb-2">{{ __('البريد الإلكتروني') }}</h3>
                <p class="text-base-content/80"><EMAIL></p>
            </div>

            <!-- Contact Card: Phone -->
            <div class="bg-base-200 p-6 rounded-lg shadow-lg text-center">
                <div class="flex justify-center mb-4">
                    <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-phone text-primary text-2xl"></i>
                    </div>
                </div>
                <h3 class="font-semibold text-lg mb-2">{{ __('الهاتف') }}</h3>
                <p class="text-base-content/80">+966 12 345 6789</p>
            </div>

            <!-- Contact Card: Location -->
            <div class="bg-base-200 p-6 rounded-lg shadow-lg text-center">
                <div class="flex justify-center mb-4">
                    <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-location-dot text-primary text-2xl"></i>
                    </div>
                </div>
                <h3 class="font-semibold text-lg mb-2">{{ __('العنوان') }}</h3>
                <p class="text-base-content/80">{{ __('الرياض، المملكة العربية السعودية') }}</p>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="bg-base-200 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-6 text-center">{{ __('أرسل لنا رسالة') }}</h2>

            @if (session('contact_success'))
                <div class="alert alert-success mb-6">
                    <div>
                        <i class="fa-solid fa-check-circle"></i>
                        <span>{{ session('contact_success') }}</span>
                    </div>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-error mb-6">
                    <div>
                        <i class="fa-solid fa-circle-exclamation"></i>
                        <span>{{ __('يرجى تصحيح الأخطاء أدناه') }}</span>
                    </div>
                    <ul class="mt-2 list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('web.contactUs') }}" method="POST" class="space-y-4">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('الاسم') }} *</span>
                        </label>
                        <input type="text" name="name" value="{{ old('name') }}" class="input input-bordered w-full" required>
                    </div>

                    <!-- Email -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('البريد الإلكتروني') }} *</span>
                        </label>
                        <input type="email" name="email" value="{{ old('email') }}" class="input input-bordered w-full" required>
                    </div>
                </div>

                <!-- Subject -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">{{ __('الموضوع') }} *</span>
                    </label>
                    <input type="text" name="subject" value="{{ old('subject') }}" class="input input-bordered w-full" required>
                </div>

                <!-- Message -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">{{ __('الرسالة') }} *</span>
                    </label>
                    <textarea name="message" class="textarea textarea-bordered h-32" required>{{ old('message') }}</textarea>
                </div>

                <!-- Submit Button -->
                <div class="form-control mt-6">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-solid fa-paper-plane me-2"></i>
                        {{ __('إرسال الرسالة') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
