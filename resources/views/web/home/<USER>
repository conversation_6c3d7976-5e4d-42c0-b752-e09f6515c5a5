<style>
    .category-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        transition: transform 0.2s ease;
    }

    .category-icon:hover {
        transform: scale(1.05);
    }

    .quick-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 0.5rem;
        transition: all 0.2s ease;
    }

    .quick-nav-item:hover {
        color: var(--primary);
    }

    .quick-nav-scroll {
        display: flex;
        overflow-x: auto;
        scrollbar-width: thin;
        padding-bottom: 0.5rem;
    }

    .quick-nav-scroll::-webkit-scrollbar {
        height: 4px;
    }

    .quick-nav-scroll::-webkit-scrollbar-thumb {
        background-color: var(--primary);
        border-radius: 4px;
    }

    .product-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .filter-bar {
        background-color: var(--base-200);
        border-radius: 0.5rem;
    }

    /* Mobile Filter Drawer Styles */
    #filter-drawer {
        backdrop-filter: blur(4px);
    }

    .drawer-side {
        overflow-y: auto;
        scrollbar-width: thin;
    }

    .drawer-side::-webkit-scrollbar {
        width: 4px;
    }

    .drawer-side::-webkit-scrollbar-thumb {
        background-color: var(--primary);
        border-radius: 4px;
    }

    /* Improved animation for drawer */
    @keyframes slide-in-rtl {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    @keyframes slide-in-ltr {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
    }

    /* Pulse animation for filter button */
    @keyframes pulse-border {
        0% { box-shadow: 0 0 0 0 rgba(var(--p), 0.4); }
        70% { box-shadow: 0 0 0 6px rgba(var(--p), 0); }
        100% { box-shadow: 0 0 0 0 rgba(var(--p), 0); }
    }

    #mobile-filter-btn {
        position: relative;
        animation: pulse-border 2s infinite;
    }
</style>
