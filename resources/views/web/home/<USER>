<script>
    // Global variables
    const isLoggedIn = "{{ auth()->check() ? 'true' : 'false' }}" === "true";
    const loginUrl = "{{ route('web.auth.login') }}";
    const toggleFavoriteUrl = "{{ route('web.toggleFavorite', ['productId' => '_ID_']) }}";
    const csrfToken = "{{ csrf_token() }}";

    // Handle favorite toggle
    function handleFavoriteClick(productId, button) {
        if (!isLoggedIn) {
            window.location.href = loginUrl;
            return;
        }

        fetch(toggleFavoriteUrl.replace('_ID_', productId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.isFavorite) {
                    button.innerHTML = '<i class="fas fa-heart text-primary"></i>';
                } else {
                    button.innerHTML = '<i class="far fa-heart"></i>';
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // Initialize Livewire components
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for Livewire events
        if (typeof Livewire !== 'undefined') {
            Livewire.on('toggleFavorite', function(productId) {
                if (!isLoggedIn) {
                    window.location.href = loginUrl;
                    return;
                }

                fetch(toggleFavoriteUrl.replace('_ID_', productId), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Refresh the Livewire component to update the UI
                        Livewire.emit('refreshProducts');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Mobile filter drawer
        const mobileFilterBtn = document.getElementById('mobile-filter-btn');
        const closeFilterBtn = document.getElementById('close-filter-btn');
        const filterDrawer = document.getElementById('filter-drawer');
        const drawerSide = filterDrawer.querySelector('.drawer-side');
        const isRtl = document.documentElement.dir === 'rtl' || document.documentElement.lang === 'ar';

        // Prevent body scrolling when drawer is open
        function toggleBodyScroll(isOpen) {
            if (isOpen) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function openDrawer() {
            // Show the overlay
            filterDrawer.classList.remove('hidden');

            // Trigger reflow to enable transitions
            filterDrawer.offsetWidth;

            // Make overlay visible
            filterDrawer.classList.add('opacity-100');

            // Animate the drawer in
            setTimeout(() => {
                if (isRtl) {
                    drawerSide.classList.remove('translate-x-full');
                } else {
                    drawerSide.classList.remove('-translate-x-full');
                }
                drawerSide.classList.add('translate-x-0');
                toggleBodyScroll(true);
            }, 10);
        }

        function closeDrawer() {
            // Animate the drawer out
            drawerSide.classList.remove('translate-x-0');
            if (isRtl) {
                drawerSide.classList.add('translate-x-full');
            } else {
                drawerSide.classList.add('-translate-x-full');
            }

            // Fade out overlay
            filterDrawer.classList.remove('opacity-100');

            // Hide the overlay after animation completes
            setTimeout(() => {
                filterDrawer.classList.add('hidden');
                toggleBodyScroll(false);
            }, 300);
        }

        // Add event listeners
        mobileFilterBtn.addEventListener('click', openDrawer);
        closeFilterBtn.addEventListener('click', closeDrawer);

        // Close drawer when clicking outside
        filterDrawer.addEventListener('click', function(e) {
            if (e.target === filterDrawer) {
                closeDrawer();
            }
        });

        // Handle touch swipe to close drawer
        let touchStartX = 0;
        let touchEndX = 0;

        drawerSide.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        }, false);

        drawerSide.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, false);

        function handleSwipe() {
            const swipeThreshold = 100;
            if (isRtl) {
                // For RTL: swipe left to close
                if (touchEndX - touchStartX > swipeThreshold) {
                    closeDrawer();
                }
            } else {
                // For LTR: swipe right to close
                if (touchStartX - touchEndX > swipeThreshold) {
                    closeDrawer();
                }
            }
        }

        // Handle escape key to close drawer
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !filterDrawer.classList.contains('hidden')) {
                closeDrawer();
            }
        });

        // Mobile location select
        const mobileLocationSelect = document.getElementById('mobile-location-select');
        const searchUrl = "{{ route('web.search') }}";
        const indexUrl = "{{ route('web.index') }}";

        mobileLocationSelect.addEventListener('change', function() {
            if (this.value) {
                window.location.href = searchUrl + "?location_id=" + this.value;
            } else {
                window.location.href = indexUrl;
            }
        });
    });
</script>
