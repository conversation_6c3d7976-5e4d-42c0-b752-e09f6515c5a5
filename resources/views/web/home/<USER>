<aside class="">
    <div class="sticky top-20 space-y-4  ">
        <!-- Quick Navigation -->
        <div class="bg-base-200 rounded-lg p-4 shadow-sm">
            <h3 class="font-bold mb-3 border-b pb-2">{{ __('تنقل سريع') }}</h3>
            <div class="space-y-2">
                @php
                    $mainCategories = \App\Models\Category::whereNull('parent_id')
                        ->orderBy('order')
                        ->limit(8)
                        ->get();
                @endphp
                @foreach($mainCategories as $category)
                <a href="{{ route('web.category', $category->slug) }}" class="flex items-center p-2 hover:bg-base-300 rounded-lg transition-colors">
                    <div class="w-8 h-8 flex items-center justify-center">
                        @if($category->icon)
                            <i class="{{ $category->icon }} text-primary"></i>
                        @else
                            <i class="fas fa-folder text-primary"></i>
                        @endif
                    </div>
                    <span class="ms-2 text-sm">{{ $category->title }}</span>
                    <span class="ms-auto text-xs text-base-content/70">{{ $category->products_count ?? 0 }}</span>
                </a>
                @endforeach
                <a href="{{ route('web.categories') }}" class="flex justify-center items-center p-2 text-primary text-sm hover:underline mt-2">
                    {{ __('عرض كل الأقسام') }} <i class="fas fa-chevron-left ms-1 rtl:rotate-180"></i>
                </a>
            </div>
        </div>

        <!-- Car Brands -->
        <div class="bg-base-200 rounded-lg p-4 shadow-sm">
            <h3 class="font-bold mb-3 border-b pb-2">{{ __('ماركات سيارات') }}</h3>
            <div class="grid grid-cols-2 gap-2">
                @php
                    $carBrands = [
                        ['name' => 'تويوتا', 'icon' => 'fa-car'],
                        ['name' => 'نيسان', 'icon' => 'fa-car'],
                        ['name' => 'فورد', 'icon' => 'fa-car'],
                        ['name' => 'مرسيدس', 'icon' => 'fa-car'],
                        ['name' => 'لكزس', 'icon' => 'fa-car'],
                        ['name' => 'هوندا', 'icon' => 'fa-car'],
                    ];
                @endphp
                @foreach($carBrands as $brand)
                <a href="{{ route('web.search') }}?q={{ $brand['name'] }}" class="flex flex-col items-center p-2 hover:bg-base-300 rounded-lg transition-colors text-center">
                    <div class="w-8 h-8 flex items-center justify-center mb-1">
                        <i class="fas {{ $brand['icon'] }} text-primary"></i>
                    </div>
                    <span class="text-sm">{{ $brand['name'] }}</span>
                </a>
                @endforeach
            </div>
        </div>

        <!-- Popular Locations -->
        <div class="bg-base-200 rounded-lg p-4 shadow-sm">
            <h3 class="font-bold mb-3 border-b pb-2">{{ __('المناطق الشائعة') }}</h3>
            <div class="space-y-2">
                @php
                    $popularLocations = \App\Models\Location::orderBy('name')
                        ->limit(6)
                        ->get();
                @endphp
                @foreach($popularLocations as $location)
                <a href="{{ route('web.search') }}?location_id={{ $location->id }}" class="flex items-center p-2 hover:bg-base-300 rounded-lg transition-colors">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-primary"></i>
                    </div>
                    <span class="ms-2 text-sm">{{ $location->name }}</span>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</aside>
