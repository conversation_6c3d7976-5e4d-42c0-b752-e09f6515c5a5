<div id="filter-drawer" class="fixed inset-0 bg-base-300 bg-opacity-50 backdrop-blur-sm z-50 hidden transition-opacity duration-300 ease-in-out opacity-0">
    <div class="drawer-side fixed top-0 bottom-0 w-4/5 max-w-sm bg-base-100 shadow-xl rtl:right-0 ltr:left-0 transform transition-transform duration-300 ease-in-out rtl:translate-x-full ltr:-translate-x-full h-full">
        <div class="p-4 h-full flex flex-col">
            <div class="flex justify-between items-center mb-4 border-b pb-3">
                <h3 class="text-lg font-bold flex items-center">
                    <i class="fas fa-filter text-primary mr-2 rtl:ml-2 rtl:mr-0"></i>
                    {{ __('خيارات التصفية') }}
                </h3>
                <button id="close-filter-btn" class="btn btn-sm btn-circle btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="overflow-y-auto flex-grow">
                <div class="mb-6">
                    <h4 class="font-medium mb-3 flex items-center">
                        <i class="fas fa-map-marker-alt text-primary mr-2 rtl:ml-2 rtl:mr-0"></i>
                        {{ __('المنطقة') }}
                    </h4>
                    <select id="mobile-location-select" class="select select-bordered w-full">
                        <option value="">{{ __('كل المناطق') }}</option>
                        @if(isset($locations))
                            @foreach($locations as $location)
                                <option value="{{ $location->id }}">{{ $location->name }}</option>
                            @endforeach
                        @else
                            @php
                                $locations = \App\Models\Location::orderBy('name')->get();
                            @endphp
                            @foreach($locations as $location)
                                <option value="{{ $location->id }}">{{ $location->name }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-3 flex items-center">
                        <i class="fas fa-th-large text-primary mr-2 rtl:ml-2 rtl:mr-0"></i>
                        {{ __('الأقسام') }}
                    </h4>
                    <div class="flex flex-col gap-2 max-h-60 overflow-y-auto pr-1">
                        @php
                            $mainCategories = \App\Models\Category::whereNull('parent_id')
                                ->orderBy('order')
                                ->limit(10)
                                ->get();
                        @endphp
                        @foreach($mainCategories as $category)
                            <a href="{{ route('web.category', $category->slug) }}" class="flex items-center p-2 hover:bg-base-200 rounded-lg">
                                @if($category->icon)
                                    <i class="{{ $category->icon }} text-primary w-6 text-center"></i>
                                @else
                                    <i class="fas fa-folder text-primary w-6 text-center"></i>
                                @endif
                                <span class="ms-2">{{ $category->title }}</span>
                            </a>
                        @endforeach
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-3 flex items-center">
                        <i class="fas fa-sort-amount-down text-primary mr-2 rtl:ml-2 rtl:mr-0"></i>
                        {{ __('ترتيب حسب') }}
                    </h4>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="{{ route('web.search') }}?sort=newest" class="btn btn-sm btn-outline">{{ __('الأحدث') }}</a>
                        <a href="{{ route('web.search') }}?sort=oldest" class="btn btn-sm btn-outline">{{ __('الأقدم') }}</a>
                        <a href="{{ route('web.search') }}?sort=price_high" class="btn btn-sm btn-outline">{{ __('السعر: الأعلى') }}</a>
                        <a href="{{ route('web.search') }}?sort=price_low" class="btn btn-sm btn-outline">{{ __('السعر: الأقل') }}</a>
                    </div>
                </div>
            </div>

            <div class="mt-auto pt-4 border-t">
                <a href="{{ route('web.search') }}" class="btn btn-primary w-full">
                    <i class="fas fa-search mr-2 rtl:ml-2 rtl:mr-0"></i>
                    {{ __('البحث المتقدم') }}
                </a>
            </div>
        </div>
    </div>
</div>
