@extends('web.layouts.layout')
@section('title', 'الرئيسية')

@section('page-meta')
<meta name="description" content="حراجي - سوق البيع والشراء الإلكتروني، اعرض منتجاتك أو ابحث عن ما تريد شراءه بكل سهولة">
<meta name="keywords" content="حراجي, سوق, بيع, شراء, إعلانات مبوبة, منتجات مستعملة, منتجات جديدة">
@endsection

@section('page-style')
    @include('web.home.styles')
@endsection

@section('content')

    <!-- Filter Bar -->

    <!-- Mobile Filter Drawer -->
    @include('web.home.mobile-filter')

    <!-- Main Content -->
    <main >
    <div class="container mx-auto px-4 py-3">
    <!-- Main Content with Sidebar -->
    <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar/Aside -->
            <div class="order-2 lg:order-1 hidden lg:block">
                @include('web.home.sidebar')
            </div>

                <!-- Main Content Area -->
                <div class="lg:w-3/4 order-1 lg:order-2">
                
                    <!-- Products will be loaded via Livewire component -->
                    @livewire('products')
                </div>
            </div>

            <!-- Banner Ads -->
            @include('web.home.banner-ads')

            <!-- Download App Section -->
            @include('web.home.download-app')
        </div>

        <!-- Mobile Footer -->
        @include('web.home.mobile-footer')
    </main>
@endsection

@section('page-script')
    @include('web.home.scripts')
@endsection
