<!DOCTYPE html>
<html lang="{{ session()->get('locale') ?? 'ar' }}" dir="{{ session()->get('locale') == 'en' ? 'ltr' : 'rtl' }}" class="{{ session()->get('theme') == 'dark' ? 'dark' : '' }}" data-theme="{{ session()->get('theme') == 'dark' ? 'haraji-dark' : 'haraji-light' }}">

@include('web.layouts.header')

<body class="bg-base-100 min-h-screen flex flex-col transition-colors duration-200" x-data="{ showSearchModal: false, showSidebarModal: false, showMobileMenu: false }" dir="{{ session()->get('locale') == 'en' ? 'ltr' : 'rtl' }}">
    @include('web.layouts.navbar')

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success mx-auto m-4 w-full max-w-1/2">
            <div>
                <i class="fa-solid fa-check-circle"></i>
                <span>{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-error mx-auto m-4 w-full max-w-1/2">
            <div>
                <i class="fa-solid fa-circle-exclamation"></i>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    @yield('content')
</body>

@include('web.layouts.footer')

@include('web.layouts.scripts')

</html>
