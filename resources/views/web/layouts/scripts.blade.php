
<script>
    // Theme Toggle
    document.addEventListener('DOMContentLoaded', function() {
        initThemeToggle();
        setupMobileMenu();
    });

    function initThemeToggle() {
        const html = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');
        const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
        const currentTheme = localStorage.getItem('theme') || '{{ session()->get('theme') }}' || 'light';

        // Apply theme on page load
        applyTheme(currentTheme);

        // Desktop theme toggle
        themeToggle?.addEventListener('click', function() {
            const newTheme = html.getAttribute('data-theme') === 'haraji-light' ? 'dark' : 'light';
            applyTheme(newTheme);
            saveThemePreference(newTheme);
        });

        // Mobile theme toggle
        mobileThemeToggle?.addEventListener('click', function() {
            const newTheme = html.getAttribute('data-theme') === 'haraji-light' ? 'dark' : 'light';
            applyTheme(newTheme);
            saveThemePreference(newTheme);
        });
    }

    function applyTheme(theme) {
        const html = document.documentElement;

        // Set data-theme attribute for DaisyUI themes
        html.setAttribute('data-theme', 'haraji-' + theme);

        // Add/remove dark class for Tailwind dark mode
        if (theme === 'dark') {
            html.classList.add('dark');
            updateThemeIcons('dark');
        } else {
            html.classList.remove('dark');
            updateThemeIcons('light');
        }
    }

    function updateThemeIcons(theme) {
        // Update desktop theme icon
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun text-xl text-white' : 'fas fa-moon text-xl text-white';
            }
        }

        // Update mobile theme icon
        const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
        if (mobileThemeToggle) {
            const icon = mobileThemeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun mr-2 rtl:ml-2 rtl:mr-0 text-white' : 'fas fa-moon mr-2 rtl:ml-2 rtl:mr-0 text-white';
            }
            mobileThemeToggle.querySelector('span') ? mobileThemeToggle.querySelector('span').textContent = theme === 'dark' ? '{{ __("الوضع النهاري") }}' : '{{ __("الوضع الليلي") }}' : null;
        }
    }

    function saveThemePreference(theme) {
        // Save to localStorage
        localStorage.setItem('theme', theme);

        // Send to server to save in session
        fetch('{{ url("/web/theme") }}/' + theme, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).catch(error => console.error('Error saving theme preference:', error));
    }

    function setupMobileMenu() {
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.querySelector('[x-show="showMobileMenu"]');
            const mobileMenuButton = document.querySelector('[x-data] [\\@click="showMobileMenu = !showMobileMenu"]');

            if (mobileMenu && !mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target)) {
                // Only if Alpine.js is available and the menu is open
                if (window.Alpine && mobileMenu.style.display !== 'none') {
                    Alpine.store('showMobileMenu', false);
                }
            }
        });
    }

    // Additional global scripts can be added here
</script>

@yield('page-script')
@stack('scripts')