<header class="bg-primary sticky top-0 z-50 shadow-md">
    <div class="py-3">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap items-center justify-between">
                <!-- Mobile Menu Toggle Button -->
                <button @click="showMobileMenu = !showMobileMenu" class="lg:hidden btn btn-ghost btn-circle">
                    <i class="fas fa-bars text-xl text-white"></i>
                </button>

                <!-- LOGO -->
                <div class="w-auto mb-0 flex items-center space-x-2 rtl:space-x-reverse">
                    <a href="{{ route('web.index') }}" class="flex items-center">
                        <img src="{{ asset('assets/img/logo.png') }}" alt="حراجي" class="img-fluid h-10" />
                        <span class="text-2xl font-bold text-white mr-2 mt-2" style="">حراجي</span>
                    </a>
                </div>

                <!-- Search Bar (Desktop) -->
                <div class="hidden lg:flex flex-grow mx-4 max-w-2xl">
                    <form action="{{ route('web.search') }}" method="GET" class="w-full">
                        <div class="relative w-full">
                            <input type="text" name="q" placeholder="{{ __('بحث عن منتجات...') }}" class="input input-bordered w-full pr-10 rtl:pr-4 rtl:pl-10" />
                            <button type="submit" class="absolute inset-y-0 right-0 rtl:right-auto rtl:left-0 flex items-center px-3">
                                <i class="fas fa-search text-gray-400"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Language Switcher -->
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-ghost btn-circle">
                            <i class="fas fa-globe text-xl text-white"></i>
                        </label>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a href="{{ route('web.setLocale', 'ar') }}" class="{{ session()->get('locale') == 'ar' ? 'active' : '' }}">العربية</a></li>
                            <li><a href="{{ route('web.setLocale', 'en') }}" class="{{ session()->get('locale') == 'en' ? 'active' : '' }}">English</a></li>
                        </ul>
                    </div>

                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="btn btn-ghost btn-circle">
                        <i class="fas {{ session()->get('theme') == 'dark' ? 'fa-sun' : 'fa-moon' }} text-white text-xl"></i>
                    </button>

                    @auth
                    <!-- Chat Button -->
                    <a href="{{ route('web.chat.index') }}" class="btn btn-ghost btn-circle">
                        <div class="indicator">
                            <i class="fas fa-comments text-xl text-white"></i>
                            @php
                                $unreadChatsCount = \Modules\Chat\app\Models\Chat::where(function($query) {
                                    $query->where('sender_id', auth()->id())
                                        ->orWhere('receiver_id', auth()->id());
                                })
                                ->whereHas('messages', function($query) {
                                    $query->where('user_id', '!=', auth()->id())
                                        ->where('is_read', false);
                                })
                                ->count();
                            @endphp
                            @if($unreadChatsCount > 0)
                                <span class="indicator-item badge badge-white badge-sm">{{ $unreadChatsCount }}</span>
                            @endif
                        </div>
                    </a>
                    @endauth

                    <!-- Add Listing Button -->
                    @auth
                        @if(auth()->user()->hasVerifiedEmail())
                            <a href="{{ route('web.products.create') }}" class="btn btn-white">
                                <i class="fas fa-plus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                            </a>
                        @else
                            <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-white">
                                <i class="fas fa-plus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                            </a>
                        @endif
                    @else
                        <a href="{{ route('web.auth.login') }}" class="btn btn-white">
                            <i class="fas fa-plus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                        </a>
                    @endauth

                    <!-- User Menu -->
                    @auth
                        <div class="dropdown dropdown-end mx-2">
                            <label tabindex="0" class="btn btn-ghost btn-circle avatar">
                                <div class="w-10 rounded-full">
                                    @if(auth()->user()->sessions->count() > 0 && auth()->user()->sessions->first()->avatar)
                                        <img src="auth()->user()->avatar()" alt="{{ auth()->user()->name }}" />
                                    @else
                                        <img src="{{ asset('assets/img/avatar.png') }}" alt="{{ auth()->user()->name }}" />
                                    @endif
                                </div>
                            </label>
                            <ul tabindex="0" class="dropdown-content z-[1] menu p-2  shadow bg-base-100 rounded-box w-52">
                                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                                <li><a href="{{ route('web.profile.products') }}">{{ __('إعلاناتي') }}</a></li>
                                <li><a href="{{ route('web.favorites') }}">{{ __('المفضلة') }}</a></li>
                                <li>
                                    <a href="{{ route('web.chat.index') }}">
                                        {{ __('المحادثات') }}
                                        @php
                                            $unreadChatsCount = \Modules\Chat\app\Models\Chat::where(function($query) {
                                                $query->where('sender_id', auth()->id())
                                                    ->orWhere('receiver_id', auth()->id());
                                            })
                                            ->whereHas('messages', function($query) {
                                                $query->where('user_id', '!=', auth()->id())
                                                    ->where('is_read', false);
                                            })
                                            ->count();
                                        @endphp
                                        @if($unreadChatsCount > 0)
                                            <span class="badge badge-primary badge-sm">{{ $unreadChatsCount }}</span>
                                        @endif
                                    </a>
                                </li>
                                @if(!auth()->user()->hasVerifiedEmail())
                                    <li>
                                        <a href="{{ route('web.auth.verification.notice') }}" class="text-warning">
                                            <i class="fas fa-exclamation-triangle mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('تحقق من البريد') }}
                                        </a>
                                    </li>
                                @endif
                                <li>
                                    <form method="POST" action="{{ route('web.auth.logout') }}">
                                        @csrf
                                        <button type="submit" class="w-full text-right rtl:text-left">{{ __('تسجيل الخروج') }}</button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @else
                        <a href="{{ route('web.auth.login') }}" class="btn btn-ghost text-white">{{ __('تسجيل الدخول') }}</a>
                        <a href="{{ route('web.auth.register') }}" class="btn btn-ghost text-white">{{ __('التسجيل') }}</a>
                    @endauth
                </div>

                <!-- Mobile Search Toggle -->
                <button @click="showSearchModal = !showSearchModal" class="lg:hidden btn btn-ghost btn-circle">
                    <i class="fas fa-search text-xl text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Categories Navigation -->
    <div class="bg-base-200 py-2 hidden lg:block">
        <div class="container mx-auto px-4">
            <ul class="flex flex-wrap items-center space-x-6 rtl:space-x-reverse">
                <li>
                    <a href="{{ route('web.categories') }}" class="text-base-content hover:text-primary font-medium">
                        <i class="fas fa-th-large mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('جميع الأقسام') }}
                    </a>
                </li>
                @php
                    $featuredCategories = \App\Models\Category::whereNull('parent_id')
                        ->orderBy('order')
                        ->limit(6)
                        ->get();
                @endphp
                @foreach($featuredCategories as $category)
                    <li>
                        <a href="{{ route('web.category', $category->slug) }}" class="text-base-content hover:text-primary">
                            @if($category->icon)
                                <i class="{{ $category->icon }} mr-1 rtl:ml-1 rtl:mr-0"></i>
                            @endif
                            {{ $category->title }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>

    <!-- Mobile Search Modal -->
    <div x-show="showSearchModal" x-cloak class="fixed inset-0 z-50 flex flex-col p-4 lg:hidden bg-base-100">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">{{ __('بحث') }}</h3>
            <button @click="showSearchModal = false" class="btn btn-ghost btn-circle">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form action="{{ route('web.search') }}" method="GET">
            <div class="relative w-full">
                <input type="text" name="q" placeholder="{{ __('بحث عن منتجات...') }}" class="input input-bordered w-full pr-10 rtl:pr-4 rtl:pl-10" />
                <button type="submit" class="absolute inset-y-0 right-0 rtl:right-auto rtl:left-0 flex items-center px-3">
                    <i class="fas fa-search text-gray-400"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Mobile Menu Modal -->
    <div x-show="showMobileMenu" x-cloak class="fixed inset-0 z-50 flex flex-col p-4 lg:hidden bg-base-100">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">{{ __('القائمة') }}</h3>
            <button @click="showMobileMenu = false" class="btn btn-ghost btn-circle">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="flex flex-col space-y-4">
            <a href="{{ route('web.index') }}" class="btn btn-ghost justify-start">
                <i class="fas fa-home mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('الرئيسية') }}
            </a>
            <a href="{{ route('web.categories') }}" class="btn btn-ghost justify-start">
                <i class="fas fa-th-large mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('الأقسام') }}
            </a>
            @auth
                @if(auth()->user()->hasVerifiedEmail())
                    <a href="{{ route('web.products.create') }}" class="btn btn-primary justify-start">
                        <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                    </a>
                @else
                    <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-primary justify-start">
                        <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                    </a>
                @endif
            @else
                <a href="{{ route('web.auth.login') }}" class="btn btn-primary justify-start">
                    <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان') }}
                </a>
            @endauth
            <a href="{{ route('web.favorites') }}" class="btn btn-ghost justify-start">
                <i class="fas fa-heart mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('المفضلة') }}
            </a>

            @auth
            <a href="{{ route('web.chat.index') }}" class="btn btn-ghost justify-start">
                <i class="fas fa-comments mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('المحادثات') }}
                @php
                    $unreadChatsCount = \Modules\Chat\app\Models\Chat::where(function($query) {
                        $query->where('sender_id', auth()->id())
                            ->orWhere('receiver_id', auth()->id());
                    })
                    ->whereHas('messages', function($query) {
                        $query->where('user_id', '!=', auth()->id())
                            ->where('is_read', false);
                    })
                    ->count();
                @endphp
                @if($unreadChatsCount > 0)
                    <span class="badge badge-primary ml-2 rtl:mr-2 rtl:ml-0">{{ $unreadChatsCount }}</span>
                @endif
            </a>
            @endauth

            <div class="divider"></div>

            <!-- Language Switcher -->
            <div class="flex space-x-2 rtl:space-x-reverse">
                <a href="{{ route('web.setLocale', 'ar') }}" class="btn {{ session()->get('locale') == 'ar' ? 'btn-primary' : 'btn-ghost' }} flex-1">العربية</a>
                <a href="{{ route('web.setLocale', 'en') }}" class="btn {{ session()->get('locale') == 'en' ? 'btn-primary' : 'btn-ghost' }} flex-1">English</a>
            </div>

            <!-- Theme Toggle -->
            <button id="mobile-theme-toggle" class="btn btn-ghost justify-start">
                <i class="fas {{ session()->get('theme') == 'dark' ? 'fa-sun' : 'fa-moon' }}text-white mr-2 rtl:ml-2 rtl:mr-0"></i>
                {{ session()->get('theme') == 'dark' ? __('الوضع النهاري') : __('الوضع الليلي') }}
            </button>

            <div class="divider"></div>

            @auth
                <a href="{{ route('web.profile.show') }}" class="btn btn-ghost justify-start">
                    <i class="fas fa-user mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('الملف الشخصي') }}
                </a>
                <a href="{{ route('web.profile.products') }}" class="btn btn-ghost justify-start">
                    <i class="fas fa-list mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إعلاناتي') }}
                </a>
                @if(!auth()->user()->hasVerifiedEmail())
                    <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning justify-start">
                        <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تحقق من البريد') }}
                    </a>
                @endif
                <form method="POST" action="{{ route('web.auth.logout') }}">
                    @csrf
                    <button type="submit" class="btn btn-ghost justify-start w-full">
                        <i class="fas fa-sign-out-alt mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تسجيل الخروج') }}
                    </button>
                </form>
            @else
                <a href="{{ route('web.auth.login') }}" class="btn btn-ghost justify-start">
                    <i class="fas fa-sign-in-alt mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تسجيل الدخول') }}
                </a>
                <a href="{{ route('web.auth.register') }}" class="btn btn-ghost justify-start">
                    <i class="fas fa-user-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('التسجيل') }}
                </a>
            @endauth
        </div>
    </div>
</header>
