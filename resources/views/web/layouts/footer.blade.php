<!-- Footer -->
<footer class="footer font-cairo w-full bg-base-200 pt-10 pb-6">
    <div class="container mx-auto px-4">
        <!-- Main Footer Content -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <!-- Column 1: About & Logo -->
            <div class="flex flex-col space-y-4">
                <div class="flex items-center mb-4">
                    <a href="{{ route('web.index') }}" class="flex items-center">
                        <img src="{{ asset('assets/img/logo.png') }}" alt="حراجي" class="h-10" />
                        <span class="text-2xl font-bold text-primary mr-2 mt-2" style="">حراجي</span>
                    </a>
                </div>
                <p class="text-sm opacity-80 mb-4">منصة حراجي للبيع والشراء، اعرض منتجاتك أو ابحث عن ما تريد شراءه بكل سهولة</p>

                <!-- Social Media Icons -->
                <div class="flex gap-4 mt-2">
                    <a href="https://t.me/haraji" class="btn btn-circle btn-sm btn-ghost hover:bg-primary hover:text-white transition-colors" target="_blank" aria-label="Telegram">
                        <i class="fab fa-telegram-plane text-lg"></i>
                        @if(isset($social['telegram']))
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-[10px] rounded-full px-1.5 py-0.5 shadow-md border border-white">{{ number_format($social['telegram']) }}</span>
                        @endif
                    </a>
                    <a href="" class="btn btn-circle btn-sm btn-ghost hover:bg-red-500 hover:text-white transition-colors" target="_blank" aria-label="YouTube">
                        <i class="fab fa-youtube text-lg"></i>
                        @if(isset($social['youtube']))
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-[10px] rounded-full px-1.5 py-0.5 shadow-md border border-white">{{ number_format($social['youtube']) }}</span>
                        @endif
                    </a>
                    <a href="#" class="btn btn-circle btn-sm btn-ghost hover:bg-blue-500 hover:text-white transition-colors" target="_blank" aria-label="Twitter">
                        <i class="fab fa-twitter text-lg"></i>
                    </a>
                    <a href="#" class="btn btn-circle btn-sm btn-ghost hover:bg-blue-600 hover:text-white transition-colors" target="_blank" aria-label="Facebook">
                        <i class="fab fa-facebook-f text-lg"></i>
                    </a>
                    <a href="#" class="btn btn-circle btn-sm btn-ghost hover:bg-gradient-to-r from-purple-500 to-pink-500 hover:text-white transition-colors" target="_blank" aria-label="Instagram">
                        <i class="fab fa-instagram text-lg"></i>
                    </a>
                </div>
            </div>

            <!-- Column 2: Quick Links -->
            <div class="flex flex-col space-y-2">
                <h3 class="text-lg font-bold mb-4 text-primary">روابط سريعة</h3>
                <a href="{{ route('web.index') }}" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="{{ route('web.categories') }}" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>جميع الأقسام</span>
                </a>
                <a href="{{ url('/about') }}" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>عن الموقع</span>
                </a>
                <a href="{{ url('/contact') }}" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>اتصل بنا</span>
                </a>
                <a href="{{ url('/terms-of-service') }}" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>الشروط والأحكام</span>
                </a>
                <a href="#" class="text-sm hover:text-primary transition-colors flex items-center">
                    <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                    <span>الأسئلة الشائعة</span>
                </a>
            </div>

            <!-- Column 3: Popular Categories -->
            <div class="flex flex-col space-y-2">
                <h3 class="text-lg font-bold mb-4 text-primary">الأقسام الشائعة</h3>
                @php
                    $featuredCategories = \App\Models\Category::whereNull('parent_id')
                        ->orderBy('order')
                        ->limit(6)
                        ->get();
                @endphp
                @foreach($featuredCategories as $category)
                    <a href="{{ route('web.category', $category->slug) }}" class="text-sm hover:text-primary transition-colors flex items-center">
                        <i class="fas fa-chevron-{{ session()->get('locale') == 'en' ? 'right' : 'left' }} ml-2 text-xs"></i>
                        @if($category->icon)
                            <i class="{{ $category->icon }} ml-1"></i>
                        @endif
                        <span>{{ $category->title }}</span>
                    </a>
                @endforeach
            </div>

            <!-- Column 4: Newsletter & App Downloads -->
            <div class="flex flex-col space-y-4">
                <h3 class="text-lg font-bold mb-2 text-primary">النشرة البريدية</h3>
                <p class="text-sm opacity-80 mb-2">اشترك في النشرة البريدية للحصول على آخر العروض والتحديثات</p>

                <form action="{{ route('web.newsletter.subscribe') }}" method="POST" class="newsletter-form">
                    @csrf
                    <div class="form-control">
                        <div class="join">
                            <input type="email" name="email" placeholder="البريد الإلكتروني" class="input input-bordered join-item w-full" required />
                            <button type="submit" class="btn btn-primary join-item">اشتراك</button>
                        </div>
                    </div>
                    @if(session('newsletter_success'))
                        <div class="text-success text-xs mt-2">{{ session('newsletter_success') }}</div>
                    @endif
                    @if(session('newsletter_error'))
                        <div class="text-error text-xs mt-2">{{ session('newsletter_error') }}</div>
                    @endif
                    @error('email')
                        <div class="text-error text-xs mt-2">{{ $message }}</div>
                    @enderror
                </form>

                <h3 class="text-lg font-bold mt-4 mb-2 text-primary">حمل التطبيق</h3>
                <div class="flex flex-wrap gap-2">
                    <a href="https://play.google.com/store/apps/details?id=com.haraji.app" class="btn btn-outline btn-sm gap-2" target="_blank">
                        <i class="fab fa-google-play text-lg"></i>
                        <span>Google Play</span>
                    </a>
                    <a href="https://apps.apple.com/app/" class="btn btn-outline btn-sm gap-2" target="_blank">
                        <i class="fab fa-apple text-lg"></i>
                        <span>App Store</span>
                    </a>
                    <a href="https://appgallery.huawei.com/#/app/" class="btn btn-outline btn-sm gap-2" target="_blank">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" class="w-5 h-5">
                            <path fill="currentColor" d="M23.05 25.8a.19.19 0 0 0-.11.08.12.12 0 0 0 .06.12 24.74 24.74 0 0 0 4.84 2.87 2.57 2.57 0 0 0 .53.08h.19c.87 0 2.37-.48 3.74-2.83zM34.73 18s-7.83 4.21-11.89 6.94a.11.11 0 0 0 0 .13s0 .07.09.07h7.08a5.54 5.54 0 0 0 1.59-.37 5.42 5.42 0 0 0 3-2.84 5.53 5.53 0 0 0 .4-2.27v-.05a5 5 0 0 0-.27-1.61zm-4.32-7.68a68.63 68.63 0 0 0-8.34 13.51s0 .09 0 .14h.05c1.32-.65 6.81-3.45 9.09-5.67a4.46 4.46 0 0 0 1.52-3c.17-2.59-2.43-5-2.43-5zM22.2 6.34s-2.74 3.52-1.41 16.79a.11.11 0 0 0 .21.05c.88-1.28 4.87-7.19 5.68-10.8a6.13 6.13 0 0 0 0-2.92 4.28 4.28 0 0 0-3.05-2.84s-.71-.18-1.45-.29zM7.72 26.12c1.37 2.35 2.87 2.8 3.74 2.83h.19a2.57 2.57 0 0 0 .53-.08A24.74 24.74 0 0 0 17 26a.12.12 0 0 0 0-.12.19.19 0 0 0 0-.08zM5.27 18A5 5 0 0 0 5 19.61v.05a5.53 5.53 0 0 0 .47 2.23 5.42 5.42 0 0 0 3 2.84 5.54 5.54 0 0 0 1.53.37h7.08c.06 0 .09-.07.09-.07a.11.11 0 0 0 0-.13C13.1 22.18 5.27 18 5.27 18zm4.32-7.68s-2.6 2.39-2.43 5a4.46 4.46 0 0 0 1.52 3c2.28 2.22 7.77 5 9.09 5.67h.05c.07 0 0-.14 0-.14a68.63 68.63 0 0 0-8.23-13.53zm8.21-3.98c-.74.11-1.45.29-1.45.29a4.28 4.28 0 0 0-3.05 2.84 6.13 6.13 0 0 0 0 2.92c.81 3.61 4.8 9.52 5.68 10.8a.11.11 0 0 0 .21-.05c1.35-13.28-1.39-16.8-1.39-16.8z"/>
                        </svg>
                        <span>App Gallery</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Divider -->
        <div class="divider my-2"></div>

        <!-- Bottom Footer -->
        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
            <div class="text-sm opacity-75">
                جميع الحقوق محفوظة © {{ date('Y') }} <span class="font-bold text-primary">حراجي</span>
            </div>

            <div class="flex items-center gap-4">
                <a href="#" class="text-sm hover:text-primary transition-colors">سياسة الخصوصية</a>
                <span class="hidden md:inline">|</span>
                <a href="{{ url('/terms-of-service') }}" class="text-sm hover:text-primary transition-colors">الشروط والأحكام</a>
                <span class="hidden md:inline">|</span>
                <a href="#" class="text-sm hover:text-primary transition-colors">خريطة الموقع</a>
            </div>

        </div>
    </div>
</footer>
