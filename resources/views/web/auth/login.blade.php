@extends('web.layouts.layout')
@section('title', __('تسجيل الدخول'))

@section('page-meta')
<meta name="description" content="{{ __('تسجيل الدخول إلى حسابك في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, تسجيل الدخول, حساب') }}">
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-base-100 rounded-lg shadow-md p-8">
            <h1 class="text-2xl font-bold text-center mb-6">{{ __('تسجيل الدخول') }}</h1>
            
            @if(session('status'))
                <div class="alert alert-success mb-6">
                    {{ session('status') }}
                </div>
            @endif
            
            @if($errors->any())
                <div class="alert alert-error mb-6">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <form action="{{ route('web.auth.login') }}" method="POST">
                @csrf
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('البريد الإلكتروني') }}</span>
                    </label>
                    <input type="email" name="email" value="{{ old('email') }}" class="input input-bordered" required autofocus />
                </div>
                
                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text">{{ __('كلمة المرور') }}</span>
                    </label>
                    <input type="password" name="password" class="input input-bordered" required />
                    <label class="label">
                        <a href="{{ route('web.auth.forgot-password') }}" class="label-text-alt link link-hover">{{ __('نسيت كلمة المرور؟') }}</a>
                    </label>
                </div>
                
                <div class="flex items-center mb-6">
                    <input type="checkbox" name="remember" id="remember" class="checkbox checkbox-primary" />
                    <label for="remember" class="mr-2 rtl:ml-2 rtl:mr-2">{{ __('تذكرني') }}</label>
                </div>
                
                <div class="form-control">
                    <button type="submit" class="btn btn-primary">{{ __('تسجيل الدخول') }}</button>
                </div>
            </form>
            
            <div class="divider my-6">{{ __('أو') }}</div>
            
            <p class="text-center">
                {{ __('ليس لديك حساب؟') }} 
                <a href="{{ route('web.auth.register') }}" class="text-primary hover:underline">{{ __('إنشاء حساب جديد') }}</a>
            </p>
        </div>
    </div>
</main>
@endsection
