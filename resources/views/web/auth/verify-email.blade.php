@extends('web.layouts.layout')
@section('title', __('تحقق من بريدك الإلكتروني'))

@section('page-meta')
<meta name="description" content="{{ __('تحقق من بريدك الإلكتروني لتفعيل حسابك في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, تحقق من البريد الإلكتروني, تفعيل الحساب') }}">
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-base-100 rounded-lg shadow-md p-8">
            <div class="text-center mb-6">
                <i class="fas fa-envelope-open-text text-5xl text-primary mb-4"></i>
                <h1 class="text-2xl font-bold">{{ __('تحقق من بريدك الإلكتروني') }}</h1>
            </div>
            
            @if(session('status') == 'verification-link-sent')
                <div class="alert alert-success mb-6">
                    {{ __('تم إرسال رابط تحقق جديد إلى عنوان بريدك الإلكتروني.') }}
                </div>
            @endif
            
            <p class="mb-6 text-center">
                {{ __('شكرًا لتسجيلك! قبل البدء، هل يمكنك التحقق من عنوان بريدك الإلكتروني من خلال النقر على الرابط الذي أرسلناه إليك عبر البريد الإلكتروني؟ إذا لم تستلم البريد الإلكتروني، فسنرسل لك رابطًا آخر بكل سرور.') }}
            </p>
            
            <div class="flex flex-col gap-4">
                <form action="{{ route('web.auth.verification.send') }}" method="POST">
                    @csrf
                    <button type="submit" class="btn btn-primary w-full">
                        {{ __('إعادة إرسال رسالة التحقق') }}
                    </button>
                </form>
                
                <form action="{{ route('web.auth.logout') }}" method="POST">
                    @csrf
                    <button type="submit" class="btn btn-outline w-full">
                        {{ __('تسجيل الخروج') }}
                    </button>
                </form>
            </div>
            
            <div class="mt-6 text-center">
                <p class="text-sm text-base-content/70">
                    {{ __('ملاحظة: بعض الميزات مثل إضافة منتجات أو التواصل مع البائعين تتطلب تحقق من البريد الإلكتروني.') }}
                </p>
            </div>
        </div>
    </div>
</main>
@endsection
