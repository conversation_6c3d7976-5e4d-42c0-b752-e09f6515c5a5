@extends('web.layouts.layout')
@section('title', __('إعادة تعيين كلمة المرور'))

@section('page-meta')
<meta name="description" content="{{ __('إعادة تعيين كلمة المرور في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, إعادة تعيين كلمة المرور, كلمة مرور جديدة') }}">
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-base-100 rounded-lg shadow-md p-8">
            <h1 class="text-2xl font-bold text-center mb-6">{{ __('إعادة تعيين كلمة المرور') }}</h1>
            
            @if($errors->any())
                <div class="alert alert-error mb-6">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <form action="{{ route('web.auth.reset-password') }}" method="POST">
                @csrf
                
                <!-- Password Reset Token -->
                <input type="hidden" name="token" value="{{ $request->route('token') }}">
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('البريد الإلكتروني') }}</span>
                    </label>
                    <input type="email" name="email" value="{{ old('email', $request->email) }}" class="input input-bordered" required autofocus readonly />
                </div>
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('كلمة المرور الجديدة') }}</span>
                    </label>
                    <input type="password" name="password" class="input input-bordered" required />
                </div>
                
                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text">{{ __('تأكيد كلمة المرور الجديدة') }}</span>
                    </label>
                    <input type="password" name="password_confirmation" class="input input-bordered" required />
                </div>
                
                <div class="form-control">
                    <button type="submit" class="btn btn-primary">{{ __('إعادة تعيين كلمة المرور') }}</button>
                </div>
            </form>
        </div>
    </div>
</main>
@endsection
