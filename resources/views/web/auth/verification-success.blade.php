@extends('web.layouts.layout')
@section('title', __('تم التحقق من البريد الإلكتروني'))

@section('page-meta')
<meta name="description" content="{{ __('تم التحقق من البريد الإلكتروني بنجاح في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, تحقق من البريد الإلكتروني, تفعيل الحساب') }}">
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-base-100 rounded-lg shadow-md p-8">
            <div class="text-center mb-6">
                <i class="fas fa-check-circle text-5xl text-success mb-4"></i>
                <h1 class="text-2xl font-bold">{{ __('تم التحقق من البريد الإلكتروني') }}</h1>
            </div>
            
            <p class="mb-6 text-center">
                {{ __('تم تفعيل حسابك بنجاح! يمكنك الآن الاستمتاع بجميع ميزات الموقع بما في ذلك إضافة منتجات والتواصل مع البائعين.') }}
            </p>
            
            <div class="flex flex-col gap-4">
                <a href="{{ route('web.index') }}" class="btn btn-primary">
                    {{ __('الذهاب إلى الصفحة الرئيسية') }}
                </a>
                
                <a href="{{ route('web.products.create') }}" class="btn btn-outline">
                    {{ __('إضافة منتج جديد') }}
                </a>
            </div>
        </div>
    </div>
</main>
@endsection
