@extends('web.layouts.layout')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-base-200 rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">
            {{ __('إضافة إعلان جديد') }}
        </h1>

        @if ($errors->any())
            <div class="alert alert-error mb-6">
                <div>
                    <i class="fa-solid fa-circle-exclamation"></i>
                    <span>{{ __('يرجى تصحيح الأخطاء أدناه') }}</span>
                </div>
                <ul class="mt-2 list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('web.products.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <!-- Title -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('عنوان الإعلان') }} *</span>
                </label>
                <input type="text" name="title" value="{{ old('title') }}" class="input input-bordered w-full" required>
            </div>

            <!-- Description -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">{{ __('وصف الإعلان') }} *</span>
                </label>
                <textarea name="desc" class="textarea textarea-bordered h-32 w-full" required>{{ old('desc') }}</textarea>
            </div>

            <!-- Price -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('السعر') }} ({{ __('اختياري') }})</span>
                </label>
                <input type="number" name="price" value="{{ old('price') }}" class="input input-bordered w-full" min="0" step="0.01">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Category Selection (Hierarchical) -->
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الرئيسي') }} *</span>
                        </label>
                        <select id="parent_category" class="select select-bordered w-full" required>
                            <option value="">{{ __('اختر القسم الرئيسي') }}</option>
                            @foreach ($categories as $category)
                                <option value="{{ $category->id }}" {{ old('parent_category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control" id="child_category_container" style="display: none;">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الفرعي') }} *</span>
                        </label>
                        <select id="child_category" class="select select-bordered w-full">
                            <option value="">{{ __('اختر القسم الفرعي') }}</option>
                        </select>
                    </div>

                    <div class="form-control" id="sub_child_category_container" style="display: none;">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الفرعي الثانوي') }} *</span>
                        </label>
                        <select id="sub_child_category" name="category_id" class="select select-bordered w-full">
                            <option value="">{{ __('اختر القسم الفرعي الثانوي') }}</option>
                        </select>
                    </div>
                </div>

                <!-- Location Selection (Hierarchical) -->
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('الموقع الرئيسي') }} *</span>
                        </label>
                        <select id="parent_location" class="select select-bordered w-full" required>
                            <option value="">{{ __('اختر الموقع الرئيسي') }}</option>
                            @foreach ($locations->where('parent_id', null) as $location)
                                <option value="{{ $location->id }}" {{ old('parent_location_id') == $location->id ? 'selected' : '' }}>
                                    {{ $location->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control" id="child_location_container" style="display: none;">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('الموقع الفرعي') }} *</span>
                        </label>
                        <select id="child_location" name="location_id" class="select select-bordered w-full">
                            <option value="">{{ __('اختر الموقع الفرعي') }}</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Condition -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('حالة المنتج') }} *</span>
                </label>
                <div class="flex flex-wrap gap-4">
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="new" class="radio" {{ old('condition', 'new') == 'new' ? 'checked' : '' }} required>
                        <span class="label-text">{{ __('جديد') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="used" class="radio" {{ old('condition') == 'used' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('مستعمل') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="refurbished" class="radio" {{ old('condition') == 'refurbished' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('مجدد') }}</span>
                    </label>
                </div>
            </div>

            <!-- Images -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('صور المنتج') }} *</span>
                </label>
                <div class="border border-base-300 rounded-lg p-4">
                    <input type="file" name="images[]" class="file-input file-input-bordered w-full" accept="image/*" multiple required>
                    <p class="text-sm mt-2 text-base-content/70">{{ __('يمكنك تحميل عدة صور (الحد الأقصى: 5 صور)') }}</p>
                </div>
            </div>

            <!-- Contact Preferences -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('خيارات التواصل') }}</span>
                </label>
                <div class="flex flex-col gap-2">
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" name="allow_phone_call" value="1" class="checkbox" {{ old('allow_phone_call', '1') == '1' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('السماح بالاتصال الهاتفي') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" name="allow_chat" value="1" class="checkbox" {{ old('allow_chat', '1') == '1' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('السماح بالمحادثة') }}</span>
                    </label>
                </div>
            </div>

            <!-- Alternative Phone -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('رقم هاتف بديل') }} ({{ __('اختياري') }})</span>
                </label>
                <input type="text" name="alternative_phone" value="{{ old('alternative_phone') }}" class="input input-bordered w-full">
            </div>

            <!-- Submit Button -->
            <div class="form-control mt-8">
                <button type="submit" class="btn btn-primary">
                    <i class="fa-solid fa-plus me-2"></i>
                    {{ __('نشر الإعلان') }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Category hierarchical selection
        const parentCategorySelect = document.getElementById('parent_category');
        const childCategorySelect = document.getElementById('child_category');
        const subChildCategorySelect = document.getElementById('sub_child_category');
        const childCategoryContainer = document.getElementById('child_category_container');
        const subChildCategoryContainer = document.getElementById('sub_child_category_container');

        // Location hierarchical selection
        const parentLocationSelect = document.getElementById('parent_location');
        const childLocationSelect = document.getElementById('child_location');
        const childLocationContainer = document.getElementById('child_location_container');

        // Category change event
        parentCategorySelect.addEventListener('change', function() {
            const parentId = this.value;

            // Reset child and sub-child selects
            childCategorySelect.innerHTML = '<option value="">{{ __("اختر القسم الفرعي") }}</option>';
            subChildCategorySelect.innerHTML = '<option value="">{{ __("اختر القسم الفرعي الثانوي") }}</option>';

            // Hide containers initially
            childCategoryContainer.style.display = 'none';
            subChildCategoryContainer.style.display = 'none';

            if (parentId) {
                // Fetch child categories
                fetch(`/api/categories/${parentId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate child categories
                            data.forEach(child => {
                                const option = document.createElement('option');
                                option.value = child.id;
                                option.textContent = child.title;
                                childCategorySelect.appendChild(option);
                            });

                            // Show child category container
                            childCategoryContainer.style.display = 'block';
                        } else {
                            // If no children, use parent as the final selection
                            subChildCategorySelect.innerHTML = '<option value="' + parentId + '" selected>{{ __("استخدم القسم الرئيسي") }}</option>';
                            subChildCategoryContainer.style.display = 'block';
                        }
                    })
                    .catch(error => console.error('Error fetching child categories:', error));
            }
        });

        // Child category change event
        childCategorySelect.addEventListener('change', function() {
            const childId = this.value;

            // Reset sub-child select
            subChildCategorySelect.innerHTML = '<option value="">{{ __("اختر القسم الفرعي الثانوي") }}</option>';

            // Hide sub-child container initially
            subChildCategoryContainer.style.display = 'none';

            if (childId) {
                // Fetch sub-child categories
                fetch(`/api/categories/${childId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate sub-child categories
                            data.forEach(subChild => {
                                const option = document.createElement('option');
                                option.value = subChild.id;
                                option.textContent = subChild.title;
                                subChildCategorySelect.appendChild(option);
                            });

                            // Show sub-child category container
                            subChildCategoryContainer.style.display = 'block';
                        } else {
                            // If no sub-children, use child as the final selection
                            subChildCategorySelect.innerHTML = '<option value="' + childId + '" selected>{{ __("استخدم القسم الفرعي") }}</option>';
                            subChildCategoryContainer.style.display = 'block';
                        }
                    })
                    .catch(error => console.error('Error fetching sub-child categories:', error));
            }
        });

        // Location change event
        parentLocationSelect.addEventListener('change', function() {
            const parentId = this.value;

            // Reset child location select
            childLocationSelect.innerHTML = '<option value="">{{ __("اختر الموقع الفرعي") }}</option>';

            // Hide child location container initially
            childLocationContainer.style.display = 'none';

            if (parentId) {
                // Fetch child locations
                fetch(`/api/locations/${parentId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate child locations
                            data.forEach(child => {
                                const option = document.createElement('option');
                                option.value = child.id;
                                option.textContent = child.name;
                                childLocationSelect.appendChild(option);
                            });

                            // Show child location container
                            childLocationContainer.style.display = 'block';
                        } else {
                            // If no children, use parent as the final selection
                            childLocationSelect.innerHTML = '<option value="' + parentId + '" selected>{{ __("استخدم الموقع الرئيسي") }}</option>';
                            childLocationContainer.style.display = 'block';
                        }
                    })
                    .catch(error => console.error('Error fetching child locations:', error));
            }
        });
    });
</script>
@endpush
