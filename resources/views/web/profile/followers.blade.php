@extends('web.layouts.layout')
@section('title', __('المتابعين'))

@section('page-meta')
<meta name="description" content="{{ __('المتابعين في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, متابعين, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                <li>{{ __('المتابعين') }}</li>
            </ul>
        </div>

        <!-- Followers List -->
        <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
            <h1 class="text-2xl font-bold mb-6">{{ __('المتابعين') }}</h1>

            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                </div>
            @endif

            @if($followers->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($followers as $follow)
                        <div class="bg-base-200 rounded-lg p-4 flex items-center gap-4">
                            <div class="avatar">
                                <div class="w-16 h-16 rounded-full">
                                    @if($follow->user->sessions->count() > 0 && $follow->user->avatar())
                                        <img src="{{ $follow->user->avatar() }}" alt="{{ $follow->user->name }}">
                                    @else
                                        <img src="{{ asset('assets/default-avatar.png') }}" alt="{{ $follow->user->name }}">
                                    @endif
                                </div>
                            </div>
                            <div class="flex-1">
                                <a href="{{ route('web.sellerProfile', $follow->user->username) }}" class="font-bold text-lg hover:text-primary">
                                    {{ $follow->user->name }}
                                </a>
                                <p class="text-sm text-base-content/70">{{ __('عضو منذ') }} {{ $follow->user->created_at->format('Y-m-d') }}</p>
                                <div class="flex items-center mt-2">
                                    <span class="text-sm text-base-content/70 mr-4 rtl:ml-4 rtl:mr-0">
                                        <i class="fas fa-tag mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $follow->user->products->count() }} {{ __('إعلان') }}
                                    </span>
                                    <span class="text-sm text-base-content/70">
                                        <i class="fas fa-users mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $follow->user->followers->count() }} {{ __('متابع') }}
                                    </span>
                                </div>
                            </div>

                            <!-- Follow/Unfollow Button -->
                            @if(auth()->check() && auth()->id() != $follow->user->id)
                                <form action="{{ route('web.toggleFollow') }}" method="POST">
                                    @csrf
                                    <input type="hidden" name="type" value="user">
                                    <input type="hidden" name="id" value="{{ $follow->user->hashId }}">

                                    @if($follow->user->isFollowedBy(auth()->user()))
                                        <button type="submit" class="btn btn-sm btn-outline">
                                            <i class="fas fa-user-minus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إلغاء المتابعة') }}
                                        </button>
                                    @else
                                        <button type="submit" class="btn btn-sm btn-primary">
                                            <i class="fas fa-user-plus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('متابعة') }}
                                        </button>
                                    @endif
                                </form>
                            @endif
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $followers->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-users text-5xl text-base-content/30 mb-4"></i>
                    <p class="text-lg">{{ __('لا يوجد متابعين حتى الآن') }}</p>
                </div>
            @endif
        </div>
    </div>
</main>
@endsection
