@extends('web.layouts.layout')
@section('title', __('إعلاناتي'))

@section('page-meta')
    <meta name="description" content="{{ __('إدارة إعلاناتي في موقع حراجي') }}">
    <meta name="keywords" content="{{ __('حراجي, إعلانات, بيع, شراء, إدارة الإعلانات') }}">
@endsection

@section('content')
    <main class="py-8">
        <div class="container mx-auto px-4">
            <!-- Breadcrumbs -->
            <div class="text-sm breadcrumbs mb-6">
                <ul>
                    <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                    <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                    <li>{{ __('إعلاناتي') }}</li>
                </ul>
            </div>

            <!-- Products Management -->
            <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold">{{ __('إعلاناتي') }}</h1>

                    @if (auth()->user()->hasVerifiedEmail())
                        <a href="{{ route('web.products.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان جديد') }}
                        </a>
                    @else
                        <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                            {{ __('تحقق من البريد لإضافة إعلان') }}
                        </a>
                    @endif
                </div>

                @if (session('success'))
                    <div class="alert alert-success mb-6">
                        <i class="fas fa-check-circle"></i> {{ session('success') }}
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-error mb-6">
                        <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
                    </div>
                @endif

                @if ($products->count() > 0)
                    <!-- Desktop Table View (Hidden on Mobile) -->
                    <div class="overflow-x-auto hidden md:block">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>{{ __('الصورة') }}</th>
                                    <th>{{ __('العنوان') }}</th>
                                    <th>{{ __('السعر') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('المشاهدات') }}</th>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr>
                                        <td>
                                            <div class="avatar">
                                                <div class="w-16 h-16 rounded">
                                                    @if ($product->primaryMediaUrl())
                                                        <img src="{{ $product->primaryMediaUrl() }}"
                                                            alt="{{ $product->title }}">
                                                    @else
                                                        <div
                                                            class="w-full h-full bg-base-200 flex items-center justify-center">
                                                            <i class="fas fa-image text-base-content/30"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('web.product', $product->slug) }}"
                                                class="font-bold hover:text-primary">
                                                {{ \Illuminate\Support\Str::limit($product->title, 30) }}
                                            </a>
                                            <div class="text-sm text-base-content/70">
                                                {{ $product->category->name }}
                                            </div>
                                        </td>
                                        <td>{{ number_format($product->price, 2) }} {{ __('ريال') }}</td>
                                        <td>
                                            @if ($product->status == 'active' || $product->status == 'approved')
                                                <span class="badge badge-success">{{ __('نشط') }}</span>
                                            @elseif($product->status == 'pending')
                                                <span class="badge badge-warning">{{ __('قيد المراجعة') }}</span>
                                            @elseif($product->status == 'rejected')
                                                <span class="badge badge-error">{{ __('مرفوض') }}</span>
                                            @elseif($product->status == 'sold')
                                                <span class="badge badge-info">{{ __('تم البيع') }}</span>
                                            @else
                                                <span class="badge">{{ $product->status }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $product->views_count }}</td>
                                        <td>{{ $product->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <div class="flex gap-2">
                                                <a href="{{ route('web.product', $product->slug) }}"
                                                    class="btn btn-sm btn-ghost btn-circle" title="{{ __('عرض') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('web.editProduct', $product->id) }}"
                                                    class="btn btn-sm btn-ghost btn-circle" title="{{ __('تعديل') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if ($product->status == 'approved')
                                                    <div class="dropdown dropdown-end">
                                                        <label tabindex="0" class="btn btn-sm btn-ghost btn-circle"
                                                            title="{{ __('تغيير الحالة') }}">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </label>
                                                        <ul tabindex="0"
                                                            class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                                            <li>
                                                                <form id="mark-sold-{{ $product->id }}"
                                                                    action="{{ route('web.markProductSold', $product->id) }}"
                                                                    method="POST">
                                                                    @csrf
                                                                    @method('PUT')
                                                                    <button type="submit" class="text-info">
                                                                        <i class="fas fa-check-circle mr-2"></i>
                                                                        {{ __('تم البيع') }}
                                                                    </button>
                                                                </form>
                                                            </li>
                                                            <li>
                                                                <form id="delete-{{ $product->id }}"
                                                                    action="{{ route('web.deleteProduct', $product->id) }}"
                                                                    method="POST">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="text-error">
                                                                        <i class="fas fa-trash mr-2"></i>
                                                                        {{ __('حذف') }}
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View (Shown only on Mobile) -->
                    <div class="grid grid-cols-1 gap-4 md:hidden">
                        @foreach ($products as $product)
                            <div class="card bg-base-200 shadow-sm">
                                <div class="card-body p-4">
                                    <div class="flex items-start gap-3">
                                        <!-- Product Image -->
                                        <div class="avatar">
                                            <div class="w-20 h-20 rounded">
                                                @if ($product->primaryMediaUrl())
                                                    <img src="{{ $product->primaryMediaUrl() }}"
                                                        alt="{{ $product->title }}">
                                                @else
                                                    <div class="w-full h-full bg-base-300 flex items-center justify-center">
                                                        <i class="fas fa-image text-base-content/30"></i>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Product Info -->
                                        <div class="flex-1">
                                            <h3 class="font-bold text-base mb-1">
                                                <a href="{{ route('web.product', $product->slug) }}"
                                                    class="hover:text-primary">
                                                    {{ \Illuminate\Support\Str::limit($product->title, 40) }}
                                                </a>
                                            </h3>
                                            <div class="text-sm text-base-content/70 mb-1">
                                                {{ $product->category->name }}
                                            </div>
                                            <div class="text-base font-semibold mb-1">
                                                {{ number_format($product->price, 2) }} {{ __('ريال') }}
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <div>
                                                    @if ($product->status == 'active')
                                                        <span
                                                            class="badge badge-success badge-sm">{{ __('نشط') }}</span>
                                                    @elseif($product->status == 'pending')
                                                        <span
                                                            class="badge badge-warning badge-sm">{{ __('قيد المراجعة') }}</span>
                                                    @elseif($product->status == 'rejected')
                                                        <span class="badge badge-error badge-sm">{{ __('مرفوض') }}</span>
                                                    @elseif($product->status == 'sold')
                                                        <span class="badge badge-info badge-sm">{{ __('تم البيع') }}</span>
                                                    @else
                                                        <span class="badge badge-sm">{{ $product->status }}</span>
                                                    @endif
                                                </div>
                                                <div class="text-base-content/70">
                                                    <i class="fas fa-eye text-xs"></i> {{ $product->views_count }}
                                                </div>
                                                <div class="text-base-content/70">
                                                    <i class="fas fa-calendar-alt text-xs"></i>
                                                    {{ $product->created_at->format('Y-m-d') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="card-actions justify-end mt-3 pt-3 border-t border-base-300">
                                        <a href="{{ route('web.product', $product->slug) }}" class="btn btn-sm btn-ghost"
                                            title="{{ __('عرض') }}">
                                            <i class="fas fa-eye mr-1"></i> {{ __('عرض') }}
                                        </a>
                                        <a href="{{ route('web.editProduct', $product->id) }}" class="btn btn-sm btn-ghost"
                                            title="{{ __('تعديل') }}">
                                            <i class="fas fa-edit mr-1"></i> {{ __('تعديل') }}
                                        </a>
                                        @if ($product->status == 'active' || $product->status == 'approved')
                                            <div class="dropdown dropdown-top dropdown-end">
                                                <label tabindex="0" class="btn btn-sm btn-ghost"
                                                    title="{{ __('تغيير الحالة') }}">
                                                    <i class="fas fa-ellipsis-v mr-1"></i> {{ __('الحالة') }}
                                                </label>
                                                <ul tabindex="0"
                                                    class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                                    <li>
                                                        <form action="{{ route('web.markProductSold', $product->id) }}"
                                                            method="POST">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="text-info">
                                                                <i class="fas fa-check-circle mr-2"></i>
                                                                {{ __('تم البيع') }}
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <li>
                                                        <form action="{{ route('web.deleteProduct', $product->id) }}"
                                                            method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-error">
                                                                <i class="fas fa-trash mr-2"></i> {{ __('حذف') }}
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $products->links() }}
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-tag text-5xl text-base-content/30 mb-4"></i>
                        <p class="text-lg">{{ __('لا توجد إعلانات حتى الآن') }}</p>
                        @if (auth()->user()->hasVerifiedEmail())
                            <a href="{{ route('web.products.create') }}" class="btn btn-primary mt-4">
                                <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان جديد') }}
                            </a>
                        @else
                            <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning mt-4">
                                <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                                {{ __('تحقق من البريد لإضافة إعلان') }}
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </main>

@endsection
