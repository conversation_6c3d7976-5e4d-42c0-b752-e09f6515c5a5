@extends('web.layouts.layout')
@section('title', __('المفضلة'))

@section('page-meta')
<meta name="description" content="{{ __('إدارة المفضلة في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, مفضلة, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                <li>{{ __('المفضلة') }}</li>
            </ul>
        </div>

        <!-- Favorites Management -->
        <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
            <h1 class="text-2xl font-bold mb-6">{{ __('المفضلة') }}</h1>

            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                </div>
            @endif

            @if($favorites->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($favorites as $favorite)
                        <div class="product-card bg-base-100 rounded-lg overflow-hidden shadow-sm transition-all duration-300 hover:shadow-md border border-base-200 relative">
                            <a href="{{ route('web.product', $favorite->slug) }}" class="block relative aspect-[4/3]">
                                @if($favorite->primaryMediaUrl())
                                    <img src="{{ $favorite->primaryMediaUrl() }}" alt="{{ $favorite->title }}" class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-base-200 flex items-center justify-center">
                                        <i class="fas fa-image text-4xl text-base-content/30"></i>
                                    </div>
                                @endif
                                <div class="absolute top-2 right-2 rtl:right-auto rtl:left-2">
                                    <span class="badge badge-sm {{ $favorite->condition == 'new' ? 'badge-success' : 'badge-warning' }}">
                                        {{ $favorite->condition == 'new' ? __('جديد') : __('مستعمل') }}
                                    </span>
                                </div>
                            </a>
                            <div class="p-4">
                                <a href="{{ route('web.product', $favorite->slug) }}" class="block">
                                    <h3 class="font-bold text-lg mb-2 line-clamp-1">{{ $favorite->title }}</h3>
                                </a>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-primary font-bold">{{ number_format($favorite->price, 2) }} {{ __('ريال') }}</span>
                                    <span class="text-sm text-base-content/70">{{ $favorite->created_at->diffForHumans() }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <a href="{{ route('web.category', $favorite->category->slug) }}" class="text-sm text-base-content/70 hover:text-primary">
                                        <i class="fas fa-tag mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $favorite->category->name }}
                                    </a>
                                    <div class="flex items-center text-sm text-base-content/70">
                                        <i class="fas fa-eye mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $favorite->views_count }}
                                    </div>
                                </div>

                                <!-- Remove from favorites button -->
                                <form action="{{ route('web.profileToggleFavorite', $favorite->id) }}" method="POST" class="absolute top-2 left-2 rtl:left-auto rtl:right-2">
                                    @csrf
                                    <button type="submit" class="btn btn-sm btn-circle btn-error">
                                        <i class="fas fa-heart-broken"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $favorites->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-heart text-5xl text-base-content/30 mb-4"></i>
                    <p class="text-lg">{{ __('لا توجد إعلانات في المفضلة حتى الآن') }}</p>
                    <a href="{{ route('web.index') }}" class="btn btn-primary mt-4">
                        <i class="fas fa-search mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تصفح الإعلانات') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</main>
@endsection
