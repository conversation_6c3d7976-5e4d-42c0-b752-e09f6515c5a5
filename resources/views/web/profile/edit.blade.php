@extends('web.layouts.layout')
@section('title', __('تعديل الملف الشخصي'))

@section('page-meta')
<meta name="description" content="{{ __('تعديل الملف الشخصي في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, ملف شخصي, تعديل, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8 bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}" class="hover:text-primary transition-colors">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}" class="hover:text-primary transition-colors">{{ __('الملف الشخصي') }}</a></li>
                <li class="text-primary font-medium">{{ __('تعديل الملف الشخصي') }}</li>
            </ul>
        </div>

        <!-- Profile Edit Form -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden max-w-4xl mx-auto">
            <div class="p-6 border-b border-gray-100">
                <h1 class="text-2xl font-bold text-gray-800">{{ __('تعديل الملف الشخصي') }}</h1>
                <p class="text-gray-500 mt-1">{{ __('قم بتحديث معلوماتك الشخصية وصورك') }}</p>
            </div>

            @if(session('success'))
                <div class="alert alert-success mx-6 mt-6 flex items-center gap-3">
                    <i class="fas fa-check-circle text-lg"></i> {{ session('success') }}
                </div>
            @endif

            <form action="{{ route('web.profile.update') }}" method="POST" enctype="multipart/form-data" class="p-6">
                @csrf
                @method('PUT')

                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold mb-4 text-gray-700 flex items-center gap-2">
                        <i class="fas fa-user-circle"></i> {{ __('المعلومات الأساسية') }}
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                        <!-- Name -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">{{ __('الاسم') }} <span class="text-error">*</span></span>
                            </label>
                            <input type="text" name="name" value="{{ old('name', $user->name) }}" 
                                class="input input-bordered w-full focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all @error('name') input-error @enderror" required>
                            @error('name')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">{{ __('البريد الإلكتروني') }} <span class="text-error">*</span></span>
                            </label>
                            <input type="email" name="email" value="{{ old('email', $user->email) }}" 
                                class="input input-bordered w-full focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all @error('email') input-error @enderror" required>
                            @error('email')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Phone -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">{{ __('رقم الهاتف') }}</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 rtl:left-auto rtl:right-0 rtl:pl-0 rtl:pr-3">
                                    <i class="fas fa-phone-alt text-gray-400"></i>
                                </span>
                                <input type="text" name="phone" value="{{ old('phone', $user->phone) }}" 
                                    class="input input-bordered w-full pl-10 rtl:pl-4 rtl:pr-10 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all @error('phone') input-error @enderror">
                            </div>
                            @error('phone')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Username (read-only) -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">{{ __('اسم المستخدم') }}</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 rtl:left-auto rtl:right-0 rtl:pl-0 rtl:pr-3">
                                    <i class="fas fa-at text-gray-400"></i>
                                </span>
                                <input type="text" value="{{ $user->username }}" 
                                    class="input input-bordered w-full pl-10 rtl:pl-4 rtl:pr-10 bg-gray-50 cursor-not-allowed" disabled>
                            </div>
                            <label class="label">
                                <span class="label-text-alt text-gray-500"><i class="fas fa-lock-alt text-xs mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('لا يمكن تغيير اسم المستخدم') }}</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Bio Section -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold mb-4 text-gray-700 flex items-center gap-2">
                        <i class="fas fa-comment-alt"></i> {{ __('نبذة شخصية') }}
                    </h2>
                    <div class="form-control">
                        <textarea name="bio" 
                            class="textarea textarea-bordered h-32 w-full focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all @error('bio') textarea-error @enderror" 
                            placeholder="{{ __('اكتب نبذة قصيرة عن نفسك...') }}">{{ old('bio', $user->bio) }}</textarea>
                        <div class="flex justify-between mt-2">
                            <span class="text-xs text-gray-500">{{ __('اكتب نبذة مختصرة تعريفية عنك') }}</span>
                            <span class="text-xs text-gray-500" id="bio-counter">0/500</span>
                        </div>
                        @error('bio')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>
                </div>

                <!-- Profile Images Section -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold mb-4 text-gray-700 flex items-center gap-2">
                        <i class="fas fa-images"></i> {{ __('الصور الشخصية') }}
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8">
                        <!-- Profile Image -->
                        <div class="form-control">
                            <label class="label mb-2">
                                <span class="label-text font-medium">{{ __('صورة الملف الشخصي') }}</span>
                            </label>
                            <div class="flex flex-col sm:flex-row items-center gap-4">
                                <div class="relative group">
                                    <div class="w-24 h-24 bg-gray-100 rounded-full overflow-hidden border-4 border-white shadow-sm">
                                        @php
                                            $avatar = $user->avatar(true);
                                        @endphp
                                        
                                        @if(is_array($avatar) && $avatar['type'] === 'initials')
                                            <div class="w-full h-full flex items-center justify-center text-white text-xl font-bold {{ $avatar['color'] }}" id="avatar-preview-initials">
                                                {{ $avatar['initials'] }}
                                            </div>
                                            <img src="" alt="{{ $user->name }}" class="w-full h-full object-cover hidden" id="avatar-preview">
                                        @else
                                            <img src="{{ $avatar }}" alt="{{ $user->name }}" class="w-full h-full object-cover" id="avatar-preview">
                                            <div class="w-full h-full hidden items-center justify-center text-white text-xl font-bold" id="avatar-preview-initials"></div>
                                        @endif
                                    </div>
                                    <div class="absolute inset-0 bg-black/40 rounded-full opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity cursor-pointer">
                                        <span class="text-white text-xs">{{ __('تغيير') }}</span>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <label class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <i class="fas fa-cloud-upload-alt text-gray-400 mb-2 text-xl"></i>
                                            <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">{{ __('اضغط للتحميل') }}</span></p>
                                            <p class="text-xs text-gray-500">PNG, JPG (الحد الأقصى 2MB)</p>
                                        </div>
                                        <input type="file" name="avatar" class="hidden" accept="image/*" onchange="previewAvatar(this)">
                                    </label>
                                    @error('avatar')
                                        <p class="text-error text-xs mt-2">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Cover Image -->
                        <div class="form-control">
                            <label class="label mb-2">
                                <span class="label-text font-medium">{{ __('صورة الغلاف') }}</span>
                            </label>
                            <div class="relative w-full h-40 bg-gray-100 rounded-lg overflow-hidden border border-gray-200 shadow-sm group">
                                @if($user->cover)
                                    <img src="{{ asset('storage/' . $user->cover) }}" alt="{{ __('صورة الغلاف') }}" class="w-full h-full object-cover" id="cover-preview">
                                @else
                                    <div class="w-full h-full bg-gray-100 flex flex-col items-center justify-center" id="cover-preview-placeholder">
                                        <i class="fas fa-image text-gray-400 mb-2 text-2xl"></i>
                                        <p class="text-sm text-gray-500">{{ __('لم يتم تحميل صورة الغلاف') }}</p>
                                    </div>
                                    <img src="" alt="{{ __('صورة الغلاف') }}" class="w-full h-full object-cover hidden" id="cover-preview">
                                @endif
                                <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity cursor-pointer">
                                    <label class="cursor-pointer bg-white/90 hover:bg-white text-gray-700 py-2 px-4 rounded-lg shadow-sm transition-colors">
                                        <i class="fas fa-upload mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تحميل صورة') }}
                                        <input type="file" name="cover" class="hidden" accept="image/*" onchange="previewCover(this)">
                                    </label>
                                </div>
                            </div>
                            @error('cover')
                                <p class="text-error text-xs mt-2">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-100 pt-6 mt-8 flex flex-col-reverse sm:flex-row sm:justify-between sm:items-center gap-4">
                    <a href="{{ route('web.profile.show') }}" class="btn btn-ghost btn-block sm:btn-md hover:bg-gray-100 transition-colors">
                        {{ __('إلغاء') }}
                    </a>
                    <button type="submit" class="btn btn-primary btn-block sm:btn-md shadow-sm hover:shadow transition-all">
                        <i class="fas fa-save mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('حفظ التغييرات') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</main>
@endsection

@push('scripts')
<script>
    // Handle bio character count
    document.addEventListener('DOMContentLoaded', function() {
        const bioTextarea = document.querySelector('textarea[name="bio"]');
        const bioCounter = document.getElementById('bio-counter');
        
        function updateCounter() {
            const count = bioTextarea.value.length;
            bioCounter.textContent = count + '/500';
            
            if (count > 500) {
                bioCounter.classList.add('text-error');
            } else {
                bioCounter.classList.remove('text-error');
            }
        }
        
        bioTextarea.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    });
    
    // Avatar preview functionality
    function previewAvatar(input) {
        if (input.files && input.files[0]) {
            var file = input.files[0];
            var reader = new FileReader();
            
            // Check file size (max 2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('حجم الملف كبير جدًا. الحد الأقصى هو 2 ميجابايت.');
                input.value = '';
                return;
            }
            
            reader.onload = function(e) {
                // Show the image preview and hide the initials
                var previewImg = document.getElementById('avatar-preview');
                var initialsDiv = document.getElementById('avatar-preview-initials');
                
                previewImg.src = e.target.result;
                previewImg.classList.remove('hidden');
                initialsDiv.classList.add('hidden');
            };
            reader.readAsDataURL(file);
        }
    }

    // Cover image preview functionality
    function previewCover(input) {
        if (input.files && input.files[0]) {
            var file = input.files[0];
            var reader = new FileReader();
            
            // Check file size (max 2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('حجم الملف كبير جدًا. الحد الأقصى هو 2 ميجابايت.');
                input.value = '';
                return;
            }
            
            reader.onload = function(e) {
                var preview = document.getElementById('cover-preview');
                preview.src = e.target.result;
                preview.classList.remove('hidden');
                
                if (document.getElementById('cover-preview-placeholder')) {
                    document.getElementById('cover-preview-placeholder').classList.add('hidden');
                }
            };
            reader.readAsDataURL(file);
        }
    }
</script>
@endpush
