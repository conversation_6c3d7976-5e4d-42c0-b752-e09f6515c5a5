@extends('web.layouts.layout')
@section('title', __('أتابعهم'))

@section('page-meta')
<meta name="description" content="{{ __('الأشخاص الذين أتابعهم في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, متابعة, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                <li>{{ __('أتابعهم') }}</li>
            </ul>
        </div>

        <!-- Following List -->
        <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
            <h1 class="text-2xl font-bold mb-6">{{ __('أتابعهم') }}</h1>

            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                </div>
            @endif

            @if($following->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($following as $follow)
                        <div class="bg-base-200 rounded-lg p-4 flex items-center gap-4">
                            <div class="avatar">
                                <div class="w-16 h-16 rounded-full">
                                        <img src="{{$follow->followable->avatar() }}" alt="{{ $follow->followable->name }}">
                                </div>
                            </div>
                            <div class="flex-1">
                                <a href="{{ route('web.sellerProfile', $follow->followable->username) }}" class="font-bold text-lg hover:text-primary">
                                    {{ $follow->followable->name }}
                                </a>
                                <p class="text-sm text-base-content/70">{{ __('عضو منذ') }} {{ $follow->followable->created_at->format('Y-m-d') }}</p>
                                <div class="flex items-center mt-2">
                                    <span class="text-sm text-base-content/70 mr-4 rtl:ml-4 rtl:mr-0">
                                        <i class="fas fa-tag mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $follow->followable->products->count() }} {{ __('إعلان') }}
                                    </span>
                                    <span class="text-sm text-base-content/70">
                                        <i class="fas fa-users mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $follow->followable->followers->count() }} {{ __('متابع') }}
                                    </span>
                                </div>
                            </div>

                            <!-- Unfollow Button -->
                            <form action="{{ route('web.toggleFollow') }}" method="POST">
                                @csrf
                                <input type="hidden" name="type" value="user">
                                <input type="hidden" name="id" value="{{ $follow->followable->hashId }}">

                                <button type="submit" class="btn btn-sm btn-outline">
                                    <i class="fas fa-user-minus mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إلغاء المتابعة') }}
                                </button>
                            </form>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $following->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-user-plus text-5xl text-base-content/30 mb-4"></i>
                    <p class="text-lg">{{ __('لا يوجد أشخاص تتابعهم حتى الآن') }}</p>
                    <a href="{{ route('web.index') }}" class="btn btn-primary mt-4">
                        <i class="fas fa-search mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تصفح الإعلانات') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</main>
@endsection
