@extends('web.layouts.layout')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-base-200 rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">
            {{ __('تعديل الإعلان') }}
        </h1>

        @if ($errors->any())
            <div class="alert alert-error mb-6">
                <div>
                    <i class="fa-solid fa-circle-exclamation"></i>
                    <span>{{ __('يرجى تصحيح الأخطاء أدناه') }}</span>
                </div>
                <ul class="mt-2 list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('web.updateProduct', $product->id) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Title -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('عنوان الإعلان') }} *</span>
                </label>
                <input type="text" name="title" value="{{ old('title', $product->title) }}" class="input input-bordered w-full" required>
            </div>

            <!-- Description -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">{{ __('وصف الإعلان') }} *</span>
                </label>
                <textarea name="desc" class="textarea textarea-bordered h-32 w-full" required>{{ old('desc', $product->desc) }}</textarea>
            </div>

            <!-- Price -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('السعر') }} ({{ __('اختياري') }})</span>
                </label>
                <input type="number" name="price" value="{{ old('price', $product->price) }}" class="input input-bordered w-full" min="0" step="0.01">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Category Selection (Hierarchical) -->
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الرئيسي') }} *</span>
                        </label>
                        <select id="parent_category" class="select select-bordered w-full" required>
                            <option value="">{{ __('اختر القسم الرئيسي') }}</option>
                            @foreach ($categories as $category)
                                <option value="{{ $category->id }}"
                                    {{ !$product->category || $product->category->parent_id == null && $product->category_id == $category->id ? 'selected' :
                                       ($product->category && $product->category->parent_id != null && $product->category->parent_id == $category->id ? 'selected' :
                                       ($product->category && $product->category->parent && $product->category->parent->parent_id == $category->id ? 'selected' : '')) }}>
                                    {{ $category->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control" id="child_category_container" style="{{ $product->category && $product->category->parent_id != null ? 'display: block;' : 'display: none;' }}">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الفرعي') }} *</span>
                        </label>
                        <select id="child_category" class="select select-bordered w-full">
                            <option value="">{{ __('اختر القسم الفرعي') }}</option>
                            @if($product->category && $product->category->parent_id != null)
                                @foreach($categories->where('id', $product->category->parent_id)->first()->children as $child)
                                    <option value="{{ $child->id }}" {{ $product->category_id == $child->id ? 'selected' : '' }}>
                                        {{ $child->title }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>

                    <div class="form-control" id="sub_child_category_container" style="{{ $product->category && $product->category->parent && $product->category->parent->parent_id != null ? 'display: block;' : 'display: none;' }}">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('القسم الفرعي الثانوي') }} *</span>
                        </label>
                        <select id="sub_child_category" name="category_id" class="select select-bordered w-full">
                            <option value="">{{ __('اختر القسم الفرعي الثانوي') }}</option>
                            @if($product->category && $product->category->parent && $product->category->parent->parent_id != null)
                                @foreach($categories->where('id', $product->category->parent->parent_id)->first()->children->where('id', $product->category->parent_id)->first()->children as $subChild)
                                    <option value="{{ $subChild->id }}" {{ $product->category_id == $subChild->id ? 'selected' : '' }}>
                                        {{ $subChild->title }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        <input type="hidden" name="category_id" id="final_category_id" value="{{ $product->category_id }}">
                    </div>
                </div>

                <!-- Location Selection (Hierarchical) -->
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('الموقع الرئيسي') }} *</span>
                        </label>
                        <select id="parent_location" class="select select-bordered w-full" required>
                            <option value="">{{ __('اختر الموقع الرئيسي') }}</option>
                            @foreach ($locations->where('parent_id', null) as $location)
                                <option value="{{ $location->id }}" {{ $product->location->parent_id == null && $product->location_id == $location->id ? 'selected' : ($product->location->parent_id == $location->id ? 'selected' : '') }}>
                                    {{ $location->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control" id="child_location_container" style="{{ $product->location && $product->location->parent_id != null ? 'display: block;' : 'display: none;' }}">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('الموقع الفرعي') }} *</span>
                        </label>
                        <select id="child_location" name="location_id" class="select select-bordered w-full">
                            <option value="">{{ __('اختر الموقع الفرعي') }}</option>
                            @if($product->location && $product->location->parent_id != null)
                                @foreach($locations->where('id', $product->location->parent_id)->first()->children as $child)
                                    <option value="{{ $child->id }}" {{ $product->location_id == $child->id ? 'selected' : '' }}>
                                        {{ $child->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>

            <!-- Condition -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('حالة المنتج') }} *</span>
                </label>
                <div class="flex flex-wrap gap-4">
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="new" class="radio" {{ old('condition', $product->condition) == 'new' ? 'checked' : '' }} required>
                        <span class="label-text">{{ __('جديد') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="used" class="radio" {{ old('condition', $product->condition) == 'used' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('مستعمل') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="radio" name="condition" value="refurbished" class="radio" {{ old('condition', $product->condition) == 'refurbished' ? 'checked' : '' }}>
                        <span class="label-text">{{ __('مجدد') }}</span>
                    </label>
                </div>
            </div>

            <!-- Current Images -->
            @if($product->media->count() > 0)
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">{{ __('الصور الحالية') }}</span>
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        @foreach($product->media as $media)
                            <div class="relative product-image-container">
                                <img src="{{ asset('storage/products/' . $media->path) }}" alt="{{ $product->title }}" class="w-full h-32 object-cover rounded-lg">

                                <!-- Delete Button - Always Visible -->
                                <div class="absolute top-2 right-2">
                                    <label class="btn btn-circle btn-error btn-sm delete-image-btn" for="delete_media_{{ $media->id }}">
                                        <i class="fas fa-trash-alt text-white"></i>
                                    </label>
                                    <input type="checkbox" id="delete_media_{{ $media->id }}" name="delete_media[]" value="{{ $media->id }}" class="hidden delete-image-checkbox">
                                </div>

                                <!-- Primary Image Indicator -->
                                @if($media->is_primary)
                                    <div class="absolute bottom-2 left-2 bg-success text-white px-2 py-1 rounded-md text-xs">
                                        {{ __('الصورة الرئيسية') }}
                                    </div>
                                @endif

                                <!-- Selected for Deletion Overlay -->
                                <div class="delete-overlay absolute inset-0 bg-error bg-opacity-30 justify-center items-center rounded-lg hidden">
                                    <div class="bg-white p-2 rounded-md shadow-md">
                                        <span class="text-error font-bold">{{ __('سيتم حذف هذه الصورة') }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="alert alert-warning mt-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span>{{ __('انقر على أيقونة سلة المهملات لتحديد الصور التي ترغب في حذفها') }}</span>
                    </div>
                </div>
            @endif

            <style>
                .delete-image-btn {
                    opacity: 0.9;
                    transition: all 0.2s ease;
                }

                .delete-image-btn:hover {
                    transform: scale(1.1);
                    opacity: 1;
                }

                .product-image-container {
                    transition: all 0.3s ease;
                }

                .product-image-container.marked-for-deletion img {
                    filter: grayscale(100%);
                }
            </style>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Get all delete buttons
                    const deleteButtons = document.querySelectorAll('.delete-image-btn');

                    // Add click event to each button
                    deleteButtons.forEach(button => {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();

                            // Get the associated checkbox
                            const checkboxId = this.getAttribute('for');
                            const checkbox = document.getElementById(checkboxId);

                            // Toggle checkbox state
                            checkbox.checked = !checkbox.checked;

                            // Get the parent container
                            const container = this.closest('.product-image-container');

                            // Toggle the marked-for-deletion class
                            container.classList.toggle('marked-for-deletion', checkbox.checked);

                            // Toggle the delete overlay
                            const overlay = container.querySelector('.delete-overlay');
                            if (checkbox.checked) {
                                overlay.classList.remove('hidden');
                                overlay.classList.add('flex');
                                this.classList.add('btn-success');
                                this.classList.remove('btn-error');
                                this.innerHTML = '<i class="fas fa-undo text-white"></i>';
                            } else {
                                overlay.classList.add('hidden');
                                overlay.classList.remove('flex');
                                this.classList.remove('btn-success');
                                this.classList.add('btn-error');
                                this.innerHTML = '<i class="fas fa-trash-alt text-white"></i>';
                            }
                        });
                    });
                });
            </script>

            <!-- Add New Images -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('إضافة صور جديدة') }}</span>
                </label>
                <div class="border border-base-300 rounded-lg p-4">
                    <input type="file" name="images[]" class="file-input file-input-bordered w-full" accept="image/*" multiple>
                    <p class="text-sm mt-2 text-base-content/70">{{ __('يمكنك تحميل عدة صور (الحد الأقصى: 5 صور)') }}</p>
                </div>
            </div>

            <!-- Contact Preferences -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('خيارات التواصل') }}</span>
                </label>
                <div class="flex flex-col gap-2">
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" name="allow_phone_call" value="1" class="checkbox" {{ old('allow_phone_call', $product->allow_phone_call) ? 'checked' : '' }}>
                        <span class="label-text">{{ __('السماح بالاتصال الهاتفي') }}</span>
                    </label>
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" name="allow_chat" value="1" class="checkbox" {{ old('allow_chat', $product->allow_chat) ? 'checked' : '' }}>
                        <span class="label-text">{{ __('السماح بالمحادثة') }}</span>
                    </label>
                </div>
            </div>

            <!-- Alternative Phone -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('رقم هاتف بديل') }} ({{ __('اختياري') }})</span>
                </label>
                <input type="text" name="alternative_phone" value="{{ old('alternative_phone', $product->alternative_phone) }}" class="input input-bordered w-full">
            </div>

            <!-- Submit Button -->
            <div class="form-control mt-8">
                <button type="submit" class="btn btn-primary">
                    <i class="fa-solid fa-save me-2"></i>
                    {{ __('حفظ التغييرات') }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Translations
        const translations = {
            selectSubCategory: "{{ __('اختر القسم الفرعي') }}",
            selectSubSubCategory: "{{ __('اختر القسم الفرعي الثانوي') }}",
            selectSubLocation: "{{ __('اختر الموقع الفرعي') }}",
            useMainCategory: "{{ __('استخدم القسم الرئيسي') }}",
            useSubCategory: "{{ __('استخدم القسم الفرعي') }}",
            useMainLocation: "{{ __('استخدم الموقع الرئيسي') }}"
        };

        // Category hierarchical selection
        const parentCategorySelect = document.getElementById('parent_category');
        const childCategorySelect = document.getElementById('child_category');
        const subChildCategorySelect = document.getElementById('sub_child_category');
        const childCategoryContainer = document.getElementById('child_category_container');
        const subChildCategoryContainer = document.getElementById('sub_child_category_container');
        const finalCategoryIdInput = document.getElementById('final_category_id');

        // Location hierarchical selection
        const parentLocationSelect = document.getElementById('parent_location');
        const childLocationSelect = document.getElementById('child_location');
        const childLocationContainer = document.getElementById('child_location_container');

        // Function to update the final category ID based on selection
        function updateFinalCategoryId() {
            if (subChildCategorySelect.style.display !== 'none' && subChildCategorySelect.value) {
                finalCategoryIdInput.value = subChildCategorySelect.value;
            } else if (childCategorySelect.style.display !== 'none' && childCategorySelect.value) {
                finalCategoryIdInput.value = childCategorySelect.value;
            } else if (parentCategorySelect.value) {
                finalCategoryIdInput.value = parentCategorySelect.value;
            }
        }

        // Category change event
        parentCategorySelect.addEventListener('change', function() {
            const parentId = this.value;

            // Reset child and sub-child selects
            childCategorySelect.innerHTML = `<option value="">${translations.selectSubCategory}</option>`;
            subChildCategorySelect.innerHTML = `<option value="">${translations.selectSubSubCategory}</option>`;

            // Hide containers initially
            childCategoryContainer.style.display = 'none';
            subChildCategoryContainer.style.display = 'none';

            // Update final category ID
            finalCategoryIdInput.value = parentId;

            if (parentId) {
                // Fetch child categories
                fetch(`/api/categories/${parentId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate child categories
                            data.forEach(child => {
                                const option = document.createElement('option');
                                option.value = child.id;
                                option.textContent = child.title;
                                childCategorySelect.appendChild(option);
                            });

                            // Show child category container
                            childCategoryContainer.style.display = 'block';
                        } else {
                            // If no children, use parent as the final selection
                            finalCategoryIdInput.value = parentId;
                        }
                    })
                    .catch(error => console.error('Error fetching child categories:', error));
            }
        });

        // Child category change event
        childCategorySelect.addEventListener('change', function() {
            const childId = this.value;

            // Reset sub-child select
            subChildCategorySelect.innerHTML = `<option value="">${translations.selectSubSubCategory}</option>`;

            // Hide sub-child container initially
            subChildCategoryContainer.style.display = 'none';

            // Update final category ID
            if (childId) {
                finalCategoryIdInput.value = childId;

                // Fetch sub-child categories
                fetch(`/api/categories/${childId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate sub-child categories
                            data.forEach(subChild => {
                                const option = document.createElement('option');
                                option.value = subChild.id;
                                option.textContent = subChild.title;
                                subChildCategorySelect.appendChild(option);
                            });

                            // Show sub-child category container
                            subChildCategoryContainer.style.display = 'block';
                        }
                    })
                    .catch(error => console.error('Error fetching sub-child categories:', error));
            }
        });

        // Sub-child category change event
        subChildCategorySelect.addEventListener('change', function() {
            const subChildId = this.value;
            if (subChildId) {
                finalCategoryIdInput.value = subChildId;
            }
        });

        // Initialize category selections on page load
        function initializeCategorySelections() {
            // Store the current category information
            const currentCategoryId = "{{ $product->category_id }}";
            const hasParent = "{{ $product->category && $product->category->parent_id ? 'true' : 'false' }}" === 'true';
            const parentId = hasParent ? "{{ $product->category ? $product->category->parent_id : '' }}" : null;
            const hasGrandParent = "{{ $product->category && $product->category->parent && $product->category->parent->parent_id ? 'true' : 'false' }}" === 'true';
            const grandParentId = hasGrandParent ? "{{ $product->category && $product->category->parent ? $product->category->parent->parent_id : '' }}" : null;

            // Set the final category ID input
            finalCategoryIdInput.value = currentCategoryId;

            // If we have a grandparent (top-level category) for a sub-sub-category
            if (hasGrandParent) {
                // First select the top-level category
                parentCategorySelect.value = grandParentId;

                // Trigger change to load subcategories
                parentCategorySelect.dispatchEvent(new Event('change'));

                // Wait for subcategories to load, then select the parent
                setTimeout(() => {
                    if (childCategorySelect.options.length > 1) {
                        childCategorySelect.value = parentId;
                        childCategorySelect.dispatchEvent(new Event('change'));

                        // Wait for sub-subcategories to load, then select the current category
                        setTimeout(() => {
                            if (subChildCategorySelect.options.length > 1) {
                                subChildCategorySelect.value = currentCategoryId;
                                subChildCategorySelect.dispatchEvent(new Event('change'));
                            }
                        }, 300);
                    }
                }, 300);
            }
            // If we have a parent (for a subcategory)
            else if (hasParent) {
                // First select the parent category
                parentCategorySelect.value = parentId;

                // Trigger change to load subcategories
                parentCategorySelect.dispatchEvent(new Event('change'));

                // Wait for subcategories to load, then select the current category
                setTimeout(() => {
                    if (childCategorySelect.options.length > 1) {
                        childCategorySelect.value = currentCategoryId;
                        childCategorySelect.dispatchEvent(new Event('change'));
                    }
                }, 300);
            }
            // If this is a top-level category
            else {
                // Just select the top-level category
                parentCategorySelect.value = currentCategoryId;
                parentCategorySelect.dispatchEvent(new Event('change'));
            }
        }

        // Call the initialization function after a short delay to ensure DOM is ready
        setTimeout(initializeCategorySelections, 100);

        // Location change event
        parentLocationSelect.addEventListener('change', function() {
            const parentId = this.value;

            // Reset child location select
            childLocationSelect.innerHTML = `<option value="">${translations.selectSubLocation}</option>`;

            // Hide child location container initially
            childLocationContainer.style.display = 'none';

            if (parentId) {
                // Fetch child locations
                fetch(`/api/locations/${parentId}/children`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            // Populate child locations
                            data.forEach(child => {
                                const option = document.createElement('option');
                                option.value = child.id;
                                option.textContent = child.name;
                                childLocationSelect.appendChild(option);
                            });

                            // Show child location container
                            childLocationContainer.style.display = 'block';
                        } else {
                            // If no children, use parent as the final selection
                            childLocationSelect.innerHTML = `<option value="${parentId}" selected>${translations.useMainLocation}</option>`;
                            childLocationContainer.style.display = 'block';
                        }
                    })
                    .catch(error => console.error('Error fetching child locations:', error));
            }
        });
    });
</script>
@endpush
