@extends('web.layouts.layout')
@section('title', __('المنتجات التي أتابعها'))

@section('page-meta')
<meta name="description" content="{{ __('المنتجات التي أتابعها في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, متابعة, منتجات, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                <li>{{ __('المنتجات التي أتابعها') }}</li>
            </ul>
        </div>

        <!-- Page Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">{{ __('المنتجات التي أتابعها') }}</h1>
            <a href="{{ route('web.profile.show') }}" class="btn btn-outline btn-sm">
                <i class="fas fa-arrow-right rtl:fa-arrow-left mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('العودة للملف الشخصي') }}
            </a>
        </div>

        <!-- Products Grid -->
        @if($followedProducts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($followedProducts as $follow)
                    @php
                        $product = $follow->followable;
                    @endphp
                    <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
                        <figure class="relative aspect-[4/3] bg-base-200">
                            @if($product->primaryMediaUrl())
                                <img src="{{ $product->primaryMediaUrl() }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <i class="fas fa-image text-4xl text-base-content/30"></i>
                                </div>
                            @endif
                            
                            @if($product->status == 'sold')
                                <div class="absolute top-0 right-0 bg-error text-white px-3 py-1 m-2 rounded-md">
                                    {{ __('تم البيع') }}
                                </div>
                            @endif
                        </figure>
                        
                        <div class="card-body">
                            <h2 class="card-title line-clamp-1">{{ $product->title }}</h2>
                            
                            <div class="flex items-center mt-2">
                                <div class="avatar mr-2 rtl:ml-2 rtl:mr-0">
                                    <div class="w-8 h-8 rounded-full">
                                        <img src="{{ $product->user->avatar() }}" alt="{{ $product->user->name }}">
                                    </div>
                                </div>
                                <span class="text-sm">{{ $product->user->name }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center mt-3">
                                <span class="text-primary font-bold">{{ number_format($product->price, 2) }} {{ __('ريال') }}</span>
                                <span class="text-sm text-base-content/70">{{ $product->created_at->diffForHumans() }}</span>
                            </div>
                            
                            <div class="card-actions justify-between mt-4">
                                <a href="{{ route('web.product', $product->slug) }}" class="btn btn-primary btn-sm">
                                    {{ __('عرض التفاصيل') }}
                                </a>
                                
                                <form action="{{ route('web.toggle.follow') }}" method="POST">
                                    @csrf
                                    <input type="hidden" name="type" value="product">
                                    <input type="hidden" name="id" value="{{ $product->id }}">
                                    <button type="submit" class="btn btn-outline btn-sm btn-error">
                                        <i class="fas fa-bell-slash mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('إلغاء المتابعة') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-8">
                {{ $followedProducts->links() }}
            </div>
        @else
            <div class="text-center py-16 bg-base-100 rounded-lg shadow-md">
                <i class="fas fa-bell-slash text-6xl text-base-content/30 mb-4"></i>
                <h2 class="text-2xl font-bold mb-2">{{ __('لا توجد منتجات متابعة') }}</h2>
                <p class="mb-6">{{ __('لم تقم بمتابعة أي منتجات حتى الآن') }}</p>
                <a href="{{ route('web.index') }}" class="btn btn-primary">
                    <i class="fas fa-search mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تصفح المنتجات') }}
                </a>
            </div>
        @endif
    </div>
</main>
@endsection
