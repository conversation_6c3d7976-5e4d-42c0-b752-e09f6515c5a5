@extends('web.layouts.layout')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto bg-base-200 rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">
            {{ __('الإبلاغ عن إعلان') }}
        </h1>

        <div class="mb-6 p-4 bg-base-100 rounded-lg">
            <div class="flex items-center gap-4">
                @if($product->media->isNotEmpty())
                    <img src="{{ asset('storage/products/' . $product->media->first()->path) }}" alt="{{ $product->title }}" class="w-16 h-16 object-cover rounded-md">
                @else
                    <div class="w-16 h-16 bg-base-300 rounded-md flex items-center justify-center">
                        <i class="fa-solid fa-image text-2xl text-base-content/50"></i>
                    </div>
                @endif
                <div>
                    <h2 class="font-semibold">{{ $product->title }}</h2>
                    <p class="text-sm text-base-content/70">{{ __('المعلن') }}: {{ $product->user->name }}</p>
                </div>
            </div>
        </div>

        @if ($errors->any())
            <div class="alert alert-error mb-6">
                <div>
                    <i class="fa-solid fa-circle-exclamation"></i>
                    <span>{{ __('يرجى تصحيح الأخطاء أدناه') }}</span>
                </div>
                <ul class="mt-2 list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('web.submitReport', $product->id) }}" method="POST" class="space-y-6">
            @csrf

            <!-- Report Reason -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('سبب الإبلاغ') }} *</span>
                </label>
                <select name="reason" class="select select-bordered w-full" required>
                    <option value="">{{ __('اختر سبب الإبلاغ') }}</option>
                    <option value="spam" {{ old('reason') == 'spam' ? 'selected' : '' }}>{{ __('محتوى مزعج أو غير مرغوب فيه') }}</option>
                    <option value="inappropriate" {{ old('reason') == 'inappropriate' ? 'selected' : '' }}>{{ __('محتوى غير لائق') }}</option>
                    <option value="prohibited" {{ old('reason') == 'prohibited' ? 'selected' : '' }}>{{ __('منتج محظور') }}</option>
                    <option value="fake" {{ old('reason') == 'fake' ? 'selected' : '' }}>{{ __('إعلان مزيف أو احتيالي') }}</option>
                    <option value="duplicate" {{ old('reason') == 'duplicate' ? 'selected' : '' }}>{{ __('إعلان مكرر') }}</option>
                    <option value="wrong_category" {{ old('reason') == 'wrong_category' ? 'selected' : '' }}>{{ __('تصنيف خاطئ') }}</option>
                    <option value="other" {{ old('reason') == 'other' ? 'selected' : '' }}>{{ __('سبب آخر') }}</option>
                </select>
            </div>

            <!-- Report Details -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">{{ __('تفاصيل الإبلاغ') }} *</span>
                </label>
                <textarea name="details" class="textarea textarea-bordered h-32" required>{{ old('details') }}</textarea>
                <label class="label">
                    <span class="label-text-alt">{{ __('يرجى تقديم تفاصيل كافية لمساعدتنا في فهم المشكلة') }}</span>
                </label>
            </div>

            <!-- Submit Button -->
            <div class="flex gap-4">
                <a href="{{ route('web.product', $product->slug) }}" class="btn btn-outline flex-1">
                    <i class="fa-solid fa-arrow-left me-2"></i>
                    {{ __('العودة') }}
                </a>
                <button type="submit" class="btn btn-primary flex-1">
                    <i class="fa-solid fa-flag me-2"></i>
                    {{ __('إرسال البلاغ') }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
