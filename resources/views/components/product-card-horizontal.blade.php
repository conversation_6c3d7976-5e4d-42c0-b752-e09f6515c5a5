@props(['product'])

<div class="product-card bg-base-100 rounded-lg overflow-hidden shadow-sm hover:shadow-md">
    <div class="flex flex-row">
        <a href="{{ route('web.product', $product['slug']) }}"
            class="block relative w-24 h-24 sm:w-32 sm:h-32">
            @if (isset($product['media'][0]))
                @php
                    $mediaPath =
                        $product['media'][0]['thumbnail_path'] ??
                        ($product['media'][0]['small_path'] ??
                            ($product['media'][0]['path'] ?? null));
                    $mediaUrl = $mediaPath ? asset('storage/products/' . $mediaPath) : null;
                @endphp
                @if ($mediaUrl)
                    <img src="{{ $mediaUrl }}" alt="{{ $product['title'] }}"
                        class="w-full h-full object-cover">
                @else
                    <div class="w-full h-full bg-base-200 flex items-center justify-center">
                        <i class="fas fa-image text-3xl text-base-content/30"></i>
                    </div>
                @endif
            @else
                <div class="w-full h-full bg-base-200 flex items-center justify-center">
                    <i class="fas fa-image text-3xl text-base-content/30"></i>
                </div>
            @endif
            @if ($product['condition'] == 'new')
                <div class="absolute top-1 right-1 rtl:right-auto rtl:left-1">
                    <span class="badge badge-xs badge-success">{{ __('جديد') }}</span>
                </div>
            @endif
        </a>

        <div class="p-3 flex-grow">
            <h3 class="font-medium text-sm line-clamp-1 mb-1 text-secondary">
                <a href="{{ route('web.product', $product['slug']) }}">{{ $product['title'] }}</a>
            </h3>

            <div class="flex items-center text-xs text-base-content/70 mb-1">
                <i class="fas fa-map-marker-alt mr-1 rtl:ml-1 rtl:mr-0"></i>
                <span>{{ $product['location']['name'] ?? __('غير محدد') }}</span>
                <span class="mx-2">•</span>
                <span>{{ \Carbon\Carbon::parse($product['created_at'])->diffForHumans() }}</span>
            </div>

            <!-- User Avatar and Name -->
            <div class="flex items-center mb-2 text-xs">
                <div class="avatar mr-2 rtl:ml-2 rtl:mr-0">
                    <div class="w-6 h-6 rounded-full bg-primary text-primary-content flex items-center justify-center text-[8px] font-semibold">
                        @if (isset($product['user']) && isset($product['user']['avatar']) && $product['user']['avatar'])
                            <img src="{{ $product['user']['avatar'] }}" alt="{{ $product['user']['name'] ?? '' }}">
                        @else
                            @php
                                $initials = '';
                                if (isset($product['user']) && isset($product['user']['name'])) {
                                    $nameParts = explode(' ', $product['user']['name']);
                                    $initials = mb_substr($nameParts[0] ?? '', 0, 1, 'UTF-8');
                                    if (count($nameParts) > 1) {
                                        $initials .= mb_substr(end($nameParts), 0, 1, 'UTF-8');
                                    } else if (strlen($nameParts[0] ?? '') > 1) {
                                        $initials .= mb_substr($nameParts[0], 1, 1, 'UTF-8');
                                    }
                                    $initials = mb_strtoupper($initials, 'UTF-8');
                                }
                            @endphp
                            <span class="inline-flex items-center justify-center w-full h-full">{{ $initials }}</span>
                        @endif
                    </div>
                </div>
                <span class="text-base-content/80">{{ $product['user']['name'] ?? __('مستخدم') }}</span>
            </div>

            <div class="flex items-center justify-between mt-1">
                @if ($product['price'] > 0)
                    <span
                        class="text-primary font-bold text-sm">{{ number_format($product['price'], 0) }}
                        {{ __('ريال') }}</span>
                @else
                    <span class="text-primary font-bold text-sm">{{ __('السعر غير محدد') }}</span>
                @endif
                <span class="text-xs text-base-content/50">
                    <i class="fas fa-eye mr-1 rtl:ml-1 rtl:mr-0"></i>{{ $product['views_count'] }}
                </span>
            </div>
        </div>
    </div>
</div>
