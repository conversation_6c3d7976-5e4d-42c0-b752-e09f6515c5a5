@props(['user'])

<div class="bg-white rounded-lg shadow-sm border p-4 mb-4">
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
            @if($user->is_verified)
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="ti ti-shield-check text-xl text-green-600"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900">حساب موثق</h3>
                    <p class="text-sm text-gray-600">تم توثيق حسابك بنجاح</p>
                    @if($user->verified_at)
                        <p class="text-xs text-gray-500">تاريخ التوثيق: {{ $user->verified_at->format('Y-m-d') }}</p>
                    @endif
                </div>
            @elseif($user->hasPendingVerification())
                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i class="ti ti-clock text-xl text-yellow-600"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900">طلب التوثيق قيد المراجعة</h3>
                    <p class="text-sm text-gray-600">سيتم مراجعة طلبك قريباً</p>
                </div>
            @else
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="ti ti-shield-x text-xl text-gray-600"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900">حساب غير موثق</h3>
                    <p class="text-sm text-gray-600">احصل على علامة التوثيق لزيادة الثقة</p>
                </div>
            @endif
        </div>
        
        @if(!$user->is_verified && !$user->hasPendingVerification())
            <a href="{{ route('web.verification.form') }}" 
               class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                طلب التوثيق
            </a>
        @endif
    </div>
</div>
