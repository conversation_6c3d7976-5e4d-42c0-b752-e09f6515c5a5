<div class="table-responsive text-nowrap">
    <table class="table table-hover">
        <thead>
            <tr>
                @foreach ($columns as $column)
                    @if (in_array($column['field'], $visibleColumns))
                        <th class="{{ isset($column['sortable']) && $column['sortable'] === false ? '' : 'sortable' }}"
                            wire:click="{{ isset($column['sortable']) && $column['sortable'] === false ? '' : 'sortBy(\'' . $column['field'] . '\')' }}">
                            {{ $column['title'] }}
                            @if (isset($column['sortable']) && $column['sortable'] !== false)
                                @if ($sortField === $column['field'])
                                    <i
                                        class="ti {{ $sortDirection === 'asc' ? 'ti-arrow-up' : 'ti-arrow-down' }} ti-xs"></i>
                                @endif
                            @endif
                        </th>
                    @endif
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $item)
                <tr>
                    @foreach ($columns as $column)
                        @if (in_array($column['field'], $visibleColumns))
                            <td>
                                @if ($column['field'] === 'actions')
                                    <div class="d-flex gap-2">
                                        @foreach ($this->actions as $actionType)
                                            {!! $this->renderAction($actionType, $item) !!}
                                        @endforeach
                                    </div>
                                @elseif ($column['field'] === 'category_id')
                                    <div class="d-flex align-items-center">
                                        {!! $this->renderColumn($column, $item) !!}
                                        <button type="button" class="btn btn-icon btn-sm btn-text-secondary rounded-pill ms-2"
                                            wire:click="openEditCategoryModal({{ $item->id }})">
                                            <i class="ti ti-edit ti-sm"></i>
                                        </button>
                                    </div>
                                @elseif ($column['field'] === 'status')
                                    <div class="d-flex align-items-center">
                                        {!! $this->renderColumn($column, $item) !!}
                                        <button type="button" class="btn btn-icon btn-sm btn-text-secondary rounded-pill ms-2"
                                            wire:click="openEditStatusModal({{ $item->id }})">
                                            <i class="ti ti-edit ti-sm"></i>
                                        </button>
                                    </div>
                                @else
                                    {!! $this->renderColumn($column, $item) !!}
                                @endif
                            </td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>
    @include('components.table.delete-modal')

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-lg modal-simple">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    <div class="text-center mb-6">
                        <h4 class="mb-2">تعديل التصنيف</h4>
                    </div>
                    <form wire:submit.prevent="updateCategory">
                        <div class="row g-3">
                            <!-- Search Categories -->
                            <div class="col-12 mb-3">
                                <div class="input-group input-group-merge">
                                    <span class="input-group-text"><i class="ti ti-search"></i></span>
                                    <input type="text" class="form-control" id="category-search"
                                        placeholder="ابحث عن تصنيف..." wire:model.live="categorySearchTerm">
                                </div>
                            </div>

                            <!-- Categories as Text Buttons with Horizontal Scroll -->
                            <div class="col-12">
                                <label class="form-label">التصنيفات</label>
                                <div class="category-scroll-container">
                                    @if(count($filteredParentCategories) > 0)
                                        <div class="category-scroll-wrapper">
                                            @foreach($filteredParentCategories as $category)
                                                <button type="button"
                                                    class="btn {{ $selectedCategory == $category->id ? 'btn-primary' : 'btn-outline-primary' }}"
                                                    wire:click="selectCategory({{ $category->id }})">
                                                    {{ $category->title }}
                                                    @if($category->children && $category->children->count() > 0)
                                                        <span class="badge bg-white text-primary ms-2">{{ $category->children->count() }}</span>
                                                    @endif
                                                </button>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-info">
                                            لا توجد تصنيفات مطابقة للبحث
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Subcategories (shown when parent is selected) -->
                            @if($selectedCategory)
                                <div class="col-12 mt-3">
                                    <label class="form-label">التصنيفات الفرعية</label>
                                    <div class="subcategory-container border-start ps-3 ms-3">
                                        @if(count($subcategories) > 0)
                                            <div class="category-scroll-wrapper">
                                                @foreach($subcategories as $subcategory)
                                                    <button type="button"
                                                        class="btn {{ $selectedSubcategory == $subcategory->id ? 'btn-secondary' : 'btn-outline-secondary' }}"
                                                        wire:click="selectSubcategory({{ $subcategory->id }})">
                                                        {{ $subcategory->title }}
                                                        @if($subcategory->children && $subcategory->children->count() > 0)
                                                            <span class="badge bg-white text-secondary ms-2">{{ $subcategory->children->count() }}</span>
                                                        @endif
                                                    </button>
                                                @endforeach
                                            </div>
                                        @else
                                            <div class="alert alert-light border">
                                                لا توجد تصنيفات فرعية
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Sub-subcategories (shown when subcategory is selected) -->
                            @if($selectedSubcategory)
                                <div class="col-12 mt-3">
                                    <label class="form-label">التصنيفات الفرعية الثانوية</label>
                                    <div class="subsubcategory-container border-start ps-3 ms-5">
                                        @if(count($subSubcategories) > 0)
                                            <div class="category-scroll-wrapper">
                                                @foreach($subSubcategories as $subSubcategory)
                                                    <button type="button"
                                                        class="btn {{ $selectedSubSubcategory == $subSubcategory->id ? 'btn-info' : 'btn-outline-info' }}"
                                                        wire:click="selectSubSubcategory({{ $subSubcategory->id }})">
                                                        {{ $subSubcategory->title }}
                                                    </button>
                                                @endforeach
                                            </div>
                                        @else
                                            <div class="alert alert-light border">
                                                لا توجد تصنيفات فرعية ثانوية
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Selected Category Path -->
                            <div class="col-12 mt-4">
                                <div class="alert alert-primary">
                                    <div class="d-flex align-items-center">
                                        <i class="ti ti-info-circle me-2"></i>
                                        <div>
                                            <strong>التصنيف المحدد:</strong>
                                            <span id="selected-category-path">{{ $selectedCategoryPath }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 text-center mt-4">
                                <button type="submit" class="btn btn-primary me-sm-3 me-1">حفظ التغييرات</button>
                                <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal"
                                    aria-label="Close">إلغاء</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
        .category-scroll-container {
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        .category-scroll-wrapper {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 10px;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            gap: 8px;
        }

        .category-scroll-wrapper::-webkit-scrollbar {
            height: 6px;
        }

        .category-scroll-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .category-scroll-wrapper::-webkit-scrollbar-thumb {
            background: #d1d1d1;
            border-radius: 10px;
        }

        .category-scroll-wrapper::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        .category-scroll-wrapper .btn {
            flex: 0 0 auto;
            white-space: nowrap;
            margin-right: 4px;
        }

        .subcategory-container, .subsubcategory-container {
            position: relative;
        }

        .subcategory-container::before, .subsubcategory-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            border-left: 2px solid #e1e1e9;
        }

        /* RTL support */
        [dir="rtl"] .subcategory-container, [dir="rtl"] .subsubcategory-container {
            border-right: 2px solid #e1e1e9;
            border-left: none;
            padding-right: 0.75rem;
            padding-left: 0;
            margin-right: 0.75rem;
            margin-left: 0;
        }

        [dir="rtl"] .subcategory-container::before, [dir="rtl"] .subsubcategory-container::before {
            right: 0;
            left: auto;
            border-right: 2px solid #e1e1e9;
            border-left: none;
        }
    </style>

    <!-- Edit Status Modal -->
    <div class="modal fade" id="editStatusModal" tabindex="-1" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-sm modal-simple">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    <div class="text-center mb-6">
                        <h4 class="mb-2">تعديل حالة المنتج</h4>
                    </div>
                    <form wire:submit.prevent="updateStatus">
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label" for="edit-status">الحالة</label>
                                <select id="edit-status" class="form-select" wire:model="selectedStatus">
                                    <option value="">اختر الحالة</option>
                                    @foreach ($statuses as $status)
                                        <option value="{{ $status->value }}">{{ $status->getLabel($status->value) }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-12 text-center mt-4">
                                <button type="submit" class="btn btn-primary me-sm-3 me-1">حفظ التغييرات</button>
                                <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal"
                                    aria-label="Close">إلغاء</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 p-3">
        {{ $data->links() }}
    </div>

    <script>
        function openDeleteModal(item) {
            $('#deleteModal').modal('show');
            if (item.deleteMessage != undefined) {
                $('#deleteModalMessage').text(item.deleteMessage);
            }
            $('#deleteForm').attr('action', item.delete);
        }

        document.addEventListener('livewire:initialized', () => {
            Livewire.on('openEditCategoryModal', () => {
                $('#editCategoryModal').modal('show');
            });

            Livewire.on('closeEditCategoryModal', () => {
                $('#editCategoryModal').modal('hide');
            });

            Livewire.on('openEditStatusModal', () => {
                $('#editStatusModal').modal('show');
            });

            Livewire.on('closeEditStatusModal', () => {
                $('#editStatusModal').modal('hide');
            });
        });
    </script>
</div>
