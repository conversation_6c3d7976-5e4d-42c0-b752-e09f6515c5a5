<div>
    @if($showModal && $verification)
        <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل طلب التوثيق</h5>
                        <button type="button" class="btn-close" wire:click="closeModal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- User Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="fw-bold">معلومات المستخدم</h6>
                                <p><strong>الاسم:</strong> {{ $verification->user->name }}</p>
                                <p><strong>البريد الإلكتروني:</strong> {{ $verification->user->email }}</p>
                                <p><strong>نوع المستخدم:</strong> {{ $verification->user_type }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">معلومات الطلب</h6>
                                <p><strong>تاريخ الطلب:</strong> {{ $verification->created_at->format('Y-m-d H:i') }}</p>
                                <p><strong>الحالة:</strong> 
                                    @if($verification->status === 'pending')
                                        <span class="badge bg-warning">قيد المراجعة</span>
                                    @elseif($verification->status === 'approved')
                                        <span class="badge bg-success">مقبول</span>
                                    @else
                                        <span class="badge bg-danger">مرفوض</span>
                                    @endif
                                </p>
                                @if($verification->reviewed_at)
                                    <p><strong>تاريخ المراجعة:</strong> {{ $verification->reviewed_at->format('Y-m-d H:i') }}</p>
                                    <p><strong>تمت المراجعة بواسطة:</strong> {{ $verification->reviewer->name ?? 'غير محدد' }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Submitted Data -->
                        @if($verification->submitted_data)
                            <div class="mb-4">
                                <h6 class="fw-bold">البيانات المرسلة</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        @foreach($verification->submitted_data as $key => $value)
                                            <tr>
                                                <td class="fw-bold">{{ $key }}</td>
                                                <td>{{ $value }}</td>
                                            </tr>
                                        @endforeach
                                    </table>
                                </div>
                            </div>
                        @endif

                        <!-- Uploaded Files -->
                        @if($verification->uploaded_files)
                            <div class="mb-4">
                                <h6 class="fw-bold">الملفات المرفقة</h6>
                                <div class="row">
                                    @foreach($verification->uploaded_files as $fieldName => $filename)
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-body p-3">
                                                    <h6 class="card-title">{{ $fieldName }}</h6>
                                                    <a href="{{ $this->getFileUrl($filename) }}" 
                                                       target="_blank" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-download me-1"></i>
                                                        عرض الملف
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Admin Notes -->
                        @if($verification->status === 'pending')
                            <div class="mb-4">
                                <label class="form-label fw-bold">ملاحظات الإدارة</label>
                                <textarea wire:model="adminNotes" 
                                          class="form-control" 
                                          rows="3" 
                                          placeholder="أدخل ملاحظاتك هنا..."></textarea>
                                @error('adminNotes')
                                    <div class="text-danger small">{{ $message }}</div>
                                @enderror
                            </div>
                        @elseif($verification->admin_notes)
                            <div class="mb-4">
                                <h6 class="fw-bold">ملاحظات الإدارة</h6>
                                <div class="alert alert-info">
                                    {{ $verification->admin_notes }}
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        @if($verification->status === 'pending')
                            <button type="button" wire:click="approve" class="btn btn-success">
                                <i class="ti ti-check me-1"></i>
                                قبول الطلب
                            </button>
                            <button type="button" wire:click="reject" class="btn btn-danger">
                                <i class="ti ti-x me-1"></i>
                                رفض الطلب
                            </button>
                        @endif
                        <button type="button" wire:click="closeModal" class="btn btn-secondary">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    @endif
</div>
