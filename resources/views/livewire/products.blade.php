<div>
    <!-- Products Section -->
    <section class="mb-10">
        @if($showCategories)
        <!-- Categories Bar for Quick Filtering -->
        <div class="mb-2 overflow-x-auto">
            <div class="flex gap-2 pb-2">
                <button wire:click="filterByCategory(null)"
                    class="btn btn-sm {{ !$selectedCategoryId ? 'btn-primary' : 'btn-outline' }}">
                    {{ __('الكل') }}
                </button>
                @foreach($categories as $category)
                    <button wire:click="filterByCategory({{ $category->id }})"
                        class="btn btn-sm {{ $selectedCategoryId == $category->id ? 'btn-primary' : 'btn-outline' }}">
                        @if($category->icon)
                            <i class="{{ $category->icon }} mr-1 rtl:ml-1 rtl:mr-0"></i>
                        @endif
                        {{ $category->title }}
                    </button>
                @endforeach
            </div>
        </div>

        <!-- Subcategories Bar (shows only when a parent category is selected) -->
        @if(count($subcategories) > 0)
        <div class="mb-2 overflow-x-auto bg-base-200 p-2 rounded-lg">
            <div class="flex gap-2 pb-2">
                @foreach($subcategories as $subcategory)
                    <button wire:click="filterByCategory({{ $subcategory->id }}, 1)"
                        class="btn btn-xs {{ $selectedCategoryId == $subcategory->id ? 'btn-primary' : 'btn-ghost' }}">
                        @if($subcategory->icon)
                            <i class="{{ $subcategory->icon }} mr-1 rtl:ml-1 rtl:mr-0"></i>
                        @endif
                        {{ $subcategory->title }}
                    </button>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Sub-subcategories Bar (shows only when a subcategory is selected) -->
        @if(count($subsubcategories) > 0)
        <div class="mb-6 overflow-x-auto bg-base-100 border border-base-300 p-2 rounded-lg">
            <div class="flex gap-2 pb-2">
                @foreach($subsubcategories as $subsubcategory)
                    <button wire:click="filterByCategory({{ $subsubcategory->id }}, 2)"
                        class="btn btn-xs {{ $selectedCategoryId == $subsubcategory->id ? 'btn-primary' : 'btn-ghost' }}">
                        @if($subsubcategory->icon)
                            <i class="{{ $subsubcategory->icon }} mr-1 rtl:ml-1 rtl:mr-0"></i>
                        @endif
                        {{ $subsubcategory->title }}
                    </button>
                @endforeach
            </div>
        </div>
        @endif
        @endif

        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-3">
                @if($showFilters)
                <div class="flex items-center gap-2">
                    <button id="mobile-filter-btn" class="lg:hidden btn btn-sm btn-primary btn-outline">
                        <i class="fas fa-filter mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('تصفية') }}
                    </button>

                    <!-- Near Me Button with Indicator -->
                    <button type="button" id="get-location-btn"
                        class="btn btn-sm {{ $isNearbyActive ? 'btn-primary' : 'btn-outline' }} flex items-center gap-1">
                        <i class="fas fa-location-arrow mr-1 rtl:ml-1 rtl:mr-0"></i>
                        {{ __('القريب') }}
                        @if($isNearbyActive)
                            <span class="badge badge-sm badge-accent">{{ __('مفعل') }}</span>
                        @endif
                    </button>

                    <!-- Reactive Location Dropdown -->
                    <div class="dropdown dropdown-hover">
                        <label tabindex="0" class="btn btn-sm btn-ghost">
                            <i class="fas fa-map-marker-alt text-primary mr-1 rtl:ml-1 rtl:mr-0"></i>
                            {{ __('المنطقة') }}
                            <i class="fas fa-chevron-down text-xs ml-1 rtl:mr-1 rtl:ml-0"></i>
                        </label>
                        <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <ul>
                                <li>
                                    <button wire:click="filterByLocation(null)">
                                        {{ __('كل المناطق') }}
                                    </button>
                                </li>
                                <li class="menu-title">
                                    <span>{{ __('المناطق') }}</span>
                                </li>
                                @foreach($locations as $location)
                                    <li>
                                        <button wire:click="filterByLocation({{ $location->id }})">
                                            {{ $location->name }}
                                        </button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <button wire:click="toggleViewMode({{ $viewMode === 'grid' ? '\'list\'' : '\'grid\'' }})"
                class="btn btn-sm btn-outline">
                <i class="fas {{ $viewMode === 'grid' ? 'fa-list' : 'fa-th-large' }}"></i>
            </button>
        </div>

        <!-- Grid View -->
        <div class="{{ $viewMode === 'grid' ? 'block' : 'hidden' }}">
            <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4">
                @foreach ($products as $product)
                    <x-product-card-vertical :product="$product" />
                @endforeach
            </div>
        </div>

        <!-- List View -->
        <div class="{{ $viewMode === 'list' ? 'block' : 'hidden' }}">
            <div class="flex flex-col gap-3">
                @foreach ($products as $product)
                    <x-product-card-horizontal :product="$product" />
                @endforeach
            </div>
        </div>

        <!-- Show More Button -->
        @if ($hasMorePages)
            <div class="flex justify-center mt-6">
                <button wire:click="loadMore" class="btn btn-outline btn-wide">
                    {{ __('عرض المزيد') }} <i class="fas fa-chevron-down mr-2 rtl:ml-2 rtl:mr-0"></i>
                </button>
            </div>
        @endif

        <!-- No Products Message -->
        @if(count($products) == 0)
            <div class="bg-base-200 rounded-lg p-8 text-center">
                <i class="fas fa-search text-5xl text-base-content/30 mb-4"></i>
                <h2 class="text-xl font-bold mb-2">{{ __('لا توجد إعلانات') }}</h2>
                <p class="text-base-content/70 mb-6">
                    @if($pageType == 'category')
                        {{ __('لم يتم العثور على إعلانات في هذا القسم بناءً على معايير البحث الخاصة بك.') }}
                    @elseif($pageType == 'search')
                        {{ __('لم يتم العثور على إعلانات تطابق معايير البحث الخاصة بك.') }}
                    @else
                        {{ __('لا توجد إعلانات متاحة حالياً.') }}
                    @endif
                </p>

                @if($pageType == 'category')
                    <a href="{{ route('web.category', request()->route('slug')) }}" class="btn btn-primary">{{ __('إعادة ضبط الفلتر') }}</a>
                @elseif($pageType == 'search')
                    <a href="{{ route('web.search') }}" class="btn btn-primary">{{ __('إعادة ضبط البحث') }}</a>
                @endif
            </div>
        @endif
    </section>

    <!-- Geolocation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get location button click handler
            document.getElementById('get-location-btn').addEventListener('click', function() {
                // Use Livewire's @this instead of trying to access the component directly
                // This ensures we're using the proper Livewire API

                // If already active, just toggle off
                if (@this.isNearbyActive) {
                    @this.getNearbyProducts();
                    return;
                }

                // Otherwise, get location and activate
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        // Success callback
                        function(position) {
                            // Get coordinates
                            const latitude = position.coords.latitude;
                            const longitude = position.coords.longitude;

                            // Call the Livewire method to filter products by location
                            @this.getNearbyProducts(latitude, longitude);
                        },
                        // Error callback
                        function(error) {
                            console.error('Error getting location:', error);
                            alert('{{ __("لم نتمكن من تحديد موقعك. يرجى التحقق من إعدادات الموقع الخاصة بك.") }}');
                        }
                    );
                } else {
                    alert('{{ __("متصفحك لا يدعم تحديد الموقع الجغرافي.") }}');
                }
            });
        });
    </script>
</div>
