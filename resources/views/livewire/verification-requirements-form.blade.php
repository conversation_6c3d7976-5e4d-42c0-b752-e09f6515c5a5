<div>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">إعدادات متطلبات التوثيق</h5>
        </div>
        <div class="card-body">
            @if (session()->has('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <form wire:submit.prevent="save">
                <div id="requirements-container">
                    @foreach($requirements as $index => $requirement)
                        <div class="requirement-item border rounded p-3 mb-3" wire:key="requirement-{{ $index }}">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">نوع الحقل</label>
                                    <select wire:model="requirements.{{ $index }}.type" class="form-select">
                                        <option value="text">نص</option>
                                        <option value="textarea">نص طويل</option>
                                        <option value="file">ملف</option>
                                        <option value="select">قائمة اختيار</option>
                                    </select>
                                    @error("requirements.{$index}.type")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">عنوان الحقل</label>
                                    <input type="text" wire:model="requirements.{{ $index }}.title" class="form-control" placeholder="مثال: رقم الهوية">
                                    @error("requirements.{$index}.title")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">الوصف</label>
                                    <input type="text" wire:model="requirements.{{ $index }}.description" class="form-control" placeholder="وصف اختياري للحقل">
                                    @error("requirements.{$index}.description")
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-1">
                                    <label class="form-label">مطلوب</label>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="requirements.{{ $index }}.is_required" class="form-check-input" id="required-{{ $index }}">
                                        <label class="form-check-label" for="required-{{ $index }}"></label>
                                    </div>
                                </div>
                            </div>

                            @if($requirement['type'] === 'select')
                                <div class="row mt-2">
                                    <div class="col-md-12">
                                        <label class="form-label">خيارات القائمة (مفصولة بفاصلة)</label>
                                        <input type="text" wire:model="requirements.{{ $index }}.options" class="form-control" placeholder="فرد,شركة,متجر">
                                    </div>
                                </div>
                            @endif

                            <div class="row mt-2">
                                <div class="col-md-12 text-end">
                                    @if(count($requirements) > 1)
                                        <button type="button" wire:click="removeRequirement({{ $index }})" class="btn btn-sm btn-outline-danger">
                                            <i class="ti ti-trash"></i> حذف
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button type="button" wire:click="addRequirement" class="btn btn-outline-primary me-2">
                            <i class="ti ti-plus"></i> إضافة حقل جديد
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-device-floppy"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <style>
    .requirement-item {
        background-color: var(--bs-light-bg-subtle);
        border: 1px solid var(--bs-border-color);
        transition: background-color 0.2s ease-in-out;
    }
    .requirement-item:hover {
        background-color: var(--bs-light-bg-subtle-hover);
    }
    @media (prefers-color-scheme: dark) {
        .requirement-item {
            background-color: var(--bs-light-bg-subtle-hover);
            border-color: var(--bs-border-color);
        }
    }
    </style>
</div>
