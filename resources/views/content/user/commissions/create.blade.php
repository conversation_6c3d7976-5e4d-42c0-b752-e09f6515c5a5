@extends('layouts.front2.app')

@section('title', 'دفع عمولة المنتج')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">دفع عمولة المنتج</h1>
            <p class="text-gray-600">قم بدفع عمولة 1% من قيمة المنتج المباع</p>
        </div>

        <!-- Product Info -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">معلومات المنتج</h2>
            <div class="flex items-center">
                @if($product->primaryMediaUrl())
                <img src="{{ $product->primaryMediaUrl() }}" 
                     alt="{{ $product->title }}" 
                     class="w-20 h-20 object-cover rounded-lg mr-4">
                @else
                <div class="w-20 h-20 bg-gray-200 rounded-lg mr-4 flex items-center justify-center">
                    <i class="ti ti-photo text-gray-400 text-2xl"></i>
                </div>
                @endif
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900">{{ $product->title }}</h3>
                    <p class="text-gray-600 mt-1">سعر المنتج: {{ number_format($product->price, 2) }} ريال</p>
                    <p class="text-green-600 font-semibold mt-1">
                        مبلغ العمولة المطلوب: {{ number_format($commissionAmount, 2) }} ريال (1%)
                    </p>
                </div>
            </div>
        </div>

        <!-- Commission Form -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل الدفع</h2>
            
            <form action="{{ route('web.commissions.store', $product) }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- Payment Method -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع *</label>
                    <div class="space-y-3">
                        @foreach($paymentMethods as $value => $label)
                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" name="payment_method" value="{{ $value }}" 
                                   class="radio radio-primary mr-3" 
                                   {{ old('payment_method') == $value ? 'checked' : '' }}
                                   onchange="togglePaymentProof('{{ $value }}')">
                            <div class="flex-1">
                                <span class="font-medium text-gray-900">{{ $label }}</span>
                                @if($value === 'online_payment')
                                <p class="text-sm text-green-600 mt-1">سيتم قبول الدفع تلقائياً</p>
                                @else
                                <p class="text-sm text-gray-600 mt-1">يتطلب إثبات الدفع</p>
                                @endif
                            </div>
                        </label>
                        @endforeach
                    </div>
                    @error('payment_method')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Proof -->
                <div id="payment-proof-section" class="mb-6" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">إثبات الدفع *</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <input type="file" name="payment_proof" id="payment_proof" 
                               class="hidden" accept="image/*" onchange="previewImage(this)">
                        <label for="payment_proof" class="cursor-pointer">
                            <div id="upload-placeholder">
                                <i class="ti ti-cloud-upload text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600">اضغط لرفع صورة إثبات الدفع</p>
                                <p class="text-sm text-gray-500 mt-1">PNG, JPG, GIF حتى 2MB</p>
                            </div>
                            <div id="image-preview" class="hidden">
                                <img id="preview-img" class="max-w-full h-32 object-cover rounded-lg mx-auto">
                                <p class="text-sm text-gray-600 mt-2">اضغط لتغيير الصورة</p>
                            </div>
                        </label>
                    </div>
                    @error('payment_proof')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                    <textarea name="notes" rows="4" 
                              class="textarea textarea-bordered w-full" 
                              placeholder="أي ملاحظات إضافية حول الدفع...">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Instructions -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="font-medium text-blue-900 mb-2">تعليمات الدفع</h3>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• تأكد من دفع المبلغ الصحيح: {{ number_format($commissionAmount, 2) }} ريال</li>
                        <li>• احتفظ بإثبات الدفع (لقطة شاشة أو صورة)</li>
                        <li>• سيتم مراجعة طلبك خلال 24-48 ساعة</li>
                        <li>• ستصلك إشعار عند قبول أو رفض الطلب</li>
                    </ul>
                </div>

                <!-- Submit Buttons -->
                <div class="flex gap-4">
                    <button type="submit" class="btn btn-primary flex-1">
                        <i class="ti ti-check"></i>
                        تقديم طلب دفع العمولة
                    </button>
                    <a href="{{ route('web.profile.products') }}" class="btn btn-outline btn-secondary">
                        <i class="ti ti-arrow-right"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePaymentProof(paymentMethod) {
    const proofSection = document.getElementById('payment-proof-section');
    const requiresProof = ['wallet', 'bank_transfer'].includes(paymentMethod);
    
    if (requiresProof) {
        proofSection.style.display = 'block';
    } else {
        proofSection.style.display = 'none';
        // Clear the file input
        document.getElementById('payment_proof').value = '';
        resetImagePreview();
    }
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('upload-placeholder').classList.add('hidden');
            document.getElementById('image-preview').classList.remove('hidden');
            document.getElementById('preview-img').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function resetImagePreview() {
    document.getElementById('upload-placeholder').classList.remove('hidden');
    document.getElementById('image-preview').classList.add('hidden');
    document.getElementById('preview-img').src = '';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (selectedMethod) {
        togglePaymentProof(selectedMethod.value);
    }
});
</script>
@endsection
