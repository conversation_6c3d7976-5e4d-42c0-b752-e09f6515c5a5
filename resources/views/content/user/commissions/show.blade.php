@extends('layouts.front2.app')

@section('title', 'تفاصيل العمولة')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">تفاصيل العمولة</h1>
                    <p class="text-gray-600">رقم العمولة: #{{ $commission->hashId }}</p>
                </div>
                <div class="text-right">
                    @if($commission->status->value === 'pending')
                        <span class="badge badge-warning badge-lg">{{ $commission->status_label }}</span>
                    @elseif($commission->status->value === 'approved')
                        <span class="badge badge-success badge-lg">{{ $commission->status_label }}</span>
                    @else
                        <span class="badge badge-error badge-lg">{{ $commission->status_label }}</span>
                    @endif
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Commission Details -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل العمولة</h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-gray-600">مبلغ العمولة:</span>
                        <span class="font-semibold text-green-600">{{ $commission->amount }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-gray-600">طريقة الدفع:</span>
                        <span class="badge badge-info">{{ $commission->payment_method_label }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-gray-600">تاريخ الإنشاء:</span>
                        <span class="text-gray-900">{{ $commission->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                    
                    @if($commission->reviewed_at)
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-gray-600">تاريخ المراجعة:</span>
                        <span class="text-gray-900">{{ $commission->reviewed_at->format('Y-m-d H:i') }}</span>
                    </div>
                    @endif
                    
                    @if($commission->admin)
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-gray-600">تمت المراجعة بواسطة:</span>
                        <span class="text-gray-900">{{ $commission->admin->name }}</span>
                    </div>
                    @endif
                </div>

                <!-- User Notes -->
                @if($commission->notes)
                <div class="mt-6">
                    <h3 class="font-medium text-gray-900 mb-2">ملاحظاتك:</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ $commission->notes }}</p>
                    </div>
                </div>
                @endif

                <!-- Admin Review Notes -->
                @if($commission->review_notes)
                <div class="mt-6">
                    <h3 class="font-medium text-gray-900 mb-2">ملاحظات الإدارة:</h3>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p class="text-blue-800">{{ $commission->review_notes }}</p>
                    </div>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="mt-6 flex gap-3">
                    @if($commission->status->value === 'pending')
                    <a href="{{ route('web.commissions.edit', $commission) }}" 
                       class="btn btn-primary">
                        <i class="ti ti-edit"></i>
                        تعديل العمولة
                    </a>
                    <button onclick="deleteCommission()" class="btn btn-error btn-outline">
                        <i class="ti ti-trash"></i>
                        حذف العمولة
                    </button>
                    @endif
                    
                    <a href="{{ route('web.commissions.index') }}" 
                       class="btn btn-outline btn-secondary">
                        <i class="ti ti-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>

            <!-- Product Details -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل المنتج</h2>
                
                <div class="flex items-start space-x-4 space-x-reverse">
                    @if($commission->product->primaryMediaUrl())
                    <img src="{{ $commission->product->primaryMediaUrl() }}" 
                         alt="{{ $commission->product->title }}" 
                         class="w-24 h-24 object-cover rounded-lg">
                    @else
                    <div class="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                        <i class="ti ti-photo text-gray-400 text-2xl"></i>
                    </div>
                    @endif
                    
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 mb-2">{{ $commission->product->title }}</h3>
                        <p class="text-gray-600 mb-2">{{ Str::limit($commission->product->desc, 100) }}</p>
                        <div class="space-y-1">
                            <p class="text-sm text-gray-600">
                                <span class="font-medium">السعر:</span> {{ number_format($commission->product->price, 2) }} ريال
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium">التصنيف:</span> {{ $commission->product->category->name ?? 'غير محدد' }}
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium">الموقع:</span> {{ $commission->product->location->name ?? 'غير محدد' }}
                            </p>
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{ route('web.product', $commission->product->slug) }}" 
                               class="btn btn-sm btn-outline btn-primary">
                                <i class="ti ti-eye"></i>
                                عرض المنتج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Proof -->
        @if($commission->payment_proof)
        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">إثبات الدفع</h2>
            <div class="text-center">
                <img src="{{ $commission->payment_proof_url }}" 
                     alt="إثبات الدفع" 
                     class="max-w-full h-auto rounded-lg shadow-md mx-auto"
                     style="max-height: 400px;">
                <p class="text-sm text-gray-600 mt-2">صورة إثبات الدفع المرفوعة</p>
            </div>
        </div>
        @endif

        <!-- Commission Timeline -->
        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">سجل العمولة</h2>
            <div class="space-y-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="ti ti-plus text-white text-sm"></i>
                    </div>
                    <div class="mr-4">
                        <p class="font-medium text-gray-900">تم إنشاء طلب العمولة</p>
                        <p class="text-sm text-gray-600">{{ $commission->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
                
                @if($commission->reviewed_at)
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 {{ $commission->status->value === 'approved' ? 'bg-green-500' : 'bg-red-500' }} rounded-full flex items-center justify-center">
                        <i class="ti {{ $commission->status->value === 'approved' ? 'ti-check' : 'ti-x' }} text-white text-sm"></i>
                    </div>
                    <div class="mr-4">
                        <p class="font-medium text-gray-900">
                            {{ $commission->status->value === 'approved' ? 'تم قبول العمولة' : 'تم رفض العمولة' }}
                        </p>
                        <p class="text-sm text-gray-600">{{ $commission->reviewed_at->format('Y-m-d H:i') }}</p>
                        @if($commission->admin)
                        <p class="text-sm text-gray-600">بواسطة: {{ $commission->admin->name }}</p>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">تأكيد الحذف</h3>
        <p class="py-4">هل أنت متأكد من حذف هذه العمولة؟ لا يمكن التراجع عن هذا الإجراء.</p>
        <div class="modal-action">
            <form method="POST" action="{{ route('web.commissions.destroy', $commission) }}">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-error">حذف</button>
            </form>
            <button class="btn" onclick="closeDeleteModal()">إلغاء</button>
        </div>
    </div>
</div>

<script>
function deleteCommission() {
    document.getElementById('deleteModal').classList.add('modal-open');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.remove('modal-open');
}
</script>
@endsection
