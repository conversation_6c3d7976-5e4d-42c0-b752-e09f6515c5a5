@extends('layouts.layoutMaster')

@section('title', 'تفاصيل العمولة')

@section('vendor-style')
<link rel="stylesheet" href="{{asset('assets/vendor/libs/sweetalert2/sweetalert2.css')}}" />
@endsection

@section('vendor-script')
<script src="{{asset('assets/vendor/libs/sweetalert2/sweetalert2.js')}}"></script>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">إدارة العمولات /</span> تفاصيل العمولة
  </h4>

  <div class="row">
    <!-- Commission Details -->
    <div class="col-xl-8 col-lg-7 col-md-7">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">تفاصيل العمولة #{{ $commission->hashId }}</h5>
          <span class="badge {{ $commission->status_badge_class }} badge-lg">
            {{ $commission->status_label }}
          </span>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label fw-semibold">مبلغ العمولة</label>
              <p class="text-success fw-bold fs-4">{{ $commission->amount }} ريال</p>
            </div>
            <div class="col-md-6">
              <label class="form-label fw-semibold">طريقة الدفع</label>
              <p><span class="badge bg-label-info">{{ $commission->payment_method_label }}</span></p>
            </div>
            <div class="col-md-6">
              <label class="form-label fw-semibold">تاريخ الإنشاء</label>
              <p>{{ $commission->created_at->format('Y-m-d H:i') }}</p>
            </div>
            @if($commission->reviewed_at)
            <div class="col-md-6">
              <label class="form-label fw-semibold">تاريخ المراجعة</label>
              <p>{{ $commission->reviewed_at->format('Y-m-d H:i') }}</p>
            </div>
            @endif
          </div>

          @if($commission->notes)
          <div class="mt-4">
            <label class="form-label fw-semibold">ملاحظات المستخدم</label>
            <div class="alert alert-info">
              {{ $commission->notes }}
            </div>
          </div>
          @endif

          @if($commission->review_notes)
          <div class="mt-4">
            <label class="form-label fw-semibold">ملاحظات المراجعة</label>
            <div class="alert alert-warning">
              {{ $commission->review_notes }}
            </div>
          </div>
          @endif
        </div>
      </div>

      <!-- Product Details -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">تفاصيل المنتج</h5>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-start">
            @if($commission->product->primaryMediaUrl())
            <img src="{{ $commission->product->primaryMediaUrl() }}" 
                 alt="{{ $commission->product->title }}" 
                 class="me-3 rounded" 
                 style="width: 100px; height: 100px; object-fit: cover;">
            @else
            <div class="me-3 bg-light rounded d-flex align-items-center justify-content-center" 
                 style="width: 100px; height: 100px;">
              <i class="ti ti-photo ti-lg text-muted"></i>
            </div>
            @endif
            
            <div class="flex-grow-1">
              <h6 class="mb-2">{{ $commission->product->title }}</h6>
              <p class="text-muted mb-2">{{ Str::limit($commission->product->desc, 150) }}</p>
              <div class="row g-2">
                <div class="col-sm-6">
                  <small class="text-muted">السعر:</small>
                  <span class="fw-semibold">{{ number_format($commission->product->price, 2) }} ريال</span>
                </div>
                <div class="col-sm-6">
                  <small class="text-muted">التصنيف:</small>
                  <span>{{ $commission->product->category->name ?? 'غير محدد' }}</span>
                </div>
              </div>
              <div class="mt-2">
                <a href="{{ route('products.show', $commission->product) }}" 
                   class="btn btn-sm btn-outline-primary">
                  <i class="ti ti-eye me-1"></i>عرض المنتج
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Proof -->
      @if($commission->payment_proof)
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">إثبات الدفع</h5>
        </div>
        <div class="card-body text-center">
          <img src="{{ $commission->payment_proof_url }}" 
               alt="إثبات الدفع" 
               class="img-fluid rounded shadow-sm"
               style="max-height: 400px;">
        </div>
      </div>
      @endif
    </div>

    <!-- User Details & Actions -->
    <div class="col-xl-4 col-lg-5 col-md-5">
      <!-- User Info -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">معلومات المستخدم</h5>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ $commission->user->initials }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $commission->user->name }}</h6>
              <small class="text-muted">{{ $commission->user->email }}</small>
            </div>
          </div>
          
          <div class="row g-2">
            <div class="col-6">
              <small class="text-muted">إجمالي العمولات</small>
              <p class="fw-semibold">{{ $commission->user->commission_count }}</p>
            </div>
            <div class="col-6">
              <small class="text-muted">المبلغ الإجمالي</small>
              <p class="fw-semibold">{{ number_format($commission->user->total_commission_amount, 2) }} ريال</p>
            </div>
          </div>
          
          <div class="mt-3">
            <a href="{{ route('users.show', $commission->user) }}" 
               class="btn btn-sm btn-outline-primary w-100">
              <i class="ti ti-user me-1"></i>عرض ملف المستخدم
            </a>
          </div>
        </div>
      </div>

      <!-- Actions -->
      @if($commission->status->value === 'pending')
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">إجراءات المراجعة</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-success" onclick="approveCommission()">
              <i class="ti ti-check me-1"></i>قبول العمولة
            </button>
            <button type="button" class="btn btn-danger" onclick="rejectCommission()">
              <i class="ti ti-x me-1"></i>رفض العمولة
            </button>
          </div>
        </div>
      </div>
      @endif

      <!-- Commission Timeline -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">سجل العمولة</h5>
        </div>
        <div class="card-body">
          <ul class="timeline">
            <li class="timeline-item timeline-item-primary">
              <span class="timeline-indicator timeline-indicator-primary">
                <i class="ti ti-plus"></i>
              </span>
              <div class="timeline-event">
                <div class="timeline-header">
                  <h6 class="mb-0">تم إنشاء طلب العمولة</h6>
                  <small class="text-muted">{{ $commission->created_at->format('Y-m-d H:i') }}</small>
                </div>
              </div>
            </li>
            
            @if($commission->reviewed_at)
            <li class="timeline-item {{ $commission->status->value === 'approved' ? 'timeline-item-success' : 'timeline-item-danger' }}">
              <span class="timeline-indicator {{ $commission->status->value === 'approved' ? 'timeline-indicator-success' : 'timeline-indicator-danger' }}">
                <i class="ti {{ $commission->status->value === 'approved' ? 'ti-check' : 'ti-x' }}"></i>
              </span>
              <div class="timeline-event">
                <div class="timeline-header">
                  <h6 class="mb-0">{{ $commission->status->value === 'approved' ? 'تم قبول العمولة' : 'تم رفض العمولة' }}</h6>
                  <small class="text-muted">{{ $commission->reviewed_at->format('Y-m-d H:i') }}</small>
                </div>
                @if($commission->admin)
                <p class="mb-0">بواسطة: {{ $commission->admin->name }}</p>
                @endif
              </div>
            </li>
            @endif
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTitle">مراجعة العمولة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="reviewForm">
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">ملاحظات المراجعة</label>
            <textarea name="review_notes" class="form-control" rows="4" 
                      placeholder="أدخل ملاحظاتك هنا..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="submit" class="btn btn-primary" id="submitBtn">تأكيد</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection

@section('page-script')
<script>
let currentAction = null;

function approveCommission() {
  currentAction = 'approve';
  $('#modalTitle').text('قبول العمولة');
  $('#submitBtn').text('قبول').removeClass('btn-danger').addClass('btn-success');
  $('textarea[name="review_notes"]').attr('placeholder', 'ملاحظات اختيارية...');
  $('#reviewModal').modal('show');
}

function rejectCommission() {
  currentAction = 'reject';
  $('#modalTitle').text('رفض العمولة');
  $('#submitBtn').text('رفض').removeClass('btn-success').addClass('btn-danger');
  $('textarea[name="review_notes"]').attr('placeholder', 'سبب الرفض مطلوب...');
  $('#reviewModal').modal('show');
}

$('#reviewForm').submit(function(e) {
  e.preventDefault();
  
  const reviewNotes = $('textarea[name="review_notes"]').val();
  
  if (currentAction === 'reject' && !reviewNotes.trim()) {
    Swal.fire({
      icon: 'error',
      title: 'خطأ',
      text: 'سبب الرفض مطلوب'
    });
    return;
  }

  $.ajax({
    url: `{{ route('commissions.approve', $commission) }}`.replace('approve', currentAction),
    method: 'POST',
    data: {
      review_notes: reviewNotes,
      _token: '{{ csrf_token() }}'
    },
    success: function(response) {
      $('#reviewModal').modal('hide');
      Swal.fire({
        icon: 'success',
        title: 'تم بنجاح',
        text: response.message
      }).then(() => {
        location.reload();
      });
    },
    error: function(xhr) {
      Swal.fire({
        icon: 'error',
        title: 'خطأ',
        text: xhr.responseJSON?.message || 'حدث خطأ غير متوقع'
      });
    }
  });
});
</script>
@endsection
