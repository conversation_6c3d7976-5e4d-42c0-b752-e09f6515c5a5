<div class="table-responsive px-5">
    <div class="col-md-12 mt-4 mb-4 d-flex justify-content-between align-items-center">
        <div class="d-flex gap-2 flex-grow-1">
            <input type="text" wire:model.live.debounce.399ms="search" class="form-control"
                placeholder="{{ $searchPlaceholder }}" style="max-width: 300px;" />

            <!-- Verification Filter (only show for UsersTable) -->
            @if(property_exists($this, 'verificationFilter'))
                <select wire:model.live="verificationFilter" class="form-select" style="max-width: 200px;">
                    <option value="all">جميع المستخدمين</option>
                    <option value="verified">موثق</option>
                    <option value="unverified">غير موثق</option>
                    <option value="pending">قيد المراجعة</option>
                </select>
            @endif

            <!-- Status Filter (for verification requests table) -->
            @if(property_exists($this, 'statusFilter'))
                <select wire:model.live="statusFilter" class="form-select" style="max-width: 200px;">
                    <option value="all">جميع الطلبات</option>
                    <option value="pending">قيد المراجعة</option>
                    <option value="approved">مقبول</option>
                    <option value="rejected">مرفوض</option>
                </select>
            @endif
        </div>

        <div class="dropdown ms-2">
            <button class="btn btn-secondary" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="ti ti-filter"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                @foreach ($columns as $column)
                    <li>
                        <a class="dropdown-item" href="javascript:void(0)"
                            wire:click="toggleColumnVisibility('{{ $column['field'] }}')">
                            <input type="checkbox" @if (in_array($column['field'], $visibleColumns)) checked @endif>
                            {{ $column['title'] }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <!-- Removed the original dropdown code from here -->
    </div>
    <table class="table">
        <thead class="border-top">
            <tr>
                @foreach ($columns as $column)
                    @if (in_array($column['field'], $visibleColumns))
                        <x-datatable.table-header field="{{ $column['field'] }}" title="{{ $column['title'] }}"
                            customClass="{{ $column['class'] ?? '' }}" :sortBy="$sortField" :sortDir="$sortDirection"
                            :sortable="$column['sortable'] ?? true" />
                    @endif
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $item)
                <tr>
                    @foreach ($columns as $column)
                        @if (in_array($column['field'], $visibleColumns) && $column['field'] != 'actions')
                            <td>{!! $this->renderColumn($column, $item) !!}</td>
                        @endif
                    @endforeach
                    <td>
                        <div class="d-flex align-items-center">
                            @foreach ($this->actions as $actionType)
                                {!! $this->renderAction($actionType, $item) !!}
                            @endforeach
                        </div>
                    </td>
                </tr>
                @if (isset($updateModalView))
                    @include($updateModalView)
                @endif
            @endforeach
        </tbody>
    </table>
    @include('components.table.delete-modal')

    <div class="col-12 p-3">
        {{ $data->links() }}
    </div>
    <script>
        function openDeleteModal(item) {
            $('#deleteModal').modal('show');
            if (item.deleteMessage != undefined) {
                $('#deleteModalMessage').text(item.deleteMessage);
            }
            $('#deleteForm').attr('action', item.delete);
        }

        // Verification management functions
        function verifyUser(userId) {
            Swal.fire({
                title: 'توثيق المستخدم',
                text: 'هل أنت متأكد من توثيق هذا المستخدم؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، وثق',
                cancelButtonText: 'إلغاء',
                input: 'textarea',
                inputPlaceholder: 'ملاحظات إضافية (اختياري)',
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/verification/verify-user/${userId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            admin_notes: result.value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'تم!',
                                text: data.message,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                            Livewire.dispatch('refreshTable');
                        } else {
                            Swal.fire({
                                title: 'خطأ!',
                                text: data.message || 'حدث خطأ أثناء التوثيق',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-danger waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'خطأ!',
                            text: 'حدث خطأ في الاتصال',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-danger waves-effect waves-light'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        }

        function unverifyUser(userId) {
            Swal.fire({
                title: 'إلغاء توثيق المستخدم',
                text: 'هل أنت متأكد من إلغاء توثيق هذا المستخدم؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، ألغ التوثيق',
                cancelButtonText: 'إلغاء',
                customClass: {
                    confirmButton: 'btn btn-warning me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/verification/unverify-user/${userId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'تم!',
                                text: data.message || 'تم إلغاء توثيق المستخدم',
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                            Livewire.dispatch('refreshTable');
                        } else {
                            Swal.fire({
                                title: 'خطأ!',
                                text: data.message || 'حدث خطأ أثناء إلغاء التوثيق',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-danger waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'خطأ!',
                            text: 'حدث خطأ في الاتصال',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-danger waves-effect waves-light'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        }
    </script>
</div>
