@extends('layouts/layoutMaster')

@section('title', 'Users')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss'])

@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/@form-validation/popular.js', 'resources/assets/vendor/libs/@form-validation/bootstrap5.js', 'resources/assets/vendor/libs/@form-validation/auto-focus.js', 'resources/assets/vendor/libs/cleavejs/cleave.js', 'resources/assets/vendor/libs/cleavejs/cleave-phone.js', 'resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('page-script')
@endsection

@section('content')

    <div class="row g-6 mb-6">
        @foreach ($analyticsData as $item)
            <div class="col-sm-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-start justify-content-between">
                            <div class="content-left">
                                <span class="text-heading">{{ $item['title'] }}</span>
                                <div class="d-flex align-items-center my-1">
                                    <h4 class="mb-0 me-2">{{ $item['value'] }}</h4>
                                    @if ($item['percent'] > 0)
                                        <p class="text-{{ $item['color'] }} mb-0">({{ $item['percent'] }}%)</p>
                                    @endif
                                </div>
                                <small class="mb-0">{{ $item['description'] }}</small>
                            </div>
                            <div class="avatar">
                                <span class="avatar-initial rounded bg-label-{{ $item['color'] }}">
                                    <i class="ti ti-{{ $item['icon'] }} ti-26px"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach

    </div>
    <!-- Users List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <div class="d-flex justify-content-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createuser">
                    إضافة مستخدم جديد
                </button>
            </div>
            </div>



            @livewire('users-table')



        </div>
        @include('modules.user.modal-create')
        @livewire('verification-modal')
    @endsection
