@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'إعدادات متطلبات التوثيق')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="nav-align-top mb-4">
                <ul class="nav nav-pills mb-3" role="tablist">
                    <li class="nav-item">
                        <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab" data-bs-target="#navs-pills-top-requirements" aria-controls="navs-pills-top-requirements" aria-selected="true">
                            <i class="ti ti-settings me-1"></i>
                            متطلبات التوثيق
                        </button>
                    </li>
                    <li class="nav-item">
                        <button type="button" class="nav-link" role="tab" data-bs-toggle="tab" data-bs-target="#navs-pills-top-pending" aria-controls="navs-pills-top-pending" aria-selected="false">
                            <i class="ti ti-clock me-1"></i>
                            الطلبات المعلقة
                        </button>
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="navs-pills-top-requirements" role="tabpanel">
                        @livewire('verification-requirements-form')
                    </div>
                    <div class="tab-pane fade" id="navs-pills-top-pending" role="tabpanel">
                        @livewire('verification-requests-table')
                        @livewire('verification-modal')
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Verification Details Modal - Redesigned -->
    <div class="modal fade" id="verificationDetailsModal" tabindex="-1" aria-labelledby="verificationDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-primary bg-opacity-10 border-0">
                    <h5 class="modal-title text-primary" id="verificationDetailsModalLabel">
                        <i class="ti ti-clipboard-check me-2"></i>
                        تفاصيل طلب التوثيق
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body py-4">
                    <!-- User Info Card -->
                    <div class="card border shadow-sm mb-4">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10">
                                            <i class="ti ti-user text-primary"></i>
                                        </div>
                                        <h6 class="mb-0">معلومات المستخدم</h6>
                                    </div>
                                    <div class="ps-4">
                                        <p class="mb-2"><strong>الاسم:</strong> <span id="verification-user-name" class="text-body"></span></p>
                                        <p class="mb-2"><strong>البريد الإلكتروني:</strong> <span id="verification-user-email" class="text-body"></span></p>
                                        <p class="mb-0"><strong>نوع الحساب:</strong> <span id="verification-user-type" class="text-body"></span></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10">
                                            <i class="ti ti-clock text-primary"></i>
                                        </div>
                                        <h6 class="mb-0">معلومات الطلب</h6>
                                    </div>
                                    <div class="ps-4">
                                        <p class="mb-2"><strong>الحالة:</strong> <span id="verification-status" class="badge bg-label-info ms-1"></span></p>
                                        <p class="mb-2"><strong>تاريخ الطلب:</strong> <span id="verification-created-at" class="text-body"></span></p>
                                        <div id="verification-reviewer-info" style="display: none;">
                                            <p class="mb-2"><strong>تاريخ المراجعة:</strong> <span id="verification-reviewed-at" class="text-body"></span></p>
                                            <p class="mb-0"><strong>المراجع:</strong> <span id="verification-reviewer-name" class="text-body"></span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Admin Notes -->
                    <div id="verification-admin-notes-container" class="card border shadow-sm mb-4" style="display: none;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10">
                                    <i class="ti ti-notes text-primary"></i>
                                </div>
                                <h6 class="mb-0">ملاحظات الإدارة</h6>
                            </div>
                            <div class="p-3 bg-light rounded">
                                <p id="verification-admin-notes" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submitted Data -->
                    <div class="card border shadow-sm mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10">
                                    <i class="ti ti-forms text-primary"></i>
                                </div>
                                <h6 class="mb-0">البيانات المقدمة</h6>
                            </div>
                            <div id="verification-data-container">
                                <!-- Data will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Attached Files -->
                    <div id="verification-files-section" class="card border shadow-sm" style="display: none;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10">
                                    <i class="ti ti-files text-primary"></i>
                                </div>
                                <h6 class="mb-0">الملفات المرفقة</h6>
                            </div>
                            <div id="verification-files-container" class="pt-2">
                                <!-- Files will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
<script>
    // Verification management functions
    function verifyUser(userId) {
        Swal.fire({
            title: 'توثيق المستخدم',
            text: 'هل أنت متأكد من توثيق هذا المستخدم؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، وثق',
            cancelButtonText: 'إلغاء',
            input: 'textarea',
            inputPlaceholder: 'ملاحظات إضافية (اختياري)',
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/verify/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        admin_notes: result.value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', data.message, 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء التوثيق', 'error');
                    }
                });
            }
        });
    }

    function unverifyUser(userId) {
        Swal.fire({
            title: 'إلغاء توثيق المستخدم',
            text: 'هل أنت متأكد من إلغاء توثيق هذا المستخدم؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، ألغ التوثيق',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/unverify/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', 'تم إلغاء توثيق المستخدم', 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء إلغاء التوثيق', 'error');
                    }
                });
            }
        });
    }

    function openVerificationModal(userId) {
        // Show loading spinner
        Swal.fire({
            title: 'جاري التحميل...',
            didOpen: () => {
                Swal.showLoading();
            },
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false
        });

        fetch(`/admin/verification/details/${userId}`)
            .then(response => response.json())
            .then(data => {
                // Close loading spinner
                Swal.close();
                
                // Populate modal with verification details
                const verification = data.verification;
                const requirements = data.requirements ? JSON.parse(data.requirements.value) : [];
                
                // Set user information with modern styling
                document.getElementById('verification-user-name').textContent = verification.user.name;
                document.getElementById('verification-user-email').textContent = verification.user.email;
                document.getElementById('verification-user-type').textContent = verification.user_type;
                
                // Set status with appropriate badge color
                const statusEl = document.getElementById('verification-status');
                statusEl.textContent = verification.status;
                
                // Update badge class based on status
                statusEl.className = 'badge';
                switch(verification.status.toLowerCase()) {
                    case 'pending':
                    case 'معلق':
                        statusEl.classList.add('bg-label-warning');
                        break;
                    case 'approved':
                    case 'مقبول':
                        statusEl.classList.add('bg-label-success');
                        break;
                    case 'rejected':
                    case 'مرفوض':
                        statusEl.classList.add('bg-label-danger');
                        break;
                    default:
                        statusEl.classList.add('bg-label-info');
                }
                
                // Format dates in Gregorian format (not Hijri)
                const dateOptions = { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                
                document.getElementById('verification-created-at').textContent = 
                    new Date(verification.created_at).toLocaleDateString('ar', dateOptions);
                
                if (verification.reviewed_at) {
                    document.getElementById('verification-reviewed-at').textContent = 
                        new Date(verification.reviewed_at).toLocaleDateString('ar', dateOptions);
                    document.getElementById('verification-reviewer-info').style.display = 'block';
                    if (verification.reviewer) {
                        document.getElementById('verification-reviewer-name').textContent = verification.reviewer.name;
                    }
                } else {
                    document.getElementById('verification-reviewer-info').style.display = 'none';
                }
                
                // Admin notes with better styling
                if (verification.admin_notes) {
                    document.getElementById('verification-admin-notes').textContent = verification.admin_notes;
                    document.getElementById('verification-admin-notes-container').style.display = 'block';
                } else {
                    document.getElementById('verification-admin-notes-container').style.display = 'none';
                }
                
                // Clear previous data
                const dataContainer = document.getElementById('verification-data-container');
                dataContainer.innerHTML = '';
                
                // Add submitted data with modern card styling
                if (verification.submitted_data && Object.keys(verification.submitted_data).length > 0) {
                    for (const [key, value] of Object.entries(verification.submitted_data)) {
                        const requirement = requirements.find(r => r.title === key.replace(/_/g, ' '));
                        const fieldTitle = requirement ? requirement.title : key;
                        
                        const row = document.createElement('div');
                        row.className = 'card shadow-none border mb-3';
                        row.innerHTML = `
                            <div class="card-body py-3 px-4">
                                <h6 class="card-title mb-1">${fieldTitle}</h6>
                                <p class="card-text mb-0">${value}</p>
                            </div>
                        `;
                        dataContainer.appendChild(row);
                    }
                }
                
                // Add uploaded files with modern gallery style
                if (verification.uploaded_files && Object.keys(verification.uploaded_files).length > 0) {
                    const filesContainer = document.getElementById('verification-files-container');
                    filesContainer.innerHTML = '';
                    document.getElementById('verification-files-section').style.display = 'block';
                    
                    // Create a row for files gallery
                    const fileGalleryRow = document.createElement('div');
                    fileGalleryRow.className = 'row g-3';
                    filesContainer.appendChild(fileGalleryRow);
                    
                    for (const [key, value] of Object.entries(verification.uploaded_files)) {
                        const fileCol = document.createElement('div');
                        fileCol.className = 'col-md-6 col-lg-4';
                        
                        // Check if file is an image by extension
                        const fileExtension = value.split('.').pop().toLowerCase();
                        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension);
                        
                        // Create card for file
                        const fileCard = document.createElement('div');
                        fileCard.className = 'card h-100';
                        
                        let fileContent = '';
                        if (isImage) {
                            // Modern image card with overlay controls
                            fileContent = `
                                <div class="position-relative">
                                    <img src="/storage/${value}" class="card-img-top" style="height: 180px; object-fit: cover;" alt="${key}">
                                    <div class="position-absolute top-0 end-0 p-2">
                                        <a href="/storage/${value}" target="_blank" class="btn btn-icon btn-sm btn-primary rounded-circle" title="عرض بالحجم الكامل">
                                            <i class="ti ti-zoom-in"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title text-truncate" title="${key}">${key}</h6>
                                    <div class="d-flex gap-2 mt-2">
                                        <a href="/storage/${value}" download class="btn btn-sm btn-outline-primary w-100">
                                            <i class="ti ti-download me-1"></i> تحميل
                                        </a>
                                    </div>
                                </div>
                            `;
                        } else {
                            // Document card with icon
                            const fileIcon = getFileIcon(fileExtension);
                            fileContent = `
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="${fileIcon} fs-1 text-primary"></i>
                                    </div>
                                    <h6 class="card-title text-truncate mb-3" title="${key}">${key}</h6>
                                    <div class="d-flex gap-2">
                                        <a href="/storage/${value}" download class="btn btn-sm btn-primary w-100">
                                            <i class="ti ti-download me-1"></i> تحميل
                                        </a>
                                        <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-primary w-100">
                                            <i class="ti ti-eye me-1"></i> فتح
                                        </a>
                                    </div>
                                </div>
                            `;
                        }
                        
                        fileCard.innerHTML = fileContent;
                        fileCol.appendChild(fileCard);
                        fileGalleryRow.appendChild(fileCol);
                    }
                } else {
                    document.getElementById('verification-files-section').style.display = 'none';
                }
                
                // Show the modal
                const verificationModal = new bootstrap.Modal(document.getElementById('verificationDetailsModal'));
                verificationModal.show();
            })
            .catch(error => {
                console.error('Error fetching verification details:', error);
                Swal.close();
                Swal.fire('خطأ!', 'حدث خطأ أثناء جلب بيانات التوثيق', 'error');
            });
    }
    
    // Helper function to get appropriate icon based on file extension
    function getFileIcon(extension) {
        switch(extension) {
            case 'pdf':
                return 'ti ti-file-type-pdf';
            case 'doc':
            case 'docx':
                return 'ti ti-file-type-doc';
            case 'xls':
            case 'xlsx':
                return 'ti ti-file-type-xls';
            case 'ppt':
            case 'pptx':
                return 'ti ti-file-type-ppt';
            case 'zip':
            case 'rar':
            case '7z':
                return 'ti ti-file-zip';
            case 'txt':
                return 'ti ti-file-text';
            default:
                return 'ti ti-file';
        }
    }

    function approveVerification(verificationId) {
        Swal.fire({
            title: 'قبول طلب التوثيق',
            text: 'هل أنت متأكد من قبول هذا الطلب؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، اقبل',
            cancelButtonText: 'إلغاء',
            input: 'textarea',
            inputPlaceholder: 'ملاحظات إضافية (اختياري)',
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/approve/${verificationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        admin_notes: result.value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', data.message, 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء قبول الطلب', 'error');
                    }
                });
            }
        });
    }

    function rejectVerification(verificationId) {
        Swal.fire({
            title: 'رفض طلب التوثيق',
            text: 'يرجى إدخال سبب الرفض',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'رفض الطلب',
            cancelButtonText: 'إلغاء',
            input: 'textarea',
            inputPlaceholder: 'سبب الرفض (مطلوب)',
            inputValidator: (value) => {
                if (!value) {
                    return 'يجب إدخال سبب الرفض'
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/reject/${verificationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        admin_notes: result.value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', data.message, 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء رفض الطلب', 'error');
                    }
                });
            }
        });
    }
</script>
@endsection
