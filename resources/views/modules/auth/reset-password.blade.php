<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - حراجي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        .error {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            text-align: right;
            display: none;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 25px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعادة تعيين كلمة المرور</h1>
        <form id="resetForm">
            <input type="hidden" id="token" value="{{ $token }}">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required>
                <div class="error" id="emailError"></div>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور الجديدة</label>
                <input type="password" id="password" name="password" required>
                <div class="error" id="passwordError"></div>
            </div>
            <div class="form-group">
                <label for="password_confirmation">تأكيد كلمة المرور</label>
                <input type="password" id="password_confirmation" name="password_confirmation" required>
                <div class="error" id="confirmError"></div>
            </div>
            <button type="submit" class="btn">إعادة تعيين كلمة المرور</button>
        </form>
    </div>

    <script>
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Reset errors
            document.getElementById('emailError').style.display = 'none';
            document.getElementById('passwordError').style.display = 'none';
            document.getElementById('confirmError').style.display = 'none';
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const password_confirmation = document.getElementById('password_confirmation').value;
            const token = document.getElementById('token').value;
            
            // Validation
            let hasError = false;
            
            if (!email) {
                document.getElementById('emailError').textContent = 'البريد الإلكتروني مطلوب';
                document.getElementById('emailError').style.display = 'block';
                hasError = true;
            }
            
            if (!password) {
                document.getElementById('passwordError').textContent = 'كلمة المرور مطلوبة';
                document.getElementById('passwordError').style.display = 'block';
                hasError = true;
            } else if (password.length < 8) {
                document.getElementById('passwordError').textContent = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                document.getElementById('passwordError').style.display = 'block';
                hasError = true;
            }
            
            if (password !== password_confirmation) {
                document.getElementById('confirmError').textContent = 'كلمة المرور غير متطابقة';
                document.getElementById('confirmError').style.display = 'block';
                hasError = true;
            }
            
            if (hasError) return;
            
            // Submit form
            fetch('/api/auth/reset-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    password,
                    token
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/password/reset/success';
                } else {
                    alert(data.message || 'حدث خطأ أثناء إعادة تعيين كلمة المرور');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إعادة تعيين كلمة المرور');
            });
        });
    </script>
</body>
</html>
