<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">تقييم المستخدم</h5>
    </div>
    <div class="card-body">
        <form action="{{ route('user-rates.store') }}" method="POST">
            @csrf
            <input type="hidden" name="rated_user_id" value="{{ $user->id }}">
            
            <div class="mb-3">
                <label class="form-label">التقييم</label>
                <div class="rating-stars">
                    <div class="d-flex">
                        @for ($i = 1; $i <= 5; $i++)
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="rate" id="rate{{ $i }}" value="{{ $i }}" required>
                                <label class="form-check-label" for="rate{{ $i }}">
                                    {{ $i }} <i class="ti ti-star-filled text-warning"></i>
                                </label>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label" for="comment">تعليق (اختياري)</label>
                <textarea class="form-control" id="comment" name="comment" rows="3" maxlength="1000"></textarea>
            </div>
            
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">إرسال التقييم</button>
            </div>
        </form>
    </div>
</div>
