@extends('layouts/layoutMaster')

@section('title', 'تقييمات المستخدم - ' . $user->name)

@section('content')
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">تقييمات المستخدم: {{ $user->name }}</h5>
            <a href="{{ route('users.show', $user->id) }}" class="btn btn-secondary">العودة إلى صفحة المستخدم</a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <h4 class="me-2">متوسط التقييم:</h4>
                        <div class="d-flex align-items-center">
                            @php
                                $avgRating = $user->average_rating;
                                $fullStars = floor($avgRating);
                                $halfStar = $avgRating - $fullStars >= 0.5;
                                $emptyStars = 5 - $fullStars - ($halfStar ? 1 : 0);
                            @endphp
                            
                            @for ($i = 0; $i < $fullStars; $i++)
                                <i class="ti ti-star-filled text-warning me-1 fs-3"></i>
                            @endfor
                            
                            @if ($halfStar)
                                <i class="ti ti-star-half-filled text-warning me-1 fs-3"></i>
                            @endif
                            
                            @for ($i = 0; $i < $emptyStars; $i++)
                                <i class="ti ti-star text-muted me-1 fs-3"></i>
                            @endfor
                            
                            <span class="fs-4 ms-2">{{ number_format($avgRating, 1) }}</span>
                        </div>
                    </div>
                    <p class="mb-0">عدد التقييمات: {{ $user->ratings_count }}</p>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-3">التقييمات</h5>
                    
                    @if ($ratings->count() > 0)
                        @foreach ($ratings as $rating)
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <h6 class="mb-0">{{ $rating->rater->name }}</h6>
                                            <small class="text-muted">{{ $rating->created_at->format('Y-m-d H:i') }}</small>
                                        </div>
                                        <div>
                                            @for ($i = 1; $i <= 5; $i++)
                                                <i class="ti ti-star{{ $i <= $rating->rate ? '-filled' : '' }} {{ $i <= $rating->rate ? 'text-warning' : 'text-muted' }}"></i>
                                            @endfor
                                        </div>
                                    </div>
                                    
                                    @if ($rating->comment)
                                        <p class="mb-0">{{ $rating->comment }}</p>
                                    @else
                                        <p class="text-muted mb-0">لا يوجد تعليق</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                        
                        <div class="d-flex justify-content-center mt-4">
                            {{ $ratings->links() }}
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد تقييمات لهذا المستخدم حتى الآن.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
