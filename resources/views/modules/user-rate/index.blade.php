@extends('layouts/layoutMaster')

@section('title', 'تقييمات المستخدمين')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/@form-validation/popular.js', 'resources/assets/vendor/libs/@form-validation/bootstrap5.js', 'resources/assets/vendor/libs/@form-validation/auto-focus.js', 'resources/assets/vendor/libs/cleavejs/cleave.js', 'resources/assets/vendor/libs/cleavejs/cleave-phone.js', 'resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('page-script')
<script>
    function updateRateStatus(id, status) {
        if (confirm('هل أنت متأكد من تغيير حالة هذا التقييم؟')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `{{ url('admin/user-rates') }}/${id}/status`;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            
            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'status';
            statusInput.value = status;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'PATCH';
            
            form.appendChild(csrfToken);
            form.appendChild(statusInput);
            form.appendChild(methodInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection

@section('content')
    <div class="row g-6 mb-6">
        @foreach ($analyticsData as $item)
            <div class="col-sm-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-start justify-content-between">
                            <div class="content-left">
                                <span class="text-heading">{{ $item['title'] }}</span>
                                <div class="d-flex align-items-center my-1">
                                    <h4 class="mb-0 me-2">{{ $item['value'] }}</h4>
                                    @if ($item['percent'] > 0)
                                        <p class="text-{{ $item['color'] }} mb-0">({{ $item['percent'] }}%)</p>
                                    @endif
                                </div>
                                <small class="mb-0">{{ $item['description'] }}</small>
                            </div>
                            <div class="avatar">
                                <span class="avatar-initial rounded bg-label-{{ $item['color'] }}">
                                    <i class="ti ti-{{ $item['icon'] }} ti-26px"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- User Rates List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title mb-3">تقييمات المستخدمين</h5>
            <div class="d-flex justify-content-between align-items-center row pb-2 gap-3 gap-md-0">
                <div class="col-md-4 user_status"></div>
                <div class="col-md-8 d-flex justify-content-md-end">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ti ti-filter me-1 ti-xs"></i>
                            <span>تصفية حسب الحالة</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-status" href="javascript:void(0);" data-status="all" wire:click="$emit('setStatusFilter', 'all')">الكل</a></li>
                            <li><a class="dropdown-item filter-status" href="javascript:void(0);" data-status="pending" wire:click="$emit('setStatusFilter', 'pending')">معلق</a></li>
                            <li><a class="dropdown-item filter-status" href="javascript:void(0);" data-status="approved" wire:click="$emit('setStatusFilter', 'approved')">مقبول</a></li>
                            <li><a class="dropdown-item filter-status" href="javascript:void(0);" data-status="rejected" wire:click="$emit('setStatusFilter', 'rejected')">مرفوض</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        @livewire('user-rates-table')
    </div>
@endsection
