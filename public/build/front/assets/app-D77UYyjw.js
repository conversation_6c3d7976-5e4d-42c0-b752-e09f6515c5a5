function dn(t,n){return function(){return t.apply(n,arguments)}}const{toString:Zi}=Object.prototype,{getPrototypeOf:dt}=Object,Ue=(t=>n=>{const s=Zi.call(n);return t[s]||(t[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Z=t=>(t=t.toLowerCase(),n=>Ue(n)===t),Fe=t=>n=>typeof n===t,{isArray:de}=Array,_e=Fe("undefined");function es(t){return t!==null&&!_e(t)&&t.constructor!==null&&!_e(t.constructor)&&Q(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const pn=Z("ArrayBuffer");function ts(t){let n;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?n=ArrayBuffer.isView(t):n=t&&t.buffer&&pn(t.buffer),n}const ns=Fe("string"),Q=Fe("function"),mn=Fe("number"),qe=t=>t!==null&&typeof t=="object",rs=t=>t===!0||t===!1,Ae=t=>{if(Ue(t)!=="object")return!1;const n=dt(t);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)},is=Z("Date"),ss=Z("File"),os=Z("Blob"),as=Z("FileList"),cs=t=>qe(t)&&Q(t.pipe),us=t=>{let n;return t&&(typeof FormData=="function"&&t instanceof FormData||Q(t.append)&&((n=Ue(t))==="formdata"||n==="object"&&Q(t.toString)&&t.toString()==="[object FormData]"))},ls=Z("URLSearchParams"),[hs,fs,ds,ps]=["ReadableStream","Request","Response","Headers"].map(Z),ms=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ke(t,n,{allOwnKeys:s=!1}={}){if(t===null||typeof t>"u")return;let o,a;if(typeof t!="object"&&(t=[t]),de(t))for(o=0,a=t.length;o<a;o++)n.call(null,t[o],o,t);else{const u=s?Object.getOwnPropertyNames(t):Object.keys(t),l=u.length;let p;for(o=0;o<l;o++)p=u[o],n.call(null,t[p],p,t)}}function gn(t,n){n=n.toLowerCase();const s=Object.keys(t);let o=s.length,a;for(;o-- >0;)if(a=s[o],n===a.toLowerCase())return a;return null}const ue=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,bn=t=>!_e(t)&&t!==ue;function st(){const{caseless:t}=bn(this)&&this||{},n={},s=(o,a)=>{const u=t&&gn(n,a)||a;Ae(n[u])&&Ae(o)?n[u]=st(n[u],o):Ae(o)?n[u]=st({},o):de(o)?n[u]=o.slice():n[u]=o};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&ke(arguments[o],s);return n}const gs=(t,n,s,{allOwnKeys:o}={})=>(ke(n,(a,u)=>{s&&Q(a)?t[u]=dn(a,s):t[u]=a},{allOwnKeys:o}),t),bs=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),ys=(t,n,s,o)=>{t.prototype=Object.create(n.prototype,o),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:n.prototype}),s&&Object.assign(t.prototype,s)},vs=(t,n,s,o)=>{let a,u,l;const p={};if(n=n||{},t==null)return n;do{for(a=Object.getOwnPropertyNames(t),u=a.length;u-- >0;)l=a[u],(!o||o(l,t,n))&&!p[l]&&(n[l]=t[l],p[l]=!0);t=s!==!1&&dt(t)}while(t&&(!s||s(t,n))&&t!==Object.prototype);return n},ws=(t,n,s)=>{t=String(t),(s===void 0||s>t.length)&&(s=t.length),s-=n.length;const o=t.indexOf(n,s);return o!==-1&&o===s},_s=t=>{if(!t)return null;if(de(t))return t;let n=t.length;if(!mn(n))return null;const s=new Array(n);for(;n-- >0;)s[n]=t[n];return s},Ss=(t=>n=>t&&n instanceof t)(typeof Uint8Array<"u"&&dt(Uint8Array)),ks=(t,n)=>{const o=(t&&t[Symbol.iterator]).call(t);let a;for(;(a=o.next())&&!a.done;){const u=a.value;n.call(t,u[0],u[1])}},Cs=(t,n)=>{let s;const o=[];for(;(s=t.exec(n))!==null;)o.push(s);return o},Ts=Z("HTMLFormElement"),Es=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,o,a){return o.toUpperCase()+a}),Vt=(({hasOwnProperty:t})=>(n,s)=>t.call(n,s))(Object.prototype),xs=Z("RegExp"),yn=(t,n)=>{const s=Object.getOwnPropertyDescriptors(t),o={};ke(s,(a,u)=>{let l;(l=n(a,u,t))!==!1&&(o[u]=l||a)}),Object.defineProperties(t,o)},Ps=t=>{yn(t,(n,s)=>{if(Q(t)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const o=t[s];if(Q(o)){if(n.enumerable=!1,"writable"in n){n.writable=!1;return}n.set||(n.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Rs=(t,n)=>{const s={},o=a=>{a.forEach(u=>{s[u]=!0})};return de(t)?o(t):o(String(t).split(n)),s},Os=()=>{},As=(t,n)=>t!=null&&Number.isFinite(t=+t)?t:n,tt="abcdefghijklmnopqrstuvwxyz",Kt="0123456789",vn={DIGIT:Kt,ALPHA:tt,ALPHA_DIGIT:tt+tt.toUpperCase()+Kt},Ls=(t=16,n=vn.ALPHA_DIGIT)=>{let s="";const{length:o}=n;for(;t--;)s+=n[Math.random()*o|0];return s};function Ns(t){return!!(t&&Q(t.append)&&t[Symbol.toStringTag]==="FormData"&&t[Symbol.iterator])}const Is=t=>{const n=new Array(10),s=(o,a)=>{if(qe(o)){if(n.indexOf(o)>=0)return;if(!("toJSON"in o)){n[a]=o;const u=de(o)?[]:{};return ke(o,(l,p)=>{const b=s(l,a+1);!_e(b)&&(u[p]=b)}),n[a]=void 0,u}}return o};return s(t,0)},js=Z("AsyncFunction"),Ds=t=>t&&(qe(t)||Q(t))&&Q(t.then)&&Q(t.catch),wn=((t,n)=>t?setImmediate:n?((s,o)=>(ue.addEventListener("message",({source:a,data:u})=>{a===ue&&u===s&&o.length&&o.shift()()},!1),a=>{o.push(a),ue.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Q(ue.postMessage)),Us=typeof queueMicrotask<"u"?queueMicrotask.bind(ue):typeof process<"u"&&process.nextTick||wn,f={isArray:de,isArrayBuffer:pn,isBuffer:es,isFormData:us,isArrayBufferView:ts,isString:ns,isNumber:mn,isBoolean:rs,isObject:qe,isPlainObject:Ae,isReadableStream:hs,isRequest:fs,isResponse:ds,isHeaders:ps,isUndefined:_e,isDate:is,isFile:ss,isBlob:os,isRegExp:xs,isFunction:Q,isStream:cs,isURLSearchParams:ls,isTypedArray:Ss,isFileList:as,forEach:ke,merge:st,extend:gs,trim:ms,stripBOM:bs,inherits:ys,toFlatObject:vs,kindOf:Ue,kindOfTest:Z,endsWith:ws,toArray:_s,forEachEntry:ks,matchAll:Cs,isHTMLForm:Ts,hasOwnProperty:Vt,hasOwnProp:Vt,reduceDescriptors:yn,freezeMethods:Ps,toObjectSet:Rs,toCamelCase:Es,noop:Os,toFiniteNumber:As,findKey:gn,global:ue,isContextDefined:bn,ALPHABET:vn,generateString:Ls,isSpecCompliantForm:Ns,toJSONObject:Is,isAsyncFn:js,isThenable:Ds,setImmediate:wn,asap:Us};function A(t,n,s,o,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",n&&(this.code=n),s&&(this.config=s),o&&(this.request=o),a&&(this.response=a,this.status=a.status?a.status:null)}f.inherits(A,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const _n=A.prototype,Sn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Sn[t]={value:t}});Object.defineProperties(A,Sn);Object.defineProperty(_n,"isAxiosError",{value:!0});A.from=(t,n,s,o,a,u)=>{const l=Object.create(_n);return f.toFlatObject(t,l,function(b){return b!==Error.prototype},p=>p!=="isAxiosError"),A.call(l,t.message,n,s,o,a),l.cause=t,l.name=t.name,u&&Object.assign(l,u),l};const Fs=null;function ot(t){return f.isPlainObject(t)||f.isArray(t)}function kn(t){return f.endsWith(t,"[]")?t.slice(0,-2):t}function Gt(t,n,s){return t?t.concat(n).map(function(a,u){return a=kn(a),!s&&u?"["+a+"]":a}).join(s?".":""):n}function qs(t){return f.isArray(t)&&!t.some(ot)}const Bs=f.toFlatObject(f,{},null,function(n){return/^is[A-Z]/.test(n)});function Be(t,n,s){if(!f.isObject(t))throw new TypeError("target must be an object");n=n||new FormData,s=f.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,d){return!f.isUndefined(d[v])});const o=s.metaTokens,a=s.visitor||g,u=s.dots,l=s.indexes,b=(s.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(n);if(!f.isFunction(a))throw new TypeError("visitor must be a function");function y(_){if(_===null)return"";if(f.isDate(_))return _.toISOString();if(!b&&f.isBlob(_))throw new A("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(_)||f.isTypedArray(_)?b&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function g(_,v,d){let w=_;if(_&&!d&&typeof _=="object"){if(f.endsWith(v,"{}"))v=o?v:v.slice(0,-2),_=JSON.stringify(_);else if(f.isArray(_)&&qs(_)||(f.isFileList(_)||f.endsWith(v,"[]"))&&(w=f.toArray(_)))return v=kn(v),w.forEach(function(P,j){!(f.isUndefined(P)||P===null)&&n.append(l===!0?Gt([v],j,u):l===null?v:v+"[]",y(P))}),!1}return ot(_)?!0:(n.append(Gt(d,v,u),y(_)),!1)}const k=[],E=Object.assign(Bs,{defaultVisitor:g,convertValue:y,isVisitable:ot});function x(_,v){if(!f.isUndefined(_)){if(k.indexOf(_)!==-1)throw Error("Circular reference detected in "+v.join("."));k.push(_),f.forEach(_,function(w,C){(!(f.isUndefined(w)||w===null)&&a.call(n,w,f.isString(C)?C.trim():C,v,E))===!0&&x(w,v?v.concat(C):[C])}),k.pop()}}if(!f.isObject(t))throw new TypeError("data must be an object");return x(t),n}function Qt(t){const n={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(o){return n[o]})}function pt(t,n){this._pairs=[],t&&Be(t,this,n)}const Cn=pt.prototype;Cn.append=function(n,s){this._pairs.push([n,s])};Cn.toString=function(n){const s=n?function(o){return n.call(this,o,Qt)}:Qt;return this._pairs.map(function(a){return s(a[0])+"="+s(a[1])},"").join("&")};function Hs(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Tn(t,n,s){if(!n)return t;const o=s&&s.encode||Hs,a=s&&s.serialize;let u;if(a?u=a(n,s):u=f.isURLSearchParams(n)?n.toString():new pt(n,s).toString(o),u){const l=t.indexOf("#");l!==-1&&(t=t.slice(0,l)),t+=(t.indexOf("?")===-1?"?":"&")+u}return t}class Yt{constructor(){this.handlers=[]}use(n,s,o){return this.handlers.push({fulfilled:n,rejected:s,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(n){this.handlers[n]&&(this.handlers[n]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(n){f.forEach(this.handlers,function(o){o!==null&&n(o)})}}const En={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ms=typeof URLSearchParams<"u"?URLSearchParams:pt,zs=typeof FormData<"u"?FormData:null,Js=typeof Blob<"u"?Blob:null,$s={isBrowser:!0,classes:{URLSearchParams:Ms,FormData:zs,Blob:Js},protocols:["http","https","file","blob","url","data"]},mt=typeof window<"u"&&typeof document<"u",at=typeof navigator=="object"&&navigator||void 0,Xs=mt&&(!at||["ReactNative","NativeScript","NS"].indexOf(at.product)<0),Ws=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Vs=mt&&window.location.href||"http://localhost",Ks=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:mt,hasStandardBrowserEnv:Xs,hasStandardBrowserWebWorkerEnv:Ws,navigator:at,origin:Vs},Symbol.toStringTag,{value:"Module"})),W={...Ks,...$s};function Gs(t,n){return Be(t,new W.classes.URLSearchParams,Object.assign({visitor:function(s,o,a,u){return W.isNode&&f.isBuffer(s)?(this.append(o,s.toString("base64")),!1):u.defaultVisitor.apply(this,arguments)}},n))}function Qs(t){return f.matchAll(/\w+|\[(\w*)]/g,t).map(n=>n[0]==="[]"?"":n[1]||n[0])}function Ys(t){const n={},s=Object.keys(t);let o;const a=s.length;let u;for(o=0;o<a;o++)u=s[o],n[u]=t[u];return n}function xn(t){function n(s,o,a,u){let l=s[u++];if(l==="__proto__")return!0;const p=Number.isFinite(+l),b=u>=s.length;return l=!l&&f.isArray(a)?a.length:l,b?(f.hasOwnProp(a,l)?a[l]=[a[l],o]:a[l]=o,!p):((!a[l]||!f.isObject(a[l]))&&(a[l]=[]),n(s,o,a[l],u)&&f.isArray(a[l])&&(a[l]=Ys(a[l])),!p)}if(f.isFormData(t)&&f.isFunction(t.entries)){const s={};return f.forEachEntry(t,(o,a)=>{n(Qs(o),a,s,0)}),s}return null}function Zs(t,n,s){if(f.isString(t))try{return(n||JSON.parse)(t),f.trim(t)}catch(o){if(o.name!=="SyntaxError")throw o}return(0,JSON.stringify)(t)}const Ce={transitional:En,adapter:["xhr","http","fetch"],transformRequest:[function(n,s){const o=s.getContentType()||"",a=o.indexOf("application/json")>-1,u=f.isObject(n);if(u&&f.isHTMLForm(n)&&(n=new FormData(n)),f.isFormData(n))return a?JSON.stringify(xn(n)):n;if(f.isArrayBuffer(n)||f.isBuffer(n)||f.isStream(n)||f.isFile(n)||f.isBlob(n)||f.isReadableStream(n))return n;if(f.isArrayBufferView(n))return n.buffer;if(f.isURLSearchParams(n))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),n.toString();let p;if(u){if(o.indexOf("application/x-www-form-urlencoded")>-1)return Gs(n,this.formSerializer).toString();if((p=f.isFileList(n))||o.indexOf("multipart/form-data")>-1){const b=this.env&&this.env.FormData;return Be(p?{"files[]":n}:n,b&&new b,this.formSerializer)}}return u||a?(s.setContentType("application/json",!1),Zs(n)):n}],transformResponse:[function(n){const s=this.transitional||Ce.transitional,o=s&&s.forcedJSONParsing,a=this.responseType==="json";if(f.isResponse(n)||f.isReadableStream(n))return n;if(n&&f.isString(n)&&(o&&!this.responseType||a)){const l=!(s&&s.silentJSONParsing)&&a;try{return JSON.parse(n)}catch(p){if(l)throw p.name==="SyntaxError"?A.from(p,A.ERR_BAD_RESPONSE,this,null,this.response):p}}return n}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:W.classes.FormData,Blob:W.classes.Blob},validateStatus:function(n){return n>=200&&n<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],t=>{Ce.headers[t]={}});const eo=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),to=t=>{const n={};let s,o,a;return t&&t.split(`
`).forEach(function(l){a=l.indexOf(":"),s=l.substring(0,a).trim().toLowerCase(),o=l.substring(a+1).trim(),!(!s||n[s]&&eo[s])&&(s==="set-cookie"?n[s]?n[s].push(o):n[s]=[o]:n[s]=n[s]?n[s]+", "+o:o)}),n},Zt=Symbol("internals");function ve(t){return t&&String(t).trim().toLowerCase()}function Le(t){return t===!1||t==null?t:f.isArray(t)?t.map(Le):String(t)}function no(t){const n=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=s.exec(t);)n[o[1]]=o[2];return n}const ro=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function nt(t,n,s,o,a){if(f.isFunction(o))return o.call(this,n,s);if(a&&(n=s),!!f.isString(n)){if(f.isString(o))return n.indexOf(o)!==-1;if(f.isRegExp(o))return o.test(n)}}function so(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(n,s,o)=>s.toUpperCase()+o)}function oo(t,n){const s=f.toCamelCase(" "+n);["get","set","has"].forEach(o=>{Object.defineProperty(t,o+s,{value:function(a,u,l){return this[o].call(this,n,a,u,l)},configurable:!0})})}class V{constructor(n){n&&this.set(n)}set(n,s,o){const a=this;function u(p,b,y){const g=ve(b);if(!g)throw new Error("header name must be a non-empty string");const k=f.findKey(a,g);(!k||a[k]===void 0||y===!0||y===void 0&&a[k]!==!1)&&(a[k||b]=Le(p))}const l=(p,b)=>f.forEach(p,(y,g)=>u(y,g,b));if(f.isPlainObject(n)||n instanceof this.constructor)l(n,s);else if(f.isString(n)&&(n=n.trim())&&!ro(n))l(to(n),s);else if(f.isHeaders(n))for(const[p,b]of n.entries())u(b,p,o);else n!=null&&u(s,n,o);return this}get(n,s){if(n=ve(n),n){const o=f.findKey(this,n);if(o){const a=this[o];if(!s)return a;if(s===!0)return no(a);if(f.isFunction(s))return s.call(this,a,o);if(f.isRegExp(s))return s.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(n,s){if(n=ve(n),n){const o=f.findKey(this,n);return!!(o&&this[o]!==void 0&&(!s||nt(this,this[o],o,s)))}return!1}delete(n,s){const o=this;let a=!1;function u(l){if(l=ve(l),l){const p=f.findKey(o,l);p&&(!s||nt(o,o[p],p,s))&&(delete o[p],a=!0)}}return f.isArray(n)?n.forEach(u):u(n),a}clear(n){const s=Object.keys(this);let o=s.length,a=!1;for(;o--;){const u=s[o];(!n||nt(this,this[u],u,n,!0))&&(delete this[u],a=!0)}return a}normalize(n){const s=this,o={};return f.forEach(this,(a,u)=>{const l=f.findKey(o,u);if(l){s[l]=Le(a),delete s[u];return}const p=n?so(u):String(u).trim();p!==u&&delete s[u],s[p]=Le(a),o[p]=!0}),this}concat(...n){return this.constructor.concat(this,...n)}toJSON(n){const s=Object.create(null);return f.forEach(this,(o,a)=>{o!=null&&o!==!1&&(s[a]=n&&f.isArray(o)?o.join(", "):o)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([n,s])=>n+": "+s).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(n){return n instanceof this?n:new this(n)}static concat(n,...s){const o=new this(n);return s.forEach(a=>o.set(a)),o}static accessor(n){const o=(this[Zt]=this[Zt]={accessors:{}}).accessors,a=this.prototype;function u(l){const p=ve(l);o[p]||(oo(a,l),o[p]=!0)}return f.isArray(n)?n.forEach(u):u(n),this}}V.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(V.prototype,({value:t},n)=>{let s=n[0].toUpperCase()+n.slice(1);return{get:()=>t,set(o){this[s]=o}}});f.freezeMethods(V);function rt(t,n){const s=this||Ce,o=n||s,a=V.from(o.headers);let u=o.data;return f.forEach(t,function(p){u=p.call(s,u,a.normalize(),n?n.status:void 0)}),a.normalize(),u}function Pn(t){return!!(t&&t.__CANCEL__)}function pe(t,n,s){A.call(this,t??"canceled",A.ERR_CANCELED,n,s),this.name="CanceledError"}f.inherits(pe,A,{__CANCEL__:!0});function Rn(t,n,s){const o=s.config.validateStatus;!s.status||!o||o(s.status)?t(s):n(new A("Request failed with status code "+s.status,[A.ERR_BAD_REQUEST,A.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function ao(t){const n=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return n&&n[1]||""}function co(t,n){t=t||10;const s=new Array(t),o=new Array(t);let a=0,u=0,l;return n=n!==void 0?n:1e3,function(b){const y=Date.now(),g=o[u];l||(l=y),s[a]=b,o[a]=y;let k=u,E=0;for(;k!==a;)E+=s[k++],k=k%t;if(a=(a+1)%t,a===u&&(u=(u+1)%t),y-l<n)return;const x=g&&y-g;return x?Math.round(E*1e3/x):void 0}}function uo(t,n){let s=0,o=1e3/n,a,u;const l=(y,g=Date.now())=>{s=g,a=null,u&&(clearTimeout(u),u=null),t.apply(null,y)};return[(...y)=>{const g=Date.now(),k=g-s;k>=o?l(y,g):(a=y,u||(u=setTimeout(()=>{u=null,l(a)},o-k)))},()=>a&&l(a)]}const Ne=(t,n,s=3)=>{let o=0;const a=co(50,250);return uo(u=>{const l=u.loaded,p=u.lengthComputable?u.total:void 0,b=l-o,y=a(b),g=l<=p;o=l;const k={loaded:l,total:p,progress:p?l/p:void 0,bytes:b,rate:y||void 0,estimated:y&&p&&g?(p-l)/y:void 0,event:u,lengthComputable:p!=null,[n?"download":"upload"]:!0};t(k)},s)},en=(t,n)=>{const s=t!=null;return[o=>n[0]({lengthComputable:s,total:t,loaded:o}),n[1]]},tn=t=>(...n)=>f.asap(()=>t(...n)),lo=W.hasStandardBrowserEnv?function(){const n=W.navigator&&/(msie|trident)/i.test(W.navigator.userAgent),s=document.createElement("a");let o;function a(u){let l=u;return n&&(s.setAttribute("href",l),l=s.href),s.setAttribute("href",l),{href:s.href,protocol:s.protocol?s.protocol.replace(/:$/,""):"",host:s.host,search:s.search?s.search.replace(/^\?/,""):"",hash:s.hash?s.hash.replace(/^#/,""):"",hostname:s.hostname,port:s.port,pathname:s.pathname.charAt(0)==="/"?s.pathname:"/"+s.pathname}}return o=a(window.location.href),function(l){const p=f.isString(l)?a(l):l;return p.protocol===o.protocol&&p.host===o.host}}():function(){return function(){return!0}}(),ho=W.hasStandardBrowserEnv?{write(t,n,s,o,a,u){const l=[t+"="+encodeURIComponent(n)];f.isNumber(s)&&l.push("expires="+new Date(s).toGMTString()),f.isString(o)&&l.push("path="+o),f.isString(a)&&l.push("domain="+a),u===!0&&l.push("secure"),document.cookie=l.join("; ")},read(t){const n=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return n?decodeURIComponent(n[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function fo(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function po(t,n){return n?t.replace(/\/?\/$/,"")+"/"+n.replace(/^\/+/,""):t}function On(t,n){return t&&!fo(n)?po(t,n):n}const nn=t=>t instanceof V?{...t}:t;function he(t,n){n=n||{};const s={};function o(y,g,k){return f.isPlainObject(y)&&f.isPlainObject(g)?f.merge.call({caseless:k},y,g):f.isPlainObject(g)?f.merge({},g):f.isArray(g)?g.slice():g}function a(y,g,k){if(f.isUndefined(g)){if(!f.isUndefined(y))return o(void 0,y,k)}else return o(y,g,k)}function u(y,g){if(!f.isUndefined(g))return o(void 0,g)}function l(y,g){if(f.isUndefined(g)){if(!f.isUndefined(y))return o(void 0,y)}else return o(void 0,g)}function p(y,g,k){if(k in n)return o(y,g);if(k in t)return o(void 0,y)}const b={url:u,method:u,data:u,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:p,headers:(y,g)=>a(nn(y),nn(g),!0)};return f.forEach(Object.keys(Object.assign({},t,n)),function(g){const k=b[g]||a,E=k(t[g],n[g],g);f.isUndefined(E)&&k!==p||(s[g]=E)}),s}const An=t=>{const n=he({},t);let{data:s,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:u,headers:l,auth:p}=n;n.headers=l=V.from(l),n.url=Tn(On(n.baseURL,n.url),t.params,t.paramsSerializer),p&&l.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):"")));let b;if(f.isFormData(s)){if(W.hasStandardBrowserEnv||W.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((b=l.getContentType())!==!1){const[y,...g]=b?b.split(";").map(k=>k.trim()).filter(Boolean):[];l.setContentType([y||"multipart/form-data",...g].join("; "))}}if(W.hasStandardBrowserEnv&&(o&&f.isFunction(o)&&(o=o(n)),o||o!==!1&&lo(n.url))){const y=a&&u&&ho.read(u);y&&l.set(a,y)}return n},mo=typeof XMLHttpRequest<"u",go=mo&&function(t){return new Promise(function(s,o){const a=An(t);let u=a.data;const l=V.from(a.headers).normalize();let{responseType:p,onUploadProgress:b,onDownloadProgress:y}=a,g,k,E,x,_;function v(){x&&x(),_&&_(),a.cancelToken&&a.cancelToken.unsubscribe(g),a.signal&&a.signal.removeEventListener("abort",g)}let d=new XMLHttpRequest;d.open(a.method.toUpperCase(),a.url,!0),d.timeout=a.timeout;function w(){if(!d)return;const P=V.from("getAllResponseHeaders"in d&&d.getAllResponseHeaders()),N={data:!p||p==="text"||p==="json"?d.responseText:d.response,status:d.status,statusText:d.statusText,headers:P,config:t,request:d};Rn(function(M){s(M),v()},function(M){o(M),v()},N),d=null}"onloadend"in d?d.onloadend=w:d.onreadystatechange=function(){!d||d.readyState!==4||d.status===0&&!(d.responseURL&&d.responseURL.indexOf("file:")===0)||setTimeout(w)},d.onabort=function(){d&&(o(new A("Request aborted",A.ECONNABORTED,t,d)),d=null)},d.onerror=function(){o(new A("Network Error",A.ERR_NETWORK,t,d)),d=null},d.ontimeout=function(){let j=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const N=a.transitional||En;a.timeoutErrorMessage&&(j=a.timeoutErrorMessage),o(new A(j,N.clarifyTimeoutError?A.ETIMEDOUT:A.ECONNABORTED,t,d)),d=null},u===void 0&&l.setContentType(null),"setRequestHeader"in d&&f.forEach(l.toJSON(),function(j,N){d.setRequestHeader(N,j)}),f.isUndefined(a.withCredentials)||(d.withCredentials=!!a.withCredentials),p&&p!=="json"&&(d.responseType=a.responseType),y&&([E,_]=Ne(y,!0),d.addEventListener("progress",E)),b&&d.upload&&([k,x]=Ne(b),d.upload.addEventListener("progress",k),d.upload.addEventListener("loadend",x)),(a.cancelToken||a.signal)&&(g=P=>{d&&(o(!P||P.type?new pe(null,t,d):P),d.abort(),d=null)},a.cancelToken&&a.cancelToken.subscribe(g),a.signal&&(a.signal.aborted?g():a.signal.addEventListener("abort",g)));const C=ao(a.url);if(C&&W.protocols.indexOf(C)===-1){o(new A("Unsupported protocol "+C+":",A.ERR_BAD_REQUEST,t));return}d.send(u||null)})},bo=(t,n)=>{const{length:s}=t=t?t.filter(Boolean):[];if(n||s){let o=new AbortController,a;const u=function(y){if(!a){a=!0,p();const g=y instanceof Error?y:this.reason;o.abort(g instanceof A?g:new pe(g instanceof Error?g.message:g))}};let l=n&&setTimeout(()=>{l=null,u(new A(`timeout ${n} of ms exceeded`,A.ETIMEDOUT))},n);const p=()=>{t&&(l&&clearTimeout(l),l=null,t.forEach(y=>{y.unsubscribe?y.unsubscribe(u):y.removeEventListener("abort",u)}),t=null)};t.forEach(y=>y.addEventListener("abort",u));const{signal:b}=o;return b.unsubscribe=()=>f.asap(p),b}},yo=function*(t,n){let s=t.byteLength;if(s<n){yield t;return}let o=0,a;for(;o<s;)a=o+n,yield t.slice(o,a),o=a},vo=async function*(t,n){for await(const s of wo(t))yield*yo(s,n)},wo=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const n=t.getReader();try{for(;;){const{done:s,value:o}=await n.read();if(s)break;yield o}}finally{await n.cancel()}},rn=(t,n,s,o)=>{const a=vo(t,n);let u=0,l,p=b=>{l||(l=!0,o&&o(b))};return new ReadableStream({async pull(b){try{const{done:y,value:g}=await a.next();if(y){p(),b.close();return}let k=g.byteLength;if(s){let E=u+=k;s(E)}b.enqueue(new Uint8Array(g))}catch(y){throw p(y),y}},cancel(b){return p(b),a.return()}},{highWaterMark:2})},He=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ln=He&&typeof ReadableStream=="function",_o=He&&(typeof TextEncoder=="function"?(t=>n=>t.encode(n))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Nn=(t,...n)=>{try{return!!t(...n)}catch{return!1}},So=Ln&&Nn(()=>{let t=!1;const n=new Request(W.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!n}),sn=64*1024,ct=Ln&&Nn(()=>f.isReadableStream(new Response("").body)),Ie={stream:ct&&(t=>t.body)};He&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(n=>{!Ie[n]&&(Ie[n]=f.isFunction(t[n])?s=>s[n]():(s,o)=>{throw new A(`Response type '${n}' is not supported`,A.ERR_NOT_SUPPORT,o)})})})(new Response);const ko=async t=>{if(t==null)return 0;if(f.isBlob(t))return t.size;if(f.isSpecCompliantForm(t))return(await new Request(W.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(f.isArrayBufferView(t)||f.isArrayBuffer(t))return t.byteLength;if(f.isURLSearchParams(t)&&(t=t+""),f.isString(t))return(await _o(t)).byteLength},Co=async(t,n)=>{const s=f.toFiniteNumber(t.getContentLength());return s??ko(n)},To=He&&(async t=>{let{url:n,method:s,data:o,signal:a,cancelToken:u,timeout:l,onDownloadProgress:p,onUploadProgress:b,responseType:y,headers:g,withCredentials:k="same-origin",fetchOptions:E}=An(t);y=y?(y+"").toLowerCase():"text";let x=bo([a,u&&u.toAbortSignal()],l),_;const v=x&&x.unsubscribe&&(()=>{x.unsubscribe()});let d;try{if(b&&So&&s!=="get"&&s!=="head"&&(d=await Co(g,o))!==0){let N=new Request(n,{method:"POST",body:o,duplex:"half"}),U;if(f.isFormData(o)&&(U=N.headers.get("content-type"))&&g.setContentType(U),N.body){const[M,z]=en(d,Ne(tn(b)));o=rn(N.body,sn,M,z)}}f.isString(k)||(k=k?"include":"omit");const w="credentials"in Request.prototype;_=new Request(n,{...E,signal:x,method:s.toUpperCase(),headers:g.normalize().toJSON(),body:o,duplex:"half",credentials:w?k:void 0});let C=await fetch(_);const P=ct&&(y==="stream"||y==="response");if(ct&&(p||P&&v)){const N={};["status","statusText","headers"].forEach(Y=>{N[Y]=C[Y]});const U=f.toFiniteNumber(C.headers.get("content-length")),[M,z]=p&&en(U,Ne(tn(p),!0))||[];C=new Response(rn(C.body,sn,M,()=>{z&&z(),v&&v()}),N)}y=y||"text";let j=await Ie[f.findKey(Ie,y)||"text"](C,t);return!P&&v&&v(),await new Promise((N,U)=>{Rn(N,U,{data:j,headers:V.from(C.headers),status:C.status,statusText:C.statusText,config:t,request:_})})}catch(w){throw v&&v(),w&&w.name==="TypeError"&&/fetch/i.test(w.message)?Object.assign(new A("Network Error",A.ERR_NETWORK,t,_),{cause:w.cause||w}):A.from(w,w&&w.code,t,_)}}),ut={http:Fs,xhr:go,fetch:To};f.forEach(ut,(t,n)=>{if(t){try{Object.defineProperty(t,"name",{value:n})}catch{}Object.defineProperty(t,"adapterName",{value:n})}});const on=t=>`- ${t}`,Eo=t=>f.isFunction(t)||t===null||t===!1,In={getAdapter:t=>{t=f.isArray(t)?t:[t];const{length:n}=t;let s,o;const a={};for(let u=0;u<n;u++){s=t[u];let l;if(o=s,!Eo(s)&&(o=ut[(l=String(s)).toLowerCase()],o===void 0))throw new A(`Unknown adapter '${l}'`);if(o)break;a[l||"#"+u]=o}if(!o){const u=Object.entries(a).map(([p,b])=>`adapter ${p} `+(b===!1?"is not supported by the environment":"is not available in the build"));let l=n?u.length>1?`since :
`+u.map(on).join(`
`):" "+on(u[0]):"as no adapter specified";throw new A("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return o},adapters:ut};function it(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new pe(null,t)}function an(t){return it(t),t.headers=V.from(t.headers),t.data=rt.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),In.getAdapter(t.adapter||Ce.adapter)(t).then(function(o){return it(t),o.data=rt.call(t,t.transformResponse,o),o.headers=V.from(o.headers),o},function(o){return Pn(o)||(it(t),o&&o.response&&(o.response.data=rt.call(t,t.transformResponse,o.response),o.response.headers=V.from(o.response.headers))),Promise.reject(o)})}const jn="1.7.7",gt={};["object","boolean","number","function","string","symbol"].forEach((t,n)=>{gt[t]=function(o){return typeof o===t||"a"+(n<1?"n ":" ")+t}});const cn={};gt.transitional=function(n,s,o){function a(u,l){return"[Axios v"+jn+"] Transitional option '"+u+"'"+l+(o?". "+o:"")}return(u,l,p)=>{if(n===!1)throw new A(a(l," has been removed"+(s?" in "+s:"")),A.ERR_DEPRECATED);return s&&!cn[l]&&(cn[l]=!0,console.warn(a(l," has been deprecated since v"+s+" and will be removed in the near future"))),n?n(u,l,p):!0}};function xo(t,n,s){if(typeof t!="object")throw new A("options must be an object",A.ERR_BAD_OPTION_VALUE);const o=Object.keys(t);let a=o.length;for(;a-- >0;){const u=o[a],l=n[u];if(l){const p=t[u],b=p===void 0||l(p,u,t);if(b!==!0)throw new A("option "+u+" must be "+b,A.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new A("Unknown option "+u,A.ERR_BAD_OPTION)}}const lt={assertOptions:xo,validators:gt},ie=lt.validators;class le{constructor(n){this.defaults=n,this.interceptors={request:new Yt,response:new Yt}}async request(n,s){try{return await this._request(n,s)}catch(o){if(o instanceof Error){let a;Error.captureStackTrace?Error.captureStackTrace(a={}):a=new Error;const u=a.stack?a.stack.replace(/^.+\n/,""):"";try{o.stack?u&&!String(o.stack).endsWith(u.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+u):o.stack=u}catch{}}throw o}}_request(n,s){typeof n=="string"?(s=s||{},s.url=n):s=n||{},s=he(this.defaults,s);const{transitional:o,paramsSerializer:a,headers:u}=s;o!==void 0&&lt.assertOptions(o,{silentJSONParsing:ie.transitional(ie.boolean),forcedJSONParsing:ie.transitional(ie.boolean),clarifyTimeoutError:ie.transitional(ie.boolean)},!1),a!=null&&(f.isFunction(a)?s.paramsSerializer={serialize:a}:lt.assertOptions(a,{encode:ie.function,serialize:ie.function},!0)),s.method=(s.method||this.defaults.method||"get").toLowerCase();let l=u&&f.merge(u.common,u[s.method]);u&&f.forEach(["delete","get","head","post","put","patch","common"],_=>{delete u[_]}),s.headers=V.concat(l,u);const p=[];let b=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(s)===!1||(b=b&&v.synchronous,p.unshift(v.fulfilled,v.rejected))});const y=[];this.interceptors.response.forEach(function(v){y.push(v.fulfilled,v.rejected)});let g,k=0,E;if(!b){const _=[an.bind(this),void 0];for(_.unshift.apply(_,p),_.push.apply(_,y),E=_.length,g=Promise.resolve(s);k<E;)g=g.then(_[k++],_[k++]);return g}E=p.length;let x=s;for(k=0;k<E;){const _=p[k++],v=p[k++];try{x=_(x)}catch(d){v.call(this,d);break}}try{g=an.call(this,x)}catch(_){return Promise.reject(_)}for(k=0,E=y.length;k<E;)g=g.then(y[k++],y[k++]);return g}getUri(n){n=he(this.defaults,n);const s=On(n.baseURL,n.url);return Tn(s,n.params,n.paramsSerializer)}}f.forEach(["delete","get","head","options"],function(n){le.prototype[n]=function(s,o){return this.request(he(o||{},{method:n,url:s,data:(o||{}).data}))}});f.forEach(["post","put","patch"],function(n){function s(o){return function(u,l,p){return this.request(he(p||{},{method:n,headers:o?{"Content-Type":"multipart/form-data"}:{},url:u,data:l}))}}le.prototype[n]=s(),le.prototype[n+"Form"]=s(!0)});class bt{constructor(n){if(typeof n!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(u){s=u});const o=this;this.promise.then(a=>{if(!o._listeners)return;let u=o._listeners.length;for(;u-- >0;)o._listeners[u](a);o._listeners=null}),this.promise.then=a=>{let u;const l=new Promise(p=>{o.subscribe(p),u=p}).then(a);return l.cancel=function(){o.unsubscribe(u)},l},n(function(u,l,p){o.reason||(o.reason=new pe(u,l,p),s(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(n){if(this.reason){n(this.reason);return}this._listeners?this._listeners.push(n):this._listeners=[n]}unsubscribe(n){if(!this._listeners)return;const s=this._listeners.indexOf(n);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const n=new AbortController,s=o=>{n.abort(o)};return this.subscribe(s),n.signal.unsubscribe=()=>this.unsubscribe(s),n.signal}static source(){let n;return{token:new bt(function(a){n=a}),cancel:n}}}function Po(t){return function(s){return t.apply(null,s)}}function Ro(t){return f.isObject(t)&&t.isAxiosError===!0}const ht={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ht).forEach(([t,n])=>{ht[n]=t});function Dn(t){const n=new le(t),s=dn(le.prototype.request,n);return f.extend(s,le.prototype,n,{allOwnKeys:!0}),f.extend(s,n,null,{allOwnKeys:!0}),s.create=function(a){return Dn(he(t,a))},s}const q=Dn(Ce);q.Axios=le;q.CanceledError=pe;q.CancelToken=bt;q.isCancel=Pn;q.VERSION=jn;q.toFormData=Be;q.AxiosError=A;q.Cancel=q.CanceledError;q.all=function(n){return Promise.all(n)};q.spread=Po;q.isAxiosError=Ro;q.mergeConfig=he;q.AxiosHeaders=V;q.formToJSON=t=>xn(f.isHTMLForm(t)?new FormData(t):t);q.getAdapter=In.getAdapter;q.HttpStatusCode=ht;q.default=q;function we(t){"@babel/helpers - typeof";return we=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},we(t)}function B(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function Oo(t,n){for(var s=0;s<n.length;s++){var o=n[s];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function H(t,n,s){return n&&Oo(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Se(){return Se=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var s=arguments[n];for(var o in s)Object.prototype.hasOwnProperty.call(s,o)&&(t[o]=s[o])}return t},Se.apply(this,arguments)}function K(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&ft(t,n)}function je(t){return je=Object.setPrototypeOf?Object.getPrototypeOf:function(s){return s.__proto__||Object.getPrototypeOf(s)},je(t)}function ft(t,n){return ft=Object.setPrototypeOf||function(o,a){return o.__proto__=a,o},ft(t,n)}function Ao(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Lo(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function No(t,n){if(n&&(typeof n=="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Lo(t)}function G(t){var n=Ao();return function(){var o=je(t),a;if(n){var u=je(this).constructor;a=Reflect.construct(o,arguments,u)}else a=o.apply(this,arguments);return No(this,a)}}var yt=function(){function t(){B(this,t)}return H(t,[{key:"listenForWhisper",value:function(s,o){return this.listen(".client-"+s,o)}},{key:"notification",value:function(s){return this.listen(".Illuminate\\Notifications\\Events\\BroadcastNotificationCreated",s)}},{key:"stopListeningForWhisper",value:function(s,o){return this.stopListening(".client-"+s,o)}}]),t}(),Un=function(){function t(n){B(this,t),this.namespace=n}return H(t,[{key:"format",value:function(s){return[".","\\"].includes(s.charAt(0))?s.substring(1):(this.namespace&&(s=this.namespace+"."+s),s.replace(/\./g,"\\"))}},{key:"setNamespace",value:function(s){this.namespace=s}}]),t}();function Io(t){try{new t}catch(n){if(n.message.includes("is not a constructor"))return!1}return!0}var vt=function(t){K(s,t);var n=G(s);function s(o,a,u){var l;return B(this,s),l=n.call(this),l.name=a,l.pusher=o,l.options=u,l.eventFormatter=new Un(l.options.namespace),l.subscribe(),l}return H(s,[{key:"subscribe",value:function(){this.subscription=this.pusher.subscribe(this.name)}},{key:"unsubscribe",value:function(){this.pusher.unsubscribe(this.name)}},{key:"listen",value:function(a,u){return this.on(this.eventFormatter.format(a),u),this}},{key:"listenToAll",value:function(a){var u=this;return this.subscription.bind_global(function(l,p){if(!l.startsWith("pusher:")){var b=u.options.namespace.replace(/\./g,"\\"),y=l.startsWith(b)?l.substring(b.length+1):"."+l;a(y,p)}}),this}},{key:"stopListening",value:function(a,u){return u?this.subscription.unbind(this.eventFormatter.format(a),u):this.subscription.unbind(this.eventFormatter.format(a)),this}},{key:"stopListeningToAll",value:function(a){return a?this.subscription.unbind_global(a):this.subscription.unbind_global(),this}},{key:"subscribed",value:function(a){return this.on("pusher:subscription_succeeded",function(){a()}),this}},{key:"error",value:function(a){return this.on("pusher:subscription_error",function(u){a(u)}),this}},{key:"on",value:function(a,u){return this.subscription.bind(a,u),this}}]),s}(yt),Fn=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"whisper",value:function(a,u){return this.pusher.channels.channels[this.name].trigger("client-".concat(a),u),this}}]),s}(vt),jo=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"whisper",value:function(a,u){return this.pusher.channels.channels[this.name].trigger("client-".concat(a),u),this}}]),s}(vt),Do=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"here",value:function(a){return this.on("pusher:subscription_succeeded",function(u){a(Object.keys(u.members).map(function(l){return u.members[l]}))}),this}},{key:"joining",value:function(a){return this.on("pusher:member_added",function(u){a(u.info)}),this}},{key:"whisper",value:function(a,u){return this.pusher.channels.channels[this.name].trigger("client-".concat(a),u),this}},{key:"leaving",value:function(a){return this.on("pusher:member_removed",function(u){a(u.info)}),this}}]),s}(Fn),qn=function(t){K(s,t);var n=G(s);function s(o,a,u){var l;return B(this,s),l=n.call(this),l.events={},l.listeners={},l.name=a,l.socket=o,l.options=u,l.eventFormatter=new Un(l.options.namespace),l.subscribe(),l}return H(s,[{key:"subscribe",value:function(){this.socket.emit("subscribe",{channel:this.name,auth:this.options.auth||{}})}},{key:"unsubscribe",value:function(){this.unbind(),this.socket.emit("unsubscribe",{channel:this.name,auth:this.options.auth||{}})}},{key:"listen",value:function(a,u){return this.on(this.eventFormatter.format(a),u),this}},{key:"stopListening",value:function(a,u){return this.unbindEvent(this.eventFormatter.format(a),u),this}},{key:"subscribed",value:function(a){return this.on("connect",function(u){a(u)}),this}},{key:"error",value:function(a){return this}},{key:"on",value:function(a,u){var l=this;return this.listeners[a]=this.listeners[a]||[],this.events[a]||(this.events[a]=function(p,b){l.name===p&&l.listeners[a]&&l.listeners[a].forEach(function(y){return y(b)})},this.socket.on(a,this.events[a])),this.listeners[a].push(u),this}},{key:"unbind",value:function(){var a=this;Object.keys(this.events).forEach(function(u){a.unbindEvent(u)})}},{key:"unbindEvent",value:function(a,u){this.listeners[a]=this.listeners[a]||[],u&&(this.listeners[a]=this.listeners[a].filter(function(l){return l!==u})),(!u||this.listeners[a].length===0)&&(this.events[a]&&(this.socket.removeListener(a,this.events[a]),delete this.events[a]),delete this.listeners[a])}}]),s}(yt),Bn=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"whisper",value:function(a,u){return this.socket.emit("client event",{channel:this.name,event:"client-".concat(a),data:u}),this}}]),s}(qn),Uo=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"here",value:function(a){return this.on("presence:subscribed",function(u){a(u.map(function(l){return l.user_info}))}),this}},{key:"joining",value:function(a){return this.on("presence:joining",function(u){return a(u.user_info)}),this}},{key:"whisper",value:function(a,u){return this.socket.emit("client event",{channel:this.name,event:"client-".concat(a),data:u}),this}},{key:"leaving",value:function(a){return this.on("presence:leaving",function(u){return a(u.user_info)}),this}}]),s}(Bn),De=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"subscribe",value:function(){}},{key:"unsubscribe",value:function(){}},{key:"listen",value:function(a,u){return this}},{key:"listenToAll",value:function(a){return this}},{key:"stopListening",value:function(a,u){return this}},{key:"subscribed",value:function(a){return this}},{key:"error",value:function(a){return this}},{key:"on",value:function(a,u){return this}}]),s}(yt),Hn=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"whisper",value:function(a,u){return this}}]),s}(De),Fo=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"whisper",value:function(a,u){return this}}]),s}(De),qo=function(t){K(s,t);var n=G(s);function s(){return B(this,s),n.apply(this,arguments)}return H(s,[{key:"here",value:function(a){return this}},{key:"joining",value:function(a){return this}},{key:"whisper",value:function(a,u){return this}},{key:"leaving",value:function(a){return this}}]),s}(Hn),wt=function(){function t(n){B(this,t),this._defaultOptions={auth:{headers:{}},authEndpoint:"/broadcasting/auth",userAuthentication:{endpoint:"/broadcasting/user-auth",headers:{}},broadcaster:"pusher",csrfToken:null,bearerToken:null,host:null,key:null,namespace:"App.Events"},this.setOptions(n),this.connect()}return H(t,[{key:"setOptions",value:function(s){this.options=Se(this._defaultOptions,s);var o=this.csrfToken();return o&&(this.options.auth.headers["X-CSRF-TOKEN"]=o,this.options.userAuthentication.headers["X-CSRF-TOKEN"]=o),o=this.options.bearerToken,o&&(this.options.auth.headers.Authorization="Bearer "+o,this.options.userAuthentication.headers.Authorization="Bearer "+o),s}},{key:"csrfToken",value:function(){var s;return typeof window<"u"&&window.Laravel&&window.Laravel.csrfToken?window.Laravel.csrfToken:this.options.csrfToken?this.options.csrfToken:typeof document<"u"&&typeof document.querySelector=="function"&&(s=document.querySelector('meta[name="csrf-token"]'))?s.getAttribute("content"):null}}]),t}(),un=function(t){K(s,t);var n=G(s);function s(){var o;return B(this,s),o=n.apply(this,arguments),o.channels={},o}return H(s,[{key:"connect",value:function(){typeof this.options.client<"u"?this.pusher=this.options.client:this.options.Pusher?this.pusher=new this.options.Pusher(this.options.key,this.options):this.pusher=new Pusher(this.options.key,this.options)}},{key:"signin",value:function(){this.pusher.signin()}},{key:"listen",value:function(a,u,l){return this.channel(a).listen(u,l)}},{key:"channel",value:function(a){return this.channels[a]||(this.channels[a]=new vt(this.pusher,a,this.options)),this.channels[a]}},{key:"privateChannel",value:function(a){return this.channels["private-"+a]||(this.channels["private-"+a]=new Fn(this.pusher,"private-"+a,this.options)),this.channels["private-"+a]}},{key:"encryptedPrivateChannel",value:function(a){return this.channels["private-encrypted-"+a]||(this.channels["private-encrypted-"+a]=new jo(this.pusher,"private-encrypted-"+a,this.options)),this.channels["private-encrypted-"+a]}},{key:"presenceChannel",value:function(a){return this.channels["presence-"+a]||(this.channels["presence-"+a]=new Do(this.pusher,"presence-"+a,this.options)),this.channels["presence-"+a]}},{key:"leave",value:function(a){var u=this,l=[a,"private-"+a,"private-encrypted-"+a,"presence-"+a];l.forEach(function(p,b){u.leaveChannel(p)})}},{key:"leaveChannel",value:function(a){this.channels[a]&&(this.channels[a].unsubscribe(),delete this.channels[a])}},{key:"socketId",value:function(){return this.pusher.connection.socket_id}},{key:"disconnect",value:function(){this.pusher.disconnect()}}]),s}(wt),ln=function(t){K(s,t);var n=G(s);function s(){var o;return B(this,s),o=n.apply(this,arguments),o.channels={},o}return H(s,[{key:"connect",value:function(){var a=this,u=this.getSocketIO();return this.socket=u(this.options.host,this.options),this.socket.on("reconnect",function(){Object.values(a.channels).forEach(function(l){l.subscribe()})}),this.socket}},{key:"getSocketIO",value:function(){if(typeof this.options.client<"u")return this.options.client;if(typeof io<"u")return io;throw new Error("Socket.io client not found. Should be globally available or passed via options.client")}},{key:"listen",value:function(a,u,l){return this.channel(a).listen(u,l)}},{key:"channel",value:function(a){return this.channels[a]||(this.channels[a]=new qn(this.socket,a,this.options)),this.channels[a]}},{key:"privateChannel",value:function(a){return this.channels["private-"+a]||(this.channels["private-"+a]=new Bn(this.socket,"private-"+a,this.options)),this.channels["private-"+a]}},{key:"presenceChannel",value:function(a){return this.channels["presence-"+a]||(this.channels["presence-"+a]=new Uo(this.socket,"presence-"+a,this.options)),this.channels["presence-"+a]}},{key:"leave",value:function(a){var u=this,l=[a,"private-"+a,"presence-"+a];l.forEach(function(p){u.leaveChannel(p)})}},{key:"leaveChannel",value:function(a){this.channels[a]&&(this.channels[a].unsubscribe(),delete this.channels[a])}},{key:"socketId",value:function(){return this.socket.id}},{key:"disconnect",value:function(){this.socket.disconnect()}}]),s}(wt),Bo=function(t){K(s,t);var n=G(s);function s(){var o;return B(this,s),o=n.apply(this,arguments),o.channels={},o}return H(s,[{key:"connect",value:function(){}},{key:"listen",value:function(a,u,l){return new De}},{key:"channel",value:function(a){return new De}},{key:"privateChannel",value:function(a){return new Hn}},{key:"encryptedPrivateChannel",value:function(a){return new Fo}},{key:"presenceChannel",value:function(a){return new qo}},{key:"leave",value:function(a){}},{key:"leaveChannel",value:function(a){}},{key:"socketId",value:function(){return"fake-socket-id"}},{key:"disconnect",value:function(){}}]),s}(wt),Ho=function(){function t(n){B(this,t),this.options=n,this.connect(),this.options.withoutInterceptors||this.registerInterceptors()}return H(t,[{key:"channel",value:function(s){return this.connector.channel(s)}},{key:"connect",value:function(){if(this.options.broadcaster=="reverb")this.connector=new un(Se(Se({},this.options),{cluster:""}));else if(this.options.broadcaster=="pusher")this.connector=new un(this.options);else if(this.options.broadcaster=="socket.io")this.connector=new ln(this.options);else if(this.options.broadcaster=="null")this.connector=new Bo(this.options);else if(typeof this.options.broadcaster=="function"&&Io(this.options.broadcaster))this.connector=new this.options.broadcaster(this.options);else throw new Error("Broadcaster ".concat(we(this.options.broadcaster)," ").concat(this.options.broadcaster," is not supported."))}},{key:"disconnect",value:function(){this.connector.disconnect()}},{key:"join",value:function(s){return this.connector.presenceChannel(s)}},{key:"leave",value:function(s){this.connector.leave(s)}},{key:"leaveChannel",value:function(s){this.connector.leaveChannel(s)}},{key:"leaveAllChannels",value:function(){for(var s in this.connector.channels)this.leaveChannel(s)}},{key:"listen",value:function(s,o,a){return this.connector.listen(s,o,a)}},{key:"private",value:function(s){return this.connector.privateChannel(s)}},{key:"encryptedPrivate",value:function(s){if(this.connector instanceof ln)throw new Error("Broadcaster ".concat(we(this.options.broadcaster)," ").concat(this.options.broadcaster," does not support encrypted private channels."));return this.connector.encryptedPrivateChannel(s)}},{key:"socketId",value:function(){return this.connector.socketId()}},{key:"registerInterceptors",value:function(){typeof Vue=="function"&&Vue.http&&this.registerVueRequestInterceptor(),typeof axios=="function"&&this.registerAxiosRequestInterceptor(),typeof jQuery=="function"&&this.registerjQueryAjaxSetup(),(typeof Turbo>"u"?"undefined":we(Turbo))==="object"&&this.registerTurboRequestInterceptor()}},{key:"registerVueRequestInterceptor",value:function(){var s=this;Vue.http.interceptors.push(function(o,a){s.socketId()&&o.headers.set("X-Socket-ID",s.socketId()),a()})}},{key:"registerAxiosRequestInterceptor",value:function(){var s=this;axios.interceptors.request.use(function(o){return s.socketId()&&(o.headers["X-Socket-Id"]=s.socketId()),o})}},{key:"registerjQueryAjaxSetup",value:function(){var s=this;typeof jQuery.ajax<"u"&&jQuery.ajaxPrefilter(function(o,a,u){s.socketId()&&u.setRequestHeader("X-Socket-Id",s.socketId())})}},{key:"registerTurboRequestInterceptor",value:function(){var s=this;document.addEventListener("turbo:before-fetch-request",function(o){o.detail.fetchOptions.headers["X-Socket-Id"]=s.socketId()})}}]),t}();function Mo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Mn={exports:{}};/*!
 * Pusher JavaScript Library v8.4.0
 * https://pusher.com/
 *
 * Copyright 2020, Pusher
 * Released under the MIT licence.
 */(function(t,n){(function(o,a){t.exports=a()})(window,function(){return function(s){var o={};function a(u){if(o[u])return o[u].exports;var l=o[u]={i:u,l:!1,exports:{}};return s[u].call(l.exports,l,l.exports,a),l.l=!0,l.exports}return a.m=s,a.c=o,a.d=function(u,l,p){a.o(u,l)||Object.defineProperty(u,l,{enumerable:!0,get:p})},a.r=function(u){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(u,"__esModule",{value:!0})},a.t=function(u,l){if(l&1&&(u=a(u)),l&8||l&4&&typeof u=="object"&&u&&u.__esModule)return u;var p=Object.create(null);if(a.r(p),Object.defineProperty(p,"default",{enumerable:!0,value:u}),l&2&&typeof u!="string")for(var b in u)a.d(p,b,(function(y){return u[y]}).bind(null,b));return p},a.n=function(u){var l=u&&u.__esModule?function(){return u.default}:function(){return u};return a.d(l,"a",l),l},a.o=function(u,l){return Object.prototype.hasOwnProperty.call(u,l)},a.p="",a(a.s=2)}([function(s,o,a){var u=this&&this.__extends||function(){var v=function(d,w){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(C,P){C.__proto__=P}||function(C,P){for(var j in P)P.hasOwnProperty(j)&&(C[j]=P[j])},v(d,w)};return function(d,w){v(d,w);function C(){this.constructor=d}d.prototype=w===null?Object.create(w):(C.prototype=w.prototype,new C)}}();Object.defineProperty(o,"__esModule",{value:!0});var l=256,p=function(){function v(d){d===void 0&&(d="="),this._paddingCharacter=d}return v.prototype.encodedLength=function(d){return this._paddingCharacter?(d+2)/3*4|0:(d*8+5)/6|0},v.prototype.encode=function(d){for(var w="",C=0;C<d.length-2;C+=3){var P=d[C]<<16|d[C+1]<<8|d[C+2];w+=this._encodeByte(P>>>3*6&63),w+=this._encodeByte(P>>>2*6&63),w+=this._encodeByte(P>>>1*6&63),w+=this._encodeByte(P>>>0*6&63)}var j=d.length-C;if(j>0){var P=d[C]<<16|(j===2?d[C+1]<<8:0);w+=this._encodeByte(P>>>3*6&63),w+=this._encodeByte(P>>>2*6&63),j===2?w+=this._encodeByte(P>>>1*6&63):w+=this._paddingCharacter||"",w+=this._paddingCharacter||""}return w},v.prototype.maxDecodedLength=function(d){return this._paddingCharacter?d/4*3|0:(d*6+7)/8|0},v.prototype.decodedLength=function(d){return this.maxDecodedLength(d.length-this._getPaddingLength(d))},v.prototype.decode=function(d){if(d.length===0)return new Uint8Array(0);for(var w=this._getPaddingLength(d),C=d.length-w,P=new Uint8Array(this.maxDecodedLength(C)),j=0,N=0,U=0,M=0,z=0,Y=0,re=0;N<C-4;N+=4)M=this._decodeChar(d.charCodeAt(N+0)),z=this._decodeChar(d.charCodeAt(N+1)),Y=this._decodeChar(d.charCodeAt(N+2)),re=this._decodeChar(d.charCodeAt(N+3)),P[j++]=M<<2|z>>>4,P[j++]=z<<4|Y>>>2,P[j++]=Y<<6|re,U|=M&l,U|=z&l,U|=Y&l,U|=re&l;if(N<C-1&&(M=this._decodeChar(d.charCodeAt(N)),z=this._decodeChar(d.charCodeAt(N+1)),P[j++]=M<<2|z>>>4,U|=M&l,U|=z&l),N<C-2&&(Y=this._decodeChar(d.charCodeAt(N+2)),P[j++]=z<<4|Y>>>2,U|=Y&l),N<C-3&&(re=this._decodeChar(d.charCodeAt(N+3)),P[j++]=Y<<6|re,U|=re&l),U!==0)throw new Error("Base64Coder: incorrect characters for decoding");return P},v.prototype._encodeByte=function(d){var w=d;return w+=65,w+=25-d>>>8&6,w+=51-d>>>8&-75,w+=61-d>>>8&-15,w+=62-d>>>8&3,String.fromCharCode(w)},v.prototype._decodeChar=function(d){var w=l;return w+=(42-d&d-44)>>>8&-l+d-43+62,w+=(46-d&d-48)>>>8&-l+d-47+63,w+=(47-d&d-58)>>>8&-l+d-48+52,w+=(64-d&d-91)>>>8&-l+d-65+0,w+=(96-d&d-123)>>>8&-l+d-97+26,w},v.prototype._getPaddingLength=function(d){var w=0;if(this._paddingCharacter){for(var C=d.length-1;C>=0&&d[C]===this._paddingCharacter;C--)w++;if(d.length<4||w>2)throw new Error("Base64Coder: incorrect padding")}return w},v}();o.Coder=p;var b=new p;function y(v){return b.encode(v)}o.encode=y;function g(v){return b.decode(v)}o.decode=g;var k=function(v){u(d,v);function d(){return v!==null&&v.apply(this,arguments)||this}return d.prototype._encodeByte=function(w){var C=w;return C+=65,C+=25-w>>>8&6,C+=51-w>>>8&-75,C+=61-w>>>8&-13,C+=62-w>>>8&49,String.fromCharCode(C)},d.prototype._decodeChar=function(w){var C=l;return C+=(44-w&w-46)>>>8&-l+w-45+62,C+=(94-w&w-96)>>>8&-l+w-95+63,C+=(47-w&w-58)>>>8&-l+w-48+52,C+=(64-w&w-91)>>>8&-l+w-65+0,C+=(96-w&w-123)>>>8&-l+w-97+26,C},d}(p);o.URLSafeCoder=k;var E=new k;function x(v){return E.encode(v)}o.encodeURLSafe=x;function _(v){return E.decode(v)}o.decodeURLSafe=_,o.encodedLength=function(v){return b.encodedLength(v)},o.maxDecodedLength=function(v){return b.maxDecodedLength(v)},o.decodedLength=function(v){return b.decodedLength(v)}},function(s,o,a){Object.defineProperty(o,"__esModule",{value:!0});var u="utf8: invalid string",l="utf8: invalid source encoding";function p(g){for(var k=new Uint8Array(b(g)),E=0,x=0;x<g.length;x++){var _=g.charCodeAt(x);_<128?k[E++]=_:_<2048?(k[E++]=192|_>>6,k[E++]=128|_&63):_<55296?(k[E++]=224|_>>12,k[E++]=128|_>>6&63,k[E++]=128|_&63):(x++,_=(_&1023)<<10,_|=g.charCodeAt(x)&1023,_+=65536,k[E++]=240|_>>18,k[E++]=128|_>>12&63,k[E++]=128|_>>6&63,k[E++]=128|_&63)}return k}o.encode=p;function b(g){for(var k=0,E=0;E<g.length;E++){var x=g.charCodeAt(E);if(x<128)k+=1;else if(x<2048)k+=2;else if(x<55296)k+=3;else if(x<=57343){if(E>=g.length-1)throw new Error(u);E++,k+=4}else throw new Error(u)}return k}o.encodedLength=b;function y(g){for(var k=[],E=0;E<g.length;E++){var x=g[E];if(x&128){var _=void 0;if(x<224){if(E>=g.length)throw new Error(l);var v=g[++E];if((v&192)!==128)throw new Error(l);x=(x&31)<<6|v&63,_=128}else if(x<240){if(E>=g.length-1)throw new Error(l);var v=g[++E],d=g[++E];if((v&192)!==128||(d&192)!==128)throw new Error(l);x=(x&15)<<12|(v&63)<<6|d&63,_=2048}else if(x<248){if(E>=g.length-2)throw new Error(l);var v=g[++E],d=g[++E],w=g[++E];if((v&192)!==128||(d&192)!==128||(w&192)!==128)throw new Error(l);x=(x&15)<<18|(v&63)<<12|(d&63)<<6|w&63,_=65536}else throw new Error(l);if(x<_||x>=55296&&x<=57343)throw new Error(l);if(x>=65536){if(x>1114111)throw new Error(l);x-=65536,k.push(String.fromCharCode(55296|x>>10)),x=56320|x&1023}}k.push(String.fromCharCode(x))}return k.join("")}o.decode=y},function(s,o,a){s.exports=a(3).default},function(s,o,a){a.r(o);class u{constructor(e,r){this.lastId=0,this.prefix=e,this.name=r}create(e){this.lastId++;var r=this.lastId,c=this.prefix+r,h=this.name+"["+r+"]",m=!1,S=function(){m||(e.apply(null,arguments),m=!0)};return this[r]=S,{number:r,id:c,name:h,callback:S}}remove(e){delete this[e.number]}}var l=new u("_pusher_script_","Pusher.ScriptReceivers"),p={VERSION:"8.4.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""},b=p;class y{constructor(e){this.options=e,this.receivers=e.receivers||l,this.loading={}}load(e,r,c){var h=this;if(h.loading[e]&&h.loading[e].length>0)h.loading[e].push(c);else{h.loading[e]=[c];var m=O.createScriptRequest(h.getPath(e,r)),S=h.receivers.create(function(T){if(h.receivers.remove(S),h.loading[e]){var R=h.loading[e];delete h.loading[e];for(var L=function(F){F||m.cleanup()},I=0;I<R.length;I++)R[I](T,L)}});m.send(S)}}getRoot(e){var r,c=O.getDocument().location.protocol;return e&&e.useTLS||c==="https:"?r=this.options.cdn_https:r=this.options.cdn_http,r.replace(/\/*$/,"")+"/"+this.options.version}getPath(e,r){return this.getRoot(r)+"/"+e+this.options.suffix+".js"}}var g=new u("_pusher_dependencies","Pusher.DependenciesReceivers"),k=new y({cdn_http:b.cdn_http,cdn_https:b.cdn_https,version:b.VERSION,suffix:b.dependency_suffix,receivers:g});const E={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var _={buildLogSuffix:function(i){const e="See:",r=E.urls[i];if(!r)return"";let c;return r.fullUrl?c=r.fullUrl:r.path&&(c=E.baseUrl+r.path),c?`${e} ${c}`:""}},v;(function(i){i.UserAuthentication="user-authentication",i.ChannelAuthorization="channel-authorization"})(v||(v={}));class d extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class w extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class C extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class P extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class j extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class N extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class U extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class M extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}class z extends Error{constructor(e,r){super(r),this.status=e,Object.setPrototypeOf(this,new.target.prototype)}}var re=function(i,e,r,c,h){const m=O.createXHR();m.open("POST",r.endpoint,!0),m.setRequestHeader("Content-Type","application/x-www-form-urlencoded");for(var S in r.headers)m.setRequestHeader(S,r.headers[S]);if(r.headersProvider!=null){let T=r.headersProvider();for(var S in T)m.setRequestHeader(S,T[S])}return m.onreadystatechange=function(){if(m.readyState===4)if(m.status===200){let T,R=!1;try{T=JSON.parse(m.responseText),R=!0}catch{h(new z(200,`JSON returned from ${c.toString()} endpoint was invalid, yet status code was 200. Data was: ${m.responseText}`),null)}R&&h(null,T)}else{let T="";switch(c){case v.UserAuthentication:T=_.buildLogSuffix("authenticationEndpoint");break;case v.ChannelAuthorization:T=`Clients must be authorized to join private or presence channels. ${_.buildLogSuffix("authorizationEndpoint")}`;break}h(new z(m.status,`Unable to retrieve auth string from ${c.toString()} endpoint - received status: ${m.status} from ${r.endpoint}. ${T}`),null)}},m.send(e),m};function zn(i){return Wn($n(i))}var me=String.fromCharCode,Te="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Jn=function(i){var e=i.charCodeAt(0);return e<128?i:e<2048?me(192|e>>>6)+me(128|e&63):me(224|e>>>12&15)+me(128|e>>>6&63)+me(128|e&63)},$n=function(i){return i.replace(/[^\x00-\x7F]/g,Jn)},Xn=function(i){var e=[0,2,1][i.length%3],r=i.charCodeAt(0)<<16|(i.length>1?i.charCodeAt(1):0)<<8|(i.length>2?i.charCodeAt(2):0),c=[Te.charAt(r>>>18),Te.charAt(r>>>12&63),e>=2?"=":Te.charAt(r>>>6&63),e>=1?"=":Te.charAt(r&63)];return c.join("")},Wn=window.btoa||function(i){return i.replace(/[\s\S]{1,3}/g,Xn)};class Vn{constructor(e,r,c,h){this.clear=r,this.timer=e(()=>{this.timer&&(this.timer=h(this.timer))},c)}isRunning(){return this.timer!==null}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}}var _t=Vn;function Kn(i){window.clearTimeout(i)}function Gn(i){window.clearInterval(i)}class se extends _t{constructor(e,r){super(setTimeout,Kn,e,function(c){return r(),null})}}class Qn extends _t{constructor(e,r){super(setInterval,Gn,e,function(c){return r(),c})}}var Yn={now(){return Date.now?Date.now():new Date().valueOf()},defer(i){return new se(0,i)},method(i,...e){var r=Array.prototype.slice.call(arguments,1);return function(c){return c[i].apply(c,r.concat(arguments))}}},$=Yn;function X(i,...e){for(var r=0;r<e.length;r++){var c=e[r];for(var h in c)c[h]&&c[h].constructor&&c[h].constructor===Object?i[h]=X(i[h]||{},c[h]):i[h]=c[h]}return i}function Zn(){for(var i=["Pusher"],e=0;e<arguments.length;e++)typeof arguments[e]=="string"?i.push(arguments[e]):i.push(Ee(arguments[e]));return i.join(" : ")}function St(i,e){var r=Array.prototype.indexOf;if(i===null)return-1;if(r&&i.indexOf===r)return i.indexOf(e);for(var c=0,h=i.length;c<h;c++)if(i[c]===e)return c;return-1}function ee(i,e){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&e(i[r],r,i)}function kt(i){var e=[];return ee(i,function(r,c){e.push(c)}),e}function er(i){var e=[];return ee(i,function(r){e.push(r)}),e}function ge(i,e,r){for(var c=0;c<i.length;c++)e.call(r||window,i[c],c,i)}function Ct(i,e){for(var r=[],c=0;c<i.length;c++)r.push(e(i[c],c,i,r));return r}function tr(i,e){var r={};return ee(i,function(c,h){r[h]=e(c)}),r}function Tt(i,e){e=e||function(h){return!!h};for(var r=[],c=0;c<i.length;c++)e(i[c],c,i,r)&&r.push(i[c]);return r}function Et(i,e){var r={};return ee(i,function(c,h){(e&&e(c,h,i,r)||c)&&(r[h]=c)}),r}function nr(i){var e=[];return ee(i,function(r,c){e.push([c,r])}),e}function xt(i,e){for(var r=0;r<i.length;r++)if(e(i[r],r,i))return!0;return!1}function rr(i,e){for(var r=0;r<i.length;r++)if(!e(i[r],r,i))return!1;return!0}function ir(i){return tr(i,function(e){return typeof e=="object"&&(e=Ee(e)),encodeURIComponent(zn(e.toString()))})}function sr(i){var e=Et(i,function(c){return c!==void 0}),r=Ct(nr(ir(e)),$.method("join","=")).join("&");return r}function or(i){var e=[],r=[];return function c(h,m){var S,T,R;switch(typeof h){case"object":if(!h)return null;for(S=0;S<e.length;S+=1)if(e[S]===h)return{$ref:r[S]};if(e.push(h),r.push(m),Object.prototype.toString.apply(h)==="[object Array]")for(R=[],S=0;S<h.length;S+=1)R[S]=c(h[S],m+"["+S+"]");else{R={};for(T in h)Object.prototype.hasOwnProperty.call(h,T)&&(R[T]=c(h[T],m+"["+JSON.stringify(T)+"]"))}return R;case"number":case"string":case"boolean":return h}}(i,"$")}function Ee(i){try{return JSON.stringify(i)}catch{return JSON.stringify(or(i))}}class ar{constructor(){this.globalLog=e=>{window.console&&window.console.log&&window.console.log(e)}}debug(...e){this.log(this.globalLog,e)}warn(...e){this.log(this.globalLogWarn,e)}error(...e){this.log(this.globalLogError,e)}globalLogWarn(e){window.console&&window.console.warn?window.console.warn(e):this.globalLog(e)}globalLogError(e){window.console&&window.console.error?window.console.error(e):this.globalLogWarn(e)}log(e,...r){var c=Zn.apply(this,arguments);Ye.log?Ye.log(c):Ye.logToConsole&&e.bind(this)(c)}}var D=new ar,cr=function(i,e,r,c,h){(r.headers!==void 0||r.headersProvider!=null)&&D.warn(`To send headers with the ${c.toString()} request, you must use AJAX, rather than JSONP.`);var m=i.nextAuthCallbackID.toString();i.nextAuthCallbackID++;var S=i.getDocument(),T=S.createElement("script");i.auth_callbacks[m]=function(I){h(null,I)};var R="Pusher.auth_callbacks['"+m+"']";T.src=r.endpoint+"?callback="+encodeURIComponent(R)+"&"+e;var L=S.getElementsByTagName("head")[0]||S.documentElement;L.insertBefore(T,L.firstChild)},ur=cr;class lr{constructor(e){this.src=e}send(e){var r=this,c="Error loading "+r.src;r.script=document.createElement("script"),r.script.id=e.id,r.script.src=r.src,r.script.type="text/javascript",r.script.charset="UTF-8",r.script.addEventListener?(r.script.onerror=function(){e.callback(c)},r.script.onload=function(){e.callback(null)}):r.script.onreadystatechange=function(){(r.script.readyState==="loaded"||r.script.readyState==="complete")&&e.callback(null)},r.script.async===void 0&&document.attachEvent&&/opera/i.test(navigator.userAgent)?(r.errorScript=document.createElement("script"),r.errorScript.id=e.id+"_error",r.errorScript.text=e.name+"('"+c+"');",r.script.async=r.errorScript.async=!1):r.script.async=!0;var h=document.getElementsByTagName("head")[0];h.insertBefore(r.script,h.firstChild),r.errorScript&&h.insertBefore(r.errorScript,r.script.nextSibling)}cleanup(){this.script&&(this.script.onload=this.script.onerror=null,this.script.onreadystatechange=null),this.script&&this.script.parentNode&&this.script.parentNode.removeChild(this.script),this.errorScript&&this.errorScript.parentNode&&this.errorScript.parentNode.removeChild(this.errorScript),this.script=null,this.errorScript=null}}class hr{constructor(e,r){this.url=e,this.data=r}send(e){if(!this.request){var r=sr(this.data),c=this.url+"/"+e.number+"?"+r;this.request=O.createScriptRequest(c),this.request.send(e)}}cleanup(){this.request&&this.request.cleanup()}}var fr=function(i,e){return function(r,c){var h="http"+(e?"s":"")+"://",m=h+(i.host||i.options.host)+i.options.path,S=O.createJSONPRequest(m,r),T=O.ScriptReceivers.create(function(R,L){l.remove(T),S.cleanup(),L&&L.host&&(i.host=L.host),c&&c(R,L)});S.send(T)}},dr={name:"jsonp",getAgent:fr},pr=dr;function Me(i,e,r){var c=i+(e.useTLS?"s":""),h=e.useTLS?e.hostTLS:e.hostNonTLS;return c+"://"+h+r}function ze(i,e){var r="/app/"+i,c="?protocol="+b.PROTOCOL+"&client=js&version="+b.VERSION+(e?"&"+e:"");return r+c}var mr={getInitial:function(i,e){var r=(e.httpPath||"")+ze(i,"flash=false");return Me("ws",e,r)}},gr={getInitial:function(i,e){var r=(e.httpPath||"/pusher")+ze(i);return Me("http",e,r)}},br={getInitial:function(i,e){return Me("http",e,e.httpPath||"/pusher")},getPath:function(i,e){return ze(i)}};class yr{constructor(){this._callbacks={}}get(e){return this._callbacks[Je(e)]}add(e,r,c){var h=Je(e);this._callbacks[h]=this._callbacks[h]||[],this._callbacks[h].push({fn:r,context:c})}remove(e,r,c){if(!e&&!r&&!c){this._callbacks={};return}var h=e?[Je(e)]:kt(this._callbacks);r||c?this.removeCallback(h,r,c):this.removeAllCallbacks(h)}removeCallback(e,r,c){ge(e,function(h){this._callbacks[h]=Tt(this._callbacks[h]||[],function(m){return r&&r!==m.fn||c&&c!==m.context}),this._callbacks[h].length===0&&delete this._callbacks[h]},this)}removeAllCallbacks(e){ge(e,function(r){delete this._callbacks[r]},this)}}function Je(i){return"_"+i}class te{constructor(e){this.callbacks=new yr,this.global_callbacks=[],this.failThrough=e}bind(e,r,c){return this.callbacks.add(e,r,c),this}bind_global(e){return this.global_callbacks.push(e),this}unbind(e,r,c){return this.callbacks.remove(e,r,c),this}unbind_global(e){return e?(this.global_callbacks=Tt(this.global_callbacks||[],r=>r!==e),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(e,r,c){for(var h=0;h<this.global_callbacks.length;h++)this.global_callbacks[h](e,r);var m=this.callbacks.get(e),S=[];if(c?S.push(r,c):r&&S.push(r),m&&m.length>0)for(var h=0;h<m.length;h++)m[h].fn.apply(m[h].context||window,S);else this.failThrough&&this.failThrough(e,r);return this}}class vr extends te{constructor(e,r,c,h,m){super(),this.initialize=O.transportConnectionInitializer,this.hooks=e,this.name=r,this.priority=c,this.key=h,this.options=m,this.state="new",this.timeline=m.timeline,this.activityTimeout=m.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return!!this.hooks.handlesActivityChecks}supportsPing(){return!!this.hooks.supportsPing}connect(){if(this.socket||this.state!=="initialized")return!1;var e=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(e,this.options)}catch(r){return $.defer(()=>{this.onError(r),this.changeState("closed")}),!1}return this.bindListeners(),D.debug("Connecting",{transport:this.name,url:e}),this.changeState("connecting"),!0}close(){return this.socket?(this.socket.close(),!0):!1}send(e){return this.state==="open"?($.defer(()=>{this.socket&&this.socket.send(e)}),!0):!1}ping(){this.state==="open"&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(e){this.emit("error",{type:"WebSocketError",error:e}),this.timeline.error(this.buildTimelineMessage({error:e.toString()}))}onClose(e){e?this.changeState("closed",{code:e.code,reason:e.reason,wasClean:e.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(e){this.emit("message",e)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=e=>{this.onError(e)},this.socket.onclose=e=>{this.onClose(e)},this.socket.onmessage=e=>{this.onMessage(e)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(e,r){this.state=e,this.timeline.info(this.buildTimelineMessage({state:e,params:r})),this.emit(e,r)}buildTimelineMessage(e){return X({cid:this.id},e)}}class fe{constructor(e){this.hooks=e}isSupported(e){return this.hooks.isSupported(e)}createConnection(e,r,c,h){return new vr(this.hooks,e,r,c,h)}}var wr=new fe({urls:mr,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return!!O.getWebSocketAPI()},isSupported:function(){return!!O.getWebSocketAPI()},getSocket:function(i){return O.createWebSocket(i)}}),Pt={urls:gr,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},Rt=X({getSocket:function(i){return O.HTTPFactory.createStreamingSocket(i)}},Pt),Ot=X({getSocket:function(i){return O.HTTPFactory.createPollingSocket(i)}},Pt),At={isSupported:function(){return O.isXHRSupported()}},_r=new fe(X({},Rt,At)),Sr=new fe(X({},Ot,At)),kr={ws:wr,xhr_streaming:_r,xhr_polling:Sr},xe=kr,Cr=new fe({file:"sockjs",urls:br,handlesActivityChecks:!0,supportsPing:!1,isSupported:function(){return!0},isInitialized:function(){return window.SockJS!==void 0},getSocket:function(i,e){return new window.SockJS(i,null,{js_path:k.getPath("sockjs",{useTLS:e.useTLS}),ignore_null_origin:e.ignoreNullOrigin})},beforeOpen:function(i,e){i.send(JSON.stringify({path:e}))}}),Lt={isSupported:function(i){var e=O.isXDRSupported(i.useTLS);return e}},Tr=new fe(X({},Rt,Lt)),Er=new fe(X({},Ot,Lt));xe.xdr_streaming=Tr,xe.xdr_polling=Er,xe.sockjs=Cr;var xr=xe;class Pr extends te{constructor(){super();var e=this;window.addEventListener!==void 0&&(window.addEventListener("online",function(){e.emit("online")},!1),window.addEventListener("offline",function(){e.emit("offline")},!1))}isOnline(){return window.navigator.onLine===void 0?!0:window.navigator.onLine}}var Rr=new Pr;class Or{constructor(e,r,c){this.manager=e,this.transport=r,this.minPingDelay=c.minPingDelay,this.maxPingDelay=c.maxPingDelay,this.pingDelay=void 0}createConnection(e,r,c,h){h=X({},h,{activityTimeout:this.pingDelay});var m=this.transport.createConnection(e,r,c,h),S=null,T=function(){m.unbind("open",T),m.bind("closed",R),S=$.now()},R=L=>{if(m.unbind("closed",R),L.code===1002||L.code===1003)this.manager.reportDeath();else if(!L.wasClean&&S){var I=$.now()-S;I<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(I/2,this.minPingDelay))}};return m.bind("open",T),m}isSupported(e){return this.manager.isAlive()&&this.transport.isSupported(e)}}const Nt={decodeMessage:function(i){try{var e=JSON.parse(i.data),r=e.data;if(typeof r=="string")try{r=JSON.parse(e.data)}catch{}var c={event:e.event,channel:e.channel,data:r};return e.user_id&&(c.user_id=e.user_id),c}catch(h){throw{type:"MessageParseError",error:h,data:i.data}}},encodeMessage:function(i){return JSON.stringify(i)},processHandshake:function(i){var e=Nt.decodeMessage(i);if(e.event==="pusher:connection_established"){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:e.data.activity_timeout*1e3}}else{if(e.event==="pusher:error")return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"}},getCloseAction:function(i){return i.code<4e3?i.code>=1002&&i.code<=1004?"backoff":null:i.code===4e3?"tls_only":i.code<4100?"refused":i.code<4200?"backoff":i.code<4300?"retry":"refused"},getCloseError:function(i){return i.code!==1e3&&i.code!==1001?{type:"PusherError",data:{code:i.code,message:i.reason||i.message}}:null}};var oe=Nt;class Ar extends te{constructor(e,r){super(),this.id=e,this.transport=r,this.activityTimeout=r.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(e){return this.transport.send(e)}send_event(e,r,c){var h={event:e,data:r};return c&&(h.channel=c),D.debug("Event sent",h),this.send(oe.encodeMessage(h))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var e={message:c=>{var h;try{h=oe.decodeMessage(c)}catch(m){this.emit("error",{type:"MessageParseError",error:m,data:c.data})}if(h!==void 0){switch(D.debug("Event recd",h),h.event){case"pusher:error":this.emit("error",{type:"PusherError",data:h.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong");break}this.emit("message",h)}},activity:()=>{this.emit("activity")},error:c=>{this.emit("error",c)},closed:c=>{r(),c&&c.code&&this.handleCloseEvent(c),this.transport=null,this.emit("closed")}},r=()=>{ee(e,(c,h)=>{this.transport.unbind(h,c)})};ee(e,(c,h)=>{this.transport.bind(h,c)})}handleCloseEvent(e){var r=oe.getCloseAction(e),c=oe.getCloseError(e);c&&this.emit("error",c),r&&this.emit(r,{action:r,error:c})}}class Lr{constructor(e,r){this.transport=e,this.callback=r,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=e=>{this.unbindListeners();var r;try{r=oe.processHandshake(e)}catch(c){this.finish("error",{error:c}),this.transport.close();return}r.action==="connected"?this.finish("connected",{connection:new Ar(r.id,this.transport),activityTimeout:r.activityTimeout}):(this.finish(r.action,{error:r.error}),this.transport.close())},this.onClosed=e=>{this.unbindListeners();var r=oe.getCloseAction(e)||"backoff",c=oe.getCloseError(e);this.finish(r,{error:c})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(e,r){this.callback(X({transport:this.transport,action:e},r))}}class Nr{constructor(e,r){this.timeline=e,this.options=r||{}}send(e,r){this.timeline.isEmpty()||this.timeline.send(O.TimelineTransport.getAgent(this,e),r)}}class $e extends te{constructor(e,r){super(function(c,h){D.debug("No callbacks on "+e+" for "+c)}),this.name=e,this.pusher=r,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(e,r){return r(null,{auth:""})}trigger(e,r){if(e.indexOf("client-")!==0)throw new d("Event '"+e+"' does not start with 'client-'");if(!this.subscribed){var c=_.buildLogSuffix("triggeringClientEvents");D.warn(`Client event triggered before channel 'subscription_succeeded' event . ${c}`)}return this.pusher.send_event(e,r,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(e){var r=e.event,c=e.data;if(r==="pusher_internal:subscription_succeeded")this.handleSubscriptionSucceededEvent(e);else if(r==="pusher_internal:subscription_count")this.handleSubscriptionCountEvent(e);else if(r.indexOf("pusher_internal:")!==0){var h={};this.emit(r,c,h)}}handleSubscriptionSucceededEvent(e){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",e.data)}handleSubscriptionCountEvent(e){e.data.subscription_count&&(this.subscriptionCount=e.data.subscription_count),this.emit("pusher:subscription_count",e.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(e,r)=>{e?(this.subscriptionPending=!1,D.error(e.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:e.message},e instanceof z?{status:e.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:r.auth,channel_data:r.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class Xe extends $e{authorize(e,r){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:e},r)}}class Ir{constructor(){this.reset()}get(e){return Object.prototype.hasOwnProperty.call(this.members,e)?{id:e,info:this.members[e]}:null}each(e){ee(this.members,(r,c)=>{e(this.get(c))})}setMyID(e){this.myID=e}onSubscription(e){this.members=e.presence.hash,this.count=e.presence.count,this.me=this.get(this.myID)}addMember(e){return this.get(e.user_id)===null&&this.count++,this.members[e.user_id]=e.user_info,this.get(e.user_id)}removeMember(e){var r=this.get(e.user_id);return r&&(delete this.members[e.user_id],this.count--),r}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}var jr=function(i,e,r,c){function h(m){return m instanceof r?m:new r(function(S){S(m)})}return new(r||(r=Promise))(function(m,S){function T(I){try{L(c.next(I))}catch(F){S(F)}}function R(I){try{L(c.throw(I))}catch(F){S(F)}}function L(I){I.done?m(I.value):h(I.value).then(T,R)}L((c=c.apply(i,e||[])).next())})};class Dr extends Xe{constructor(e,r){super(e,r),this.members=new Ir}authorize(e,r){super.authorize(e,(c,h)=>jr(this,void 0,void 0,function*(){if(!c)if(h=h,h.channel_data!=null){var m=JSON.parse(h.channel_data);this.members.setMyID(m.user_id)}else if(yield this.pusher.user.signinDonePromise,this.pusher.user.user_data!=null)this.members.setMyID(this.pusher.user.user_data.id);else{let S=_.buildLogSuffix("authorizationEndpoint");D.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${S}, or the user should be signed in.`),r("Invalid auth response");return}r(c,h)}))}handleEvent(e){var r=e.event;if(r.indexOf("pusher_internal:")===0)this.handleInternalEvent(e);else{var c=e.data,h={};e.user_id&&(h.user_id=e.user_id),this.emit(r,c,h)}}handleInternalEvent(e){var r=e.event,c=e.data;switch(r){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(e);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(e);break;case"pusher_internal:member_added":var h=this.members.addMember(c);this.emit("pusher:member_added",h);break;case"pusher_internal:member_removed":var m=this.members.removeMember(c);m&&this.emit("pusher:member_removed",m);break}}handleSubscriptionSucceededEvent(e){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(e.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var Ur=a(1),We=a(0);class Fr extends Xe{constructor(e,r,c){super(e,r),this.key=null,this.nacl=c}authorize(e,r){super.authorize(e,(c,h)=>{if(c){r(c,h);return}let m=h.shared_secret;if(!m){r(new Error(`No shared_secret key in auth payload for encrypted channel: ${this.name}`),null);return}this.key=Object(We.decode)(m),delete h.shared_secret,r(null,h)})}trigger(e,r){throw new N("Client events are not currently supported for encrypted channels")}handleEvent(e){var r=e.event,c=e.data;if(r.indexOf("pusher_internal:")===0||r.indexOf("pusher:")===0){super.handleEvent(e);return}this.handleEncryptedEvent(r,c)}handleEncryptedEvent(e,r){if(!this.key){D.debug("Received encrypted event before key has been retrieved from the authEndpoint");return}if(!r.ciphertext||!r.nonce){D.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+r);return}let c=Object(We.decode)(r.ciphertext);if(c.length<this.nacl.secretbox.overheadLength){D.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${c.length}`);return}let h=Object(We.decode)(r.nonce);if(h.length<this.nacl.secretbox.nonceLength){D.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${h.length}`);return}let m=this.nacl.secretbox.open(c,h,this.key);if(m===null){D.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),this.authorize(this.pusher.connection.socket_id,(S,T)=>{if(S){D.error(`Failed to make a request to the authEndpoint: ${T}. Unable to fetch new key, so dropping encrypted event`);return}if(m=this.nacl.secretbox.open(c,h,this.key),m===null){D.error("Failed to decrypt event with new key. Dropping encrypted event");return}this.emit(e,this.getDataToEmit(m))});return}this.emit(e,this.getDataToEmit(m))}getDataToEmit(e){let r=Object(Ur.decode)(e);try{return JSON.parse(r)}catch{return r}}}class qr extends te{constructor(e,r){super(),this.state="initialized",this.connection=null,this.key=e,this.options=r,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var c=O.getNetwork();c.bind("online",()=>{this.timeline.info({netinfo:"online"}),(this.state==="connecting"||this.state==="unavailable")&&this.retryIn(0)}),c.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}connect(){if(!(this.connection||this.runner)){if(!this.strategy.isSupported()){this.updateState("failed");return}this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()}}send(e){return this.connection?this.connection.send(e):!1}send_event(e,r,c){return this.connection?this.connection.send_event(e,r,c):!1}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var e=(r,c)=>{r?this.runner=this.strategy.connect(0,e):c.action==="error"?(this.emit("error",{type:"HandshakeError",error:c.error}),this.timeline.error({handshakeError:c.error})):(this.abortConnecting(),this.handshakeCallbacks[c.action](c))};this.runner=this.strategy.connect(0,e)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){if(this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection){var e=this.abandonConnection();e.close()}}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(e){this.timeline.info({action:"retry",delay:e}),e>0&&this.emit("connecting_in",Math.round(e/1e3)),this.retryTimer=new se(e||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new se(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new se(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new se(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(e){return X({},e,{message:r=>{this.resetActivityCheck(),this.emit("message",r)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:r=>{this.emit("error",r)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(e){return X({},e,{connected:r=>{this.activityTimeout=Math.min(this.options.activityTimeout,r.activityTimeout,r.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(r.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let e=r=>c=>{c.error&&this.emit("error",{type:"WebSocketError",error:c.error}),r(c)};return{tls_only:e(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:e(()=>{this.disconnect()}),backoff:e(()=>{this.retryIn(1e3)}),retry:e(()=>{this.retryIn(0)})}}setConnection(e){this.connection=e;for(var r in this.connectionCallbacks)this.connection.bind(r,this.connectionCallbacks[r]);this.resetActivityCheck()}abandonConnection(){if(this.connection){this.stopActivityCheck();for(var e in this.connectionCallbacks)this.connection.unbind(e,this.connectionCallbacks[e]);var r=this.connection;return this.connection=null,r}}updateState(e,r){var c=this.state;if(this.state=e,c!==e){var h=e;h==="connected"&&(h+=" with new socket ID "+r.socket_id),D.debug("State changed",c+" -> "+h),this.timeline.info({state:e,params:r}),this.emit("state_change",{previous:c,current:e}),this.emit(e,r)}}shouldRetry(){return this.state==="connecting"||this.state==="connected"}}class Br{constructor(){this.channels={}}add(e,r){return this.channels[e]||(this.channels[e]=Hr(e,r)),this.channels[e]}all(){return er(this.channels)}find(e){return this.channels[e]}remove(e){var r=this.channels[e];return delete this.channels[e],r}disconnect(){ee(this.channels,function(e){e.disconnect()})}}function Hr(i,e){if(i.indexOf("private-encrypted-")===0){if(e.config.nacl)return ne.createEncryptedChannel(i,e,e.config.nacl);let r="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",c=_.buildLogSuffix("encryptedChannelSupport");throw new N(`${r}. ${c}`)}else{if(i.indexOf("private-")===0)return ne.createPrivateChannel(i,e);if(i.indexOf("presence-")===0)return ne.createPresenceChannel(i,e);if(i.indexOf("#")===0)throw new w('Cannot create a channel with name "'+i+'".');return ne.createChannel(i,e)}}var Mr={createChannels(){return new Br},createConnectionManager(i,e){return new qr(i,e)},createChannel(i,e){return new $e(i,e)},createPrivateChannel(i,e){return new Xe(i,e)},createPresenceChannel(i,e){return new Dr(i,e)},createEncryptedChannel(i,e,r){return new Fr(i,e,r)},createTimelineSender(i,e){return new Nr(i,e)},createHandshake(i,e){return new Lr(i,e)},createAssistantToTheTransportManager(i,e,r){return new Or(i,e,r)}},ne=Mr;class It{constructor(e){this.options=e||{},this.livesLeft=this.options.lives||1/0}getAssistant(e){return ne.createAssistantToTheTransportManager(this,e,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class ae{constructor(e,r){this.strategies=e,this.loop=!!r.loop,this.failFast=!!r.failFast,this.timeout=r.timeout,this.timeoutLimit=r.timeoutLimit}isSupported(){return xt(this.strategies,$.method("isSupported"))}connect(e,r){var c=this.strategies,h=0,m=this.timeout,S=null,T=(R,L)=>{L?r(null,L):(h=h+1,this.loop&&(h=h%c.length),h<c.length?(m&&(m=m*2,this.timeoutLimit&&(m=Math.min(m,this.timeoutLimit))),S=this.tryStrategy(c[h],e,{timeout:m,failFast:this.failFast},T)):r(!0))};return S=this.tryStrategy(c[h],e,{timeout:m,failFast:this.failFast},T),{abort:function(){S.abort()},forceMinPriority:function(R){e=R,S&&S.forceMinPriority(R)}}}tryStrategy(e,r,c,h){var m=null,S=null;return c.timeout>0&&(m=new se(c.timeout,function(){S.abort(),h(!0)})),S=e.connect(r,function(T,R){T&&m&&m.isRunning()&&!c.failFast||(m&&m.ensureAborted(),h(T,R))}),{abort:function(){m&&m.ensureAborted(),S.abort()},forceMinPriority:function(T){S.forceMinPriority(T)}}}}class Ve{constructor(e){this.strategies=e}isSupported(){return xt(this.strategies,$.method("isSupported"))}connect(e,r){return zr(this.strategies,e,function(c,h){return function(m,S){if(h[c].error=m,m){Jr(h)&&r(!0);return}ge(h,function(T){T.forceMinPriority(S.transport.priority)}),r(null,S)}})}}function zr(i,e,r){var c=Ct(i,function(h,m,S,T){return h.connect(e,r(m,T))});return{abort:function(){ge(c,$r)},forceMinPriority:function(h){ge(c,function(m){m.forceMinPriority(h)})}}}function Jr(i){return rr(i,function(e){return!!e.error})}function $r(i){!i.error&&!i.aborted&&(i.abort(),i.aborted=!0)}class Xr{constructor(e,r,c){this.strategy=e,this.transports=r,this.ttl=c.ttl||1800*1e3,this.usingTLS=c.useTLS,this.timeline=c.timeline}isSupported(){return this.strategy.isSupported()}connect(e,r){var c=this.usingTLS,h=Wr(c),m=h&&h.cacheSkipCount?h.cacheSkipCount:0,S=[this.strategy];if(h&&h.timestamp+this.ttl>=$.now()){var T=this.transports[h.transport];T&&(["ws","wss"].includes(h.transport)||m>3?(this.timeline.info({cached:!0,transport:h.transport,latency:h.latency}),S.push(new ae([T],{timeout:h.latency*2+1e3,failFast:!0}))):m++)}var R=$.now(),L=S.pop().connect(e,function I(F,Oe){F?(jt(c),S.length>0?(R=$.now(),L=S.pop().connect(e,I)):r(F)):(Vr(c,Oe.transport.name,$.now()-R,m),r(null,Oe))});return{abort:function(){L.abort()},forceMinPriority:function(I){e=I,L&&L.forceMinPriority(I)}}}}function Ke(i){return"pusherTransport"+(i?"TLS":"NonTLS")}function Wr(i){var e=O.getLocalStorage();if(e)try{var r=e[Ke(i)];if(r)return JSON.parse(r)}catch{jt(i)}return null}function Vr(i,e,r,c){var h=O.getLocalStorage();if(h)try{h[Ke(i)]=Ee({timestamp:$.now(),transport:e,latency:r,cacheSkipCount:c})}catch{}}function jt(i){var e=O.getLocalStorage();if(e)try{delete e[Ke(i)]}catch{}}class Pe{constructor(e,{delay:r}){this.strategy=e,this.options={delay:r}}isSupported(){return this.strategy.isSupported()}connect(e,r){var c=this.strategy,h,m=new se(this.options.delay,function(){h=c.connect(e,r)});return{abort:function(){m.ensureAborted(),h&&h.abort()},forceMinPriority:function(S){e=S,h&&h.forceMinPriority(S)}}}}class be{constructor(e,r,c){this.test=e,this.trueBranch=r,this.falseBranch=c}isSupported(){var e=this.test()?this.trueBranch:this.falseBranch;return e.isSupported()}connect(e,r){var c=this.test()?this.trueBranch:this.falseBranch;return c.connect(e,r)}}class Kr{constructor(e){this.strategy=e}isSupported(){return this.strategy.isSupported()}connect(e,r){var c=this.strategy.connect(e,function(h,m){m&&c.abort(),r(h,m)});return c}}function ye(i){return function(){return i.isSupported()}}var Gr=function(i,e,r){var c={};function h(Xt,Ki,Gi,Qi,Yi){var Wt=r(i,Xt,Ki,Gi,Qi,Yi);return c[Xt]=Wt,Wt}var m=Object.assign({},e,{hostNonTLS:i.wsHost+":"+i.wsPort,hostTLS:i.wsHost+":"+i.wssPort,httpPath:i.wsPath}),S=Object.assign({},m,{useTLS:!0}),T=Object.assign({},e,{hostNonTLS:i.httpHost+":"+i.httpPort,hostTLS:i.httpHost+":"+i.httpsPort,httpPath:i.httpPath}),R={loop:!0,timeout:15e3,timeoutLimit:6e4},L=new It({minPingDelay:1e4,maxPingDelay:i.activityTimeout}),I=new It({lives:2,minPingDelay:1e4,maxPingDelay:i.activityTimeout}),F=h("ws","ws",3,m,L),Oe=h("wss","ws",3,S,L),Ji=h("sockjs","sockjs",1,T),Bt=h("xhr_streaming","xhr_streaming",1,T,I),$i=h("xdr_streaming","xdr_streaming",1,T,I),Ht=h("xhr_polling","xhr_polling",1,T),Xi=h("xdr_polling","xdr_polling",1,T),Mt=new ae([F],R),Wi=new ae([Oe],R),Vi=new ae([Ji],R),zt=new ae([new be(ye(Bt),Bt,$i)],R),Jt=new ae([new be(ye(Ht),Ht,Xi)],R),$t=new ae([new be(ye(zt),new Ve([zt,new Pe(Jt,{delay:4e3})]),Jt)],R),Ze=new be(ye($t),$t,Vi),et;return e.useTLS?et=new Ve([Mt,new Pe(Ze,{delay:2e3})]):et=new Ve([Mt,new Pe(Wi,{delay:2e3}),new Pe(Ze,{delay:5e3})]),new Xr(new Kr(new be(ye(F),et,Ze)),c,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})},Qr=Gr,Yr=function(){var i=this;i.timeline.info(i.buildTimelineMessage({transport:i.name+(i.options.useTLS?"s":"")})),i.hooks.isInitialized()?i.changeState("initialized"):i.hooks.file?(i.changeState("initializing"),k.load(i.hooks.file,{useTLS:i.options.useTLS},function(e,r){i.hooks.isInitialized()?(i.changeState("initialized"),r(!0)):(e&&i.onError(e),i.onClose(),r(!1))})):i.onClose()},Zr={getRequest:function(i){var e=new window.XDomainRequest;return e.ontimeout=function(){i.emit("error",new C),i.close()},e.onerror=function(r){i.emit("error",r),i.close()},e.onprogress=function(){e.responseText&&e.responseText.length>0&&i.onChunk(200,e.responseText)},e.onload=function(){e.responseText&&e.responseText.length>0&&i.onChunk(200,e.responseText),i.emit("finished",200),i.close()},e},abortRequest:function(i){i.ontimeout=i.onerror=i.onprogress=i.onload=null,i.abort()}},ei=Zr;const ti=256*1024;class ni extends te{constructor(e,r,c){super(),this.hooks=e,this.method=r,this.url=c}start(e){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},O.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(e)}close(){this.unloader&&(O.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(e,r){for(;;){var c=this.advanceBuffer(r);if(c)this.emit("chunk",{status:e,data:c});else break}this.isBufferTooLong(r)&&this.emit("buffer_too_long")}advanceBuffer(e){var r=e.slice(this.position),c=r.indexOf(`
`);return c!==-1?(this.position+=c+1,r.slice(0,c)):null}isBufferTooLong(e){return this.position===e.length&&e.length>ti}}var Ge;(function(i){i[i.CONNECTING=0]="CONNECTING",i[i.OPEN=1]="OPEN",i[i.CLOSED=3]="CLOSED"})(Ge||(Ge={}));var ce=Ge,ri=1;class ii{constructor(e,r){this.hooks=e,this.session=Ut(1e3)+"/"+ci(8),this.location=si(r),this.readyState=ce.CONNECTING,this.openStream()}send(e){return this.sendRaw(JSON.stringify([e]))}ping(){this.hooks.sendHeartbeat(this)}close(e,r){this.onClose(e,r,!0)}sendRaw(e){if(this.readyState===ce.OPEN)try{return O.createSocketRequest("POST",Dt(oi(this.location,this.session))).start(e),!0}catch{return!1}else return!1}reconnect(){this.closeStream(),this.openStream()}onClose(e,r,c){this.closeStream(),this.readyState=ce.CLOSED,this.onclose&&this.onclose({code:e,reason:r,wasClean:c})}onChunk(e){if(e.status===200){this.readyState===ce.OPEN&&this.onActivity();var r,c=e.data.slice(0,1);switch(c){case"o":r=JSON.parse(e.data.slice(1)||"{}"),this.onOpen(r);break;case"a":r=JSON.parse(e.data.slice(1)||"[]");for(var h=0;h<r.length;h++)this.onEvent(r[h]);break;case"m":r=JSON.parse(e.data.slice(1)||"null"),this.onEvent(r);break;case"h":this.hooks.onHeartbeat(this);break;case"c":r=JSON.parse(e.data.slice(1)||"[]"),this.onClose(r[0],r[1],!0);break}}}onOpen(e){this.readyState===ce.CONNECTING?(e&&e.hostname&&(this.location.base=ai(this.location.base,e.hostname)),this.readyState=ce.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(e){this.readyState===ce.OPEN&&this.onmessage&&this.onmessage({data:e})}onActivity(){this.onactivity&&this.onactivity()}onError(e){this.onerror&&this.onerror(e)}openStream(){this.stream=O.createSocketRequest("POST",Dt(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",e=>{this.onChunk(e)}),this.stream.bind("finished",e=>{this.hooks.onFinished(this,e)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(e){$.defer(()=>{this.onError(e),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}}function si(i){var e=/([^\?]*)\/*(\??.*)/.exec(i);return{base:e[1],queryString:e[2]}}function oi(i,e){return i.base+"/"+e+"/xhr_send"}function Dt(i){var e=i.indexOf("?")===-1?"?":"&";return i+e+"t="+ +new Date+"&n="+ri++}function ai(i,e){var r=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(i);return r[1]+e+r[3]}function Ut(i){return O.randomInt(i)}function ci(i){for(var e=[],r=0;r<i;r++)e.push(Ut(32).toString(32));return e.join("")}var ui=ii,li={getReceiveURL:function(i,e){return i.base+"/"+e+"/xhr_streaming"+i.queryString},onHeartbeat:function(i){i.sendRaw("[]")},sendHeartbeat:function(i){i.sendRaw("[]")},onFinished:function(i,e){i.onClose(1006,"Connection interrupted ("+e+")",!1)}},hi=li,fi={getReceiveURL:function(i,e){return i.base+"/"+e+"/xhr"+i.queryString},onHeartbeat:function(){},sendHeartbeat:function(i){i.sendRaw("[]")},onFinished:function(i,e){e===200?i.reconnect():i.onClose(1006,"Connection interrupted ("+e+")",!1)}},di=fi,pi={getRequest:function(i){var e=O.getXHRAPI(),r=new e;return r.onreadystatechange=r.onprogress=function(){switch(r.readyState){case 3:r.responseText&&r.responseText.length>0&&i.onChunk(r.status,r.responseText);break;case 4:r.responseText&&r.responseText.length>0&&i.onChunk(r.status,r.responseText),i.emit("finished",r.status),i.close();break}},r},abortRequest:function(i){i.onreadystatechange=null,i.abort()}},mi=pi,gi={createStreamingSocket(i){return this.createSocket(hi,i)},createPollingSocket(i){return this.createSocket(di,i)},createSocket(i,e){return new ui(i,e)},createXHR(i,e){return this.createRequest(mi,i,e)},createRequest(i,e,r){return new ni(i,e,r)}},Ft=gi;Ft.createXDR=function(i,e){return this.createRequest(ei,i,e)};var bi=Ft,yi={nextAuthCallbackID:1,auth_callbacks:{},ScriptReceivers:l,DependenciesReceivers:g,getDefaultStrategy:Qr,Transports:xr,transportConnectionInitializer:Yr,HTTPFactory:bi,TimelineTransport:pr,getXHRAPI(){return window.XMLHttpRequest},getWebSocketAPI(){return window.WebSocket||window.MozWebSocket},setup(i){window.Pusher=i;var e=()=>{this.onDocumentBody(i.ready)};window.JSON?e():k.load("json2",{},e)},getDocument(){return document},getProtocol(){return this.getDocument().location.protocol},getAuthorizers(){return{ajax:re,jsonp:ur}},onDocumentBody(i){document.body?i():setTimeout(()=>{this.onDocumentBody(i)},0)},createJSONPRequest(i,e){return new hr(i,e)},createScriptRequest(i){return new lr(i)},getLocalStorage(){try{return window.localStorage}catch{return}},createXHR(){return this.getXHRAPI()?this.createXMLHttpRequest():this.createMicrosoftXHR()},createXMLHttpRequest(){var i=this.getXHRAPI();return new i},createMicrosoftXHR(){return new ActiveXObject("Microsoft.XMLHTTP")},getNetwork(){return Rr},createWebSocket(i){var e=this.getWebSocketAPI();return new e(i)},createSocketRequest(i,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(i,e);if(this.isXDRSupported(e.indexOf("https:")===0))return this.HTTPFactory.createXDR(i,e);throw"Cross-origin HTTP requests are not supported"},isXHRSupported(){var i=this.getXHRAPI();return!!i&&new i().withCredentials!==void 0},isXDRSupported(i){var e=i?"https:":"http:",r=this.getProtocol();return!!window.XDomainRequest&&r===e},addUnloadListener(i){window.addEventListener!==void 0?window.addEventListener("unload",i,!1):window.attachEvent!==void 0&&window.attachEvent("onunload",i)},removeUnloadListener(i){window.addEventListener!==void 0?window.removeEventListener("unload",i,!1):window.detachEvent!==void 0&&window.detachEvent("onunload",i)},randomInt(i){return Math.floor(function(){return(window.crypto||window.msCrypto).getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32)}()*i)}},O=yi,Qe;(function(i){i[i.ERROR=3]="ERROR",i[i.INFO=6]="INFO",i[i.DEBUG=7]="DEBUG"})(Qe||(Qe={}));var Re=Qe;class vi{constructor(e,r,c){this.key=e,this.session=r,this.events=[],this.options=c||{},this.sent=0,this.uniqueID=0}log(e,r){e<=this.options.level&&(this.events.push(X({},r,{timestamp:$.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(e){this.log(Re.ERROR,e)}info(e){this.log(Re.INFO,e)}debug(e){this.log(Re.DEBUG,e)}isEmpty(){return this.events.length===0}send(e,r){var c=X({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],e(c,(h,m)=>{h||this.sent++,r&&r(h,m)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class wi{constructor(e,r,c,h){this.name=e,this.priority=r,this.transport=c,this.options=h||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(e,r){if(this.isSupported()){if(this.priority<e)return qt(new P,r)}else return qt(new M,r);var c=!1,h=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),m=null,S=function(){h.unbind("initialized",S),h.connect()},T=function(){m=ne.createHandshake(h,function(F){c=!0,I(),r(null,F)})},R=function(F){I(),r(F)},L=function(){I();var F;F=Ee(h),r(new j(F))},I=function(){h.unbind("initialized",S),h.unbind("open",T),h.unbind("error",R),h.unbind("closed",L)};return h.bind("initialized",S),h.bind("open",T),h.bind("error",R),h.bind("closed",L),h.initialize(),{abort:()=>{c||(I(),m?m.close():h.close())},forceMinPriority:F=>{c||this.priority<F&&(m?m.close():h.close())}}}}function qt(i,e){return $.defer(function(){e(i)}),{abort:function(){},forceMinPriority:function(){}}}const{Transports:_i}=O;var Si=function(i,e,r,c,h,m){var S=_i[r];if(!S)throw new U(r);var T=(!i.enabledTransports||St(i.enabledTransports,e)!==-1)&&(!i.disabledTransports||St(i.disabledTransports,e)===-1),R;return T?(h=Object.assign({ignoreNullOrigin:i.ignoreNullOrigin},h),R=new wi(e,c,m?m.getAssistant(S):S,h)):R=ki,R},ki={isSupported:function(){return!1},connect:function(i,e){var r=$.defer(function(){e(new M)});return{abort:function(){r.ensureAborted()},forceMinPriority:function(){}}}};function Ci(i){if(i==null)throw"You must pass an options object";if(i.cluster==null)throw"Options object must provide a cluster";"disableStats"in i&&D.warn("The disableStats option is deprecated in favor of enableStats")}const Ti=(i,e)=>{var r="socket_id="+encodeURIComponent(i.socketId);for(var c in e.params)r+="&"+encodeURIComponent(c)+"="+encodeURIComponent(e.params[c]);if(e.paramsProvider!=null){let h=e.paramsProvider();for(var c in h)r+="&"+encodeURIComponent(c)+"="+encodeURIComponent(h[c])}return r};var Ei=i=>{if(typeof O.getAuthorizers()[i.transport]>"u")throw`'${i.transport}' is not a recognized auth transport`;return(e,r)=>{const c=Ti(e,i);O.getAuthorizers()[i.transport](O,c,i,v.UserAuthentication,r)}};const xi=(i,e)=>{var r="socket_id="+encodeURIComponent(i.socketId);r+="&channel_name="+encodeURIComponent(i.channelName);for(var c in e.params)r+="&"+encodeURIComponent(c)+"="+encodeURIComponent(e.params[c]);if(e.paramsProvider!=null){let h=e.paramsProvider();for(var c in h)r+="&"+encodeURIComponent(c)+"="+encodeURIComponent(h[c])}return r};var Pi=i=>{if(typeof O.getAuthorizers()[i.transport]>"u")throw`'${i.transport}' is not a recognized auth transport`;return(e,r)=>{const c=xi(e,i);O.getAuthorizers()[i.transport](O,c,i,v.ChannelAuthorization,r)}};const Ri=(i,e,r)=>{const c={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(h,m)=>{const S=i.channel(h.channelName);r(S,c).authorize(h.socketId,m)}};function Oi(i,e){let r={activityTimeout:i.activityTimeout||b.activityTimeout,cluster:i.cluster,httpPath:i.httpPath||b.httpPath,httpPort:i.httpPort||b.httpPort,httpsPort:i.httpsPort||b.httpsPort,pongTimeout:i.pongTimeout||b.pongTimeout,statsHost:i.statsHost||b.stats_host,unavailableTimeout:i.unavailableTimeout||b.unavailableTimeout,wsPath:i.wsPath||b.wsPath,wsPort:i.wsPort||b.wsPort,wssPort:i.wssPort||b.wssPort,enableStats:ji(i),httpHost:Ai(i),useTLS:Ii(i),wsHost:Li(i),userAuthenticator:Di(i),channelAuthorizer:Fi(i,e)};return"disabledTransports"in i&&(r.disabledTransports=i.disabledTransports),"enabledTransports"in i&&(r.enabledTransports=i.enabledTransports),"ignoreNullOrigin"in i&&(r.ignoreNullOrigin=i.ignoreNullOrigin),"timelineParams"in i&&(r.timelineParams=i.timelineParams),"nacl"in i&&(r.nacl=i.nacl),r}function Ai(i){return i.httpHost?i.httpHost:i.cluster?`sockjs-${i.cluster}.pusher.com`:b.httpHost}function Li(i){return i.wsHost?i.wsHost:Ni(i.cluster)}function Ni(i){return`ws-${i}.pusher.com`}function Ii(i){return O.getProtocol()==="https:"?!0:i.forceTLS!==!1}function ji(i){return"enableStats"in i?i.enableStats:"disableStats"in i?!i.disableStats:!1}function Di(i){const e=Object.assign(Object.assign({},b.userAuthentication),i.userAuthentication);return"customHandler"in e&&e.customHandler!=null?e.customHandler:Ei(e)}function Ui(i,e){let r;return"channelAuthorization"in i?r=Object.assign(Object.assign({},b.channelAuthorization),i.channelAuthorization):(r={transport:i.authTransport||b.authTransport,endpoint:i.authEndpoint||b.authEndpoint},"auth"in i&&("params"in i.auth&&(r.params=i.auth.params),"headers"in i.auth&&(r.headers=i.auth.headers)),"authorizer"in i&&(r.customHandler=Ri(e,r,i.authorizer))),r}function Fi(i,e){const r=Ui(i,e);return"customHandler"in r&&r.customHandler!=null?r.customHandler:Pi(r)}class qi extends te{constructor(e){super(function(r,c){D.debug(`No callbacks on watchlist events for ${r}`)}),this.pusher=e,this.bindWatchlistInternalEvent()}handleEvent(e){e.data.events.forEach(r=>{this.emit(r.name,r)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",e=>{var r=e.event;r==="pusher_internal:watchlist_events"&&this.handleEvent(e)})}}function Bi(){let i,e;return{promise:new Promise((c,h)=>{i=c,e=h}),resolve:i,reject:e}}var Hi=Bi;class Mi extends te{constructor(e){super(function(r,c){D.debug("No callbacks on user for "+r)}),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(r,c)=>{if(r){D.warn(`Error during signin: ${r}`),this._cleanup();return}this.pusher.send_event("pusher:signin",{auth:c.auth,user_data:c.user_data})},this.pusher=e,this.pusher.connection.bind("state_change",({previous:r,current:c})=>{r!=="connected"&&c==="connected"&&this._signin(),r==="connected"&&c!=="connected"&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new qi(e),this.pusher.connection.bind("message",r=>{var c=r.event;c==="pusher:signin_success"&&this._onSigninSuccess(r.data),this.serverToUserChannel&&this.serverToUserChannel.name===r.channel&&this.serverToUserChannel.handleEvent(r)})}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),this.pusher.connection.state==="connected"&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(e){try{this.user_data=JSON.parse(e.user_data)}catch{D.error(`Failed parsing user data after signin: ${e.user_data}`),this._cleanup();return}if(typeof this.user_data.id!="string"||this.user_data.id===""){D.error(`user_data doesn't contain an id. user_data: ${this.user_data}`),this._cleanup();return}this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){const e=r=>{r.subscriptionPending&&r.subscriptionCancelled?r.reinstateSubscription():!r.subscriptionPending&&this.pusher.connection.state==="connected"&&r.subscribe()};this.serverToUserChannel=new $e(`#server-to-user-${this.user_data.id}`,this.pusher),this.serverToUserChannel.bind_global((r,c)=>{r.indexOf("pusher_internal:")===0||r.indexOf("pusher:")===0||this.emit(r,c)}),e(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested||this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:e,resolve:r,reject:c}=Hi();e.done=!1;const h=()=>{e.done=!0};e.then(h).catch(h),this.signinDonePromise=e,this._signinDoneResolve=r}}class J{static ready(){J.isReady=!0;for(var e=0,r=J.instances.length;e<r;e++)J.instances[e].connect()}static getClientFeatures(){return kt(Et({ws:O.Transports.ws},function(e){return e.isSupported({})}))}constructor(e,r){zi(e),Ci(r),this.key=e,this.config=Oi(r,this),this.channels=ne.createChannels(),this.global_emitter=new te,this.sessionID=O.randomInt(1e9),this.timeline=new vi(this.key,this.sessionID,{cluster:this.config.cluster,features:J.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:Re.INFO,version:b.VERSION}),this.config.enableStats&&(this.timelineSender=ne.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+O.TimelineTransport.name}));var c=h=>O.getDefaultStrategy(this.config,h,Si);this.connection=ne.createConnectionManager(this.key,{getStrategy:c,timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:!!this.config.useTLS}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",h=>{var m=h.event,S=m.indexOf("pusher_internal:")===0;if(h.channel){var T=this.channel(h.channel);T&&T.handleEvent(h)}S||this.global_emitter.emit(h.event,h.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",h=>{D.warn(h)}),J.instances.push(this),this.timeline.info({instances:J.instances.length}),this.user=new Mi(this),J.isReady&&this.connect()}channel(e){return this.channels.find(e)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var e=this.connection.isUsingTLS(),r=this.timelineSender;this.timelineSenderTimer=new Qn(6e4,function(){r.send(e)})}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(e,r,c){return this.global_emitter.bind(e,r,c),this}unbind(e,r,c){return this.global_emitter.unbind(e,r,c),this}bind_global(e){return this.global_emitter.bind_global(e),this}unbind_global(e){return this.global_emitter.unbind_global(e),this}unbind_all(e){return this.global_emitter.unbind_all(),this}subscribeAll(){var e;for(e in this.channels.channels)this.channels.channels.hasOwnProperty(e)&&this.subscribe(e)}subscribe(e){var r=this.channels.add(e,this);return r.subscriptionPending&&r.subscriptionCancelled?r.reinstateSubscription():!r.subscriptionPending&&this.connection.state==="connected"&&r.subscribe(),r}unsubscribe(e){var r=this.channels.find(e);r&&r.subscriptionPending?r.cancelSubscription():(r=this.channels.remove(e),r&&r.subscribed&&r.unsubscribe())}send_event(e,r,c){return this.connection.send_event(e,r,c)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}J.instances=[],J.isReady=!1,J.logToConsole=!1,J.Runtime=O,J.ScriptReceivers=O.ScriptReceivers,J.DependenciesReceivers=O.DependenciesReceivers,J.auth_callbacks=O.auth_callbacks;var Ye=o.default=J;function zi(i){if(i==null)throw"You must pass your app key when you instantiate Pusher."}O.setup(J)}])})})(Mn);var zo=Mn.exports;const Jo=Mo(zo);window.Pusher=Jo;window.Echo=new Ho({broadcaster:"reverb",key:"itaitrdi1iolivmpvnmn",wsHost:"sw.harajy.s3f.sa",wsPort:"443",wssPort:"443",forceTLS:!0,enabledTransports:["ws","wss"]});window.axios=q;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const hn=document.head.querySelector('meta[name="csrf-token"]');hn?window.axios.defaults.headers.common["X-CSRF-TOKEN"]=hn.content:console.error("CSRF token not found");document.addEventListener("DOMContentLoaded",()=>{const t=localStorage.getItem("theme")||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");fn(t);const n=document.getElementById("dark-mode-toggle");n&&n.addEventListener("click",()=>{const o=(document.documentElement.classList.contains("dark")?"dark":"light")==="dark"?"light":"dark";fn(o),localStorage.setItem("theme",o)})});function fn(t){t==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}document.addEventListener("bookContentLoaded",()=>{console.log("Book content loaded")});
