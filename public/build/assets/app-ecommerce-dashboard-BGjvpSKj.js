(function(){let l,r,n,s;isDarkStyle?(l=config.colors_dark.cardColor,r=config.colors_dark.textMuted,config.colors_dark.bodyColor,n=config.colors_dark.headingColor,s=config.colors_dark.borderColor):(l=config.colors.cardColor,r=config.colors.textMuted,config.colors.bodyColor,n=config.colors.headingColor,s=config.colors.borderColor);const c={donut:{series1:"#24B364",series2:"#53D28C",series3:"#7EDDA9",series4:"#A9E9C5"}},p=document.querySelector("#expensesChart"),w={chart:{height:170,sparkline:{enabled:!0},parentHeightOffset:0,type:"radialBar"},colors:[config.colors.warning],series:[78],plotOptions:{radialBar:{offsetY:0,startAngle:-90,endAngle:90,hollow:{size:"65%"},track:{strokeWidth:"45%",background:s},dataLabels:{name:{show:!1},value:{fontSize:"24px",color:n,fontWeight:500,offsetY:-5}}}},grid:{show:!1,padding:{bottom:5}},stroke:{lineCap:"round"},labels:["Progress"],responsive:[{breakpoint:1442,options:{chart:{height:120},plotOptions:{radialBar:{dataLabels:{value:{fontSize:"18px"}},hollow:{size:"60%"}}}}},{breakpoint:1025,options:{chart:{height:136},plotOptions:{radialBar:{hollow:{size:"65%"},dataLabels:{value:{fontSize:"18px"}}}}}},{breakpoint:769,options:{chart:{height:120},plotOptions:{radialBar:{hollow:{size:"55%"}}}}},{breakpoint:426,options:{chart:{height:145},plotOptions:{radialBar:{hollow:{size:"65%"}}},dataLabels:{value:{offsetY:0}}}},{breakpoint:376,options:{chart:{height:105},plotOptions:{radialBar:{hollow:{size:"60%"}}}}}]};typeof p!==void 0&&p!==null&&new ApexCharts(p,w).render();const h=document.querySelector("#profitLastMonth"),x={chart:{height:110,type:"line",parentHeightOffset:0,toolbar:{show:!1}},grid:{borderColor:s,strokeDashArray:6,xaxis:{lines:{show:!0,colors:"#000"}},yaxis:{lines:{show:!1}},padding:{top:-18,left:-4,right:7,bottom:-10}},colors:[config.colors.info],stroke:{width:2},series:[{data:[0,25,10,40,25,55]}],tooltip:{shared:!1,intersect:!0,x:{show:!1}},xaxis:{labels:{show:!1},axisTicks:{show:!1},axisBorder:{show:!1}},yaxis:{labels:{show:!1}},tooltip:{enabled:!1},markers:{size:3.5,fillColor:config.colors.info,strokeColors:"transparent",strokeWidth:3.2,discrete:[{seriesIndex:0,dataPointIndex:5,fillColor:l,strokeColor:config.colors.info,size:5,shape:"circle"}],hover:{size:5.5}},responsive:[{breakpoint:1442,options:{chart:{height:100}}},{breakpoint:1025,options:{chart:{height:86}}},{breakpoint:769,options:{chart:{height:93}}}]};typeof h!==void 0&&h!==null&&new ApexCharts(h,x).render();const f=document.querySelector("#generatedLeadsChart"),v={chart:{height:125,width:120,parentHeightOffset:0,type:"donut"},labels:["Electronic","Sports","Decor","Fashion"],series:[45,58,30,50],colors:[c.donut.series1,c.donut.series2,c.donut.series3,c.donut.series4],stroke:{width:0},dataLabels:{enabled:!1,formatter:function(e,a){return parseInt(e)+"%"}},legend:{show:!1},tooltip:{theme:!1},grid:{padding:{top:15,right:-20,left:-20}},states:{hover:{filter:{type:"none"}}},plotOptions:{pie:{donut:{size:"70%",labels:{show:!0,value:{fontSize:"1.5rem",fontFamily:"Public Sans",color:n,fontWeight:500,offsetY:-15,formatter:function(e){return parseInt(e)+"%"}},name:{offsetY:20,fontFamily:"Public Sans"},total:{show:!0,showAlways:!0,color:config.colors.success,fontSize:".8125rem",label:"Total",fontFamily:"Public Sans",formatter:function(e){return"184"}}}}}},responsive:[{breakpoint:1025,options:{chart:{height:172,width:160}}},{breakpoint:769,options:{chart:{height:178}}},{breakpoint:426,options:{chart:{height:147}}}]};typeof f!==void 0&&f!==null&&new ApexCharts(f,v).render();const u=document.querySelector("#totalRevenueChart"),y={series:[{name:"Earning",data:[270,210,180,200,250,280,250,270,150]},{name:"Expense",data:[-140,-160,-180,-150,-100,-60,-80,-100,-180]}],chart:{height:413,parentHeightOffset:0,stacked:!0,type:"bar",toolbar:{show:!1}},tooltip:{enabled:!1},plotOptions:{bar:{horizontal:!1,columnWidth:"40%",borderRadius:9,startingShape:"rounded",endingShape:"rounded"}},colors:[config.colors.primary,config.colors.warning],dataLabels:{enabled:!1},stroke:{curve:"smooth",width:6,lineCap:"round",colors:[l]},legend:{show:!0,horizontalAlign:"right",position:"top",fontSize:"13px",fontFamily:"Public Sans",markers:{height:12,width:12,radius:12,offsetX:-5,offsetY:2},labels:{colors:n},itemMargin:{horizontal:10,vertical:2}},grid:{show:!1,padding:{bottom:-8,top:20}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep"],labels:{style:{fontSize:"13px",colors:r,fontFamily:"Public Sans"}},axisTicks:{show:!1},axisBorder:{show:!1}},yaxis:{labels:{offsetX:-16,style:{fontSize:"13px",colors:r,fontFamily:"Public Sans"}},min:-200,max:300,tickAmount:5},responsive:[{breakpoint:1700,options:{plotOptions:{bar:{columnWidth:"43%"}}}},{breakpoint:1441,options:{plotOptions:{bar:{columnWidth:"50%"}},chart:{height:422}}},{breakpoint:1300,options:{plotOptions:{bar:{columnWidth:"50%"}}}},{breakpoint:1025,options:{plotOptions:{bar:{columnWidth:"50%"}},chart:{height:390}}},{breakpoint:991,options:{plotOptions:{bar:{columnWidth:"38%"}}}},{breakpoint:850,options:{plotOptions:{bar:{columnWidth:"50%"}}}},{breakpoint:449,options:{plotOptions:{bar:{columnWidth:"73%"}},chart:{height:360},xaxis:{labels:{offsetY:-5}},legend:{show:!0,horizontalAlign:"right",position:"top",itemMargin:{horizontal:10,vertical:0}}}},{breakpoint:394,options:{plotOptions:{bar:{columnWidth:"88%"}},legend:{show:!0,horizontalAlign:"center",position:"bottom",markers:{offsetX:-3,offsetY:0},itemMargin:{horizontal:10,vertical:5}}}}],states:{hover:{filter:{type:"none"}},active:{filter:{type:"none"}}}};typeof u!==void 0&&u!==null&&new ApexCharts(u,y).render();const b=document.querySelector("#budgetChart"),C={chart:{height:100,toolbar:{show:!1},zoom:{enabled:!1},type:"line"},series:[{name:"Last Month",data:[20,10,30,16,24,5,40,23,28,5,30]},{name:"This Month",data:[50,40,60,46,54,35,70,53,58,35,60]}],stroke:{curve:"smooth",dashArray:[5,0],width:[1,2]},legend:{show:!1},colors:[s,config.colors.primary],grid:{show:!1,borderColor:s,padding:{top:-30,bottom:-15,left:25}},markers:{size:0},xaxis:{labels:{show:!1},axisTicks:{show:!1},axisBorder:{show:!1}},yaxis:{show:!1},tooltip:{enabled:!1}};typeof b!==void 0&&b!==null&&new ApexCharts(b,C).render();const g=document.querySelector("#reportBarChart"),k={chart:{height:230,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{barHeight:"60%",columnWidth:"60%",startingShape:"rounded",endingShape:"rounded",borderRadius:4,distributed:!0}},grid:{show:!1,padding:{top:-20,bottom:0,left:-10,right:-10}},colors:[config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors.primary,config.colors_label.primary,config.colors_label.primary],dataLabels:{enabled:!1},series:[{data:[40,95,60,45,90,50,75]}],legend:{show:!1},xaxis:{categories:["Mo","Tu","We","Th","Fr","Sa","Su"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:r,fontSize:"13px"}}},yaxis:{labels:{show:!1}},responsive:[{breakpoint:1025,options:{chart:{height:190}}},{breakpoint:769,options:{chart:{height:250}}}]};typeof g!==void 0&&g!==null&&new ApexCharts(g,k).render();var m=$(".datatable-invoice");if(m.length)var S=m.DataTable({ajax:assetsPath+"json/invoice-list.json",columns:[{data:"invoice_id"},{data:"invoice_id"},{data:"invoice_id"},{data:"invoice_status"},{data:"total"},{data:"issued_date"},{data:"invoice_status"},{data:"action"}],columnDefs:[{className:"control",responsivePriority:2,searchable:!1,targets:0,render:function(e,a,t,i){return""}},{targets:1,orderable:!1,checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'},render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input" >'},searchable:!1},{targets:2,render:function(e,a,t,i){var o=t.invoice_id,d='<a href="'+baseUrl+'app/invoice/preview">#'+o+"</a>";return d}},{targets:3,render:function(e,a,t,i){var o=t.invoice_status,d=t.due_date,_=t.balance,z={Sent:'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-secondary w-px-30 h-px-30"><i class="ti ti-circle-check ti-xs"></i></span>',Draft:'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-primary w-px-30 h-px-30"><i class="ti ti-device-floppy ti-xs"></i></span>',"Past Due":'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-danger w-px-30 h-px-30"><i class="ti ti-info-circle ti-xs"></i></span>',"Partial Payment":'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-success w-px-30 h-px-30"><i class="ti ti-circle-half-2 ti-xs"></i></span>',Paid:'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-warning w-px-30 h-px-30"><i class="ti ti-chart-pie ti-xs"></i></span>',Downloaded:'<span class="badge badge-center d-flex align-items-center justify-content-center rounded-pill bg-label-info w-px-30 h-px-30"><i class="ti ti-arrow-down-circle ti-xs"></i></span>'};return"<span class='d-inline-block' data-bs-toggle='tooltip' data-bs-html='true' title='<span>"+o+'<br> <span class="fw-medium">Balance:</span> '+_+'<br> <span class="fw-medium">Due Date:</span> '+d+"</span>'>"+z[o]+"</span>"}},{targets:4,render:function(e,a,t,i){var o=t.total;return"$"+o}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(e,a,t,i){return'<div class="d-flex align-items-center"><a href="javascript:;" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill delete-record" data-bs-toggle="tooltip" title="Delete record"><i class="ti ti-trash ti-md"></i></a><a href="'+baseUrl+'app/invoice/preview" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill" data-bs-toggle="tooltip" title="Preview"><i class="ti ti-eye ti-md"></i></a><div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-icon dropdown-toggle hide-arrow btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><ul class="dropdown-menu dropdown-menu-end m-0"><li><a href="javascript:;" class="dropdown-item">Details</a></li><li><a href="javascript:;" class="dropdown-item">Archive</a></li><div class="dropdown-divider"></div><li><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></li></ul></div></div>'}},{targets:-2,visible:!1}],order:[[1,"desc"]],dom:'<"row"<"col-12 col-md-6 d-flex align-items-center justify-content-center justify-content-md-start gap-2"l<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start"B>><"col-12 col-md-6 d-flex align-items-center justify-content-end flex-column flex-md-row pe-5 gap-md-4 mt-n5 mt-md-0"f<"invoice_status mb-6 mb-md-0">>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:6,lengthMenu:[6,10,25,50,75,100],language:{sLengthMenu:"Show _MENU_",search:"",searchPlaceholder:"Search Invoice",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{text:'<i class="ti ti-plus ti-xs me-md-2"></i><span class="d-md-inline-block d-none">Create Invoice</span>',className:"btn btn-primary waves-effect waves-light",action:function(e,a,t,i){window.location=baseUrl+"app/invoice/add"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(e){var a=e.data();return"Details of "+a.full_name}}),type:"column",renderer:function(e,a,t){var i=$.map(t,function(o,d){return o.title!==""?'<tr data-dt-row="'+o.rowIndex+'" data-dt-column="'+o.columnIndex+'"><td>'+o.title+":</td> <td>"+o.data+"</td></tr>":""}).join("");return i?$('<table class="table"/><tbody />').append(i):!1}}},initComplete:function(){this.api().columns(6).every(function(){var e=this,a=$('<select id="UserRole" class="form-select"><option value=""> Select Status </option></select>').appendTo(".invoice_status").on("change",function(){var t=$.fn.dataTable.util.escapeRegex($(this).val());e.search(t?"^"+t+"$":"",!0,!1).draw()});e.data().unique().sort().each(function(t,i){a.append('<option value="'+t+'" class="text-capitalize">'+t+"</option>")})})}});m.on("draw.dt",function(){var e=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));e.map(function(a){return new bootstrap.Tooltip(a,{boundary:document.body})})}),$(".datatable-invoice tbody").on("click",".delete-record",function(){S.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)})();
