var k={exports:{}},F=k.exports,S;function C(){return S||(S=1,function(y,I){(function(p,s){y.exports=s()})(F,function(){return function(f){var p={};function s(n){if(p[n])return p[n].exports;var t=p[n]={exports:{},id:n,loaded:!1};return f[n].call(t.exports,t,t.exports,s),t.loaded=!0,t.exports}return s.m=f,s.c=p,s.p="",s(0)}([function(f,p,s){(function(n){var t=function(e,r){var i=this,a=!1;if(typeof e=="string"?(i.element=document.querySelector(e),a=document.querySelectorAll(e).length>1):typeof e.length<"u"&&e.length>0?(i.element=e[0],a=e.length>1):i.element=e,!i.element)throw new Error("[cleave.js] Please check the element");if(a)try{console.warn("[cleave.js] Multiple input fields matched, cleave.js will only take the first one.")}catch{}r.initValue=i.element.value,i.properties=t.DefaultProperties.assign({},r),i.init()};t.prototype={init:function(){var e=this,r=e.properties;if(!r.numeral&&!r.phone&&!r.creditCard&&!r.time&&!r.date&&r.blocksLength===0&&!r.prefix){e.onInput(r.initValue);return}r.maxLength=t.Util.getMaxLength(r.blocks),e.isAndroid=t.Util.isAndroid(),e.lastInputValue="",e.isBackward="",e.onChangeListener=e.onChange.bind(e),e.onKeyDownListener=e.onKeyDown.bind(e),e.onFocusListener=e.onFocus.bind(e),e.onCutListener=e.onCut.bind(e),e.onCopyListener=e.onCopy.bind(e),e.initSwapHiddenInput(),e.element.addEventListener("input",e.onChangeListener),e.element.addEventListener("keydown",e.onKeyDownListener),e.element.addEventListener("focus",e.onFocusListener),e.element.addEventListener("cut",e.onCutListener),e.element.addEventListener("copy",e.onCopyListener),e.initPhoneFormatter(),e.initDateFormatter(),e.initTimeFormatter(),e.initNumeralFormatter(),(r.initValue||r.prefix&&!r.noImmediatePrefix)&&e.onInput(r.initValue)},initSwapHiddenInput:function(){var e=this,r=e.properties;if(r.swapHiddenInput){var i=e.element.cloneNode(!0);e.element.parentNode.insertBefore(i,e.element),e.elementSwapHidden=e.element,e.elementSwapHidden.type="hidden",e.element=i,e.element.id=""}},initNumeralFormatter:function(){var e=this,r=e.properties;r.numeral&&(r.numeralFormatter=new t.NumeralFormatter(r.numeralDecimalMark,r.numeralIntegerScale,r.numeralDecimalScale,r.numeralThousandsGroupStyle,r.numeralPositiveOnly,r.stripLeadingZeroes,r.prefix,r.signBeforePrefix,r.tailPrefix,r.delimiter))},initTimeFormatter:function(){var e=this,r=e.properties;r.time&&(r.timeFormatter=new t.TimeFormatter(r.timePattern,r.timeFormat),r.blocks=r.timeFormatter.getBlocks(),r.blocksLength=r.blocks.length,r.maxLength=t.Util.getMaxLength(r.blocks))},initDateFormatter:function(){var e=this,r=e.properties;r.date&&(r.dateFormatter=new t.DateFormatter(r.datePattern,r.dateMin,r.dateMax),r.blocks=r.dateFormatter.getBlocks(),r.blocksLength=r.blocks.length,r.maxLength=t.Util.getMaxLength(r.blocks))},initPhoneFormatter:function(){var e=this,r=e.properties;if(r.phone)try{r.phoneFormatter=new t.PhoneFormatter(new r.root.Cleave.AsYouTypeFormatter(r.phoneRegionCode),r.delimiter)}catch{throw new Error("[cleave.js] Please include phone-type-formatter.{country}.js lib")}},onKeyDown:function(e){var r=this,i=e.which||e.keyCode;r.lastInputValue=r.element.value,r.isBackward=i===8},onChange:function(e){var r=this,i=r.properties,a=t.Util;r.isBackward=r.isBackward||e.inputType==="deleteContentBackward";var o=a.getPostDelimiter(r.lastInputValue,i.delimiter,i.delimiters);r.isBackward&&o?i.postDelimiterBackspace=o:i.postDelimiterBackspace=!1,this.onInput(this.element.value)},onFocus:function(){var e=this,r=e.properties;e.lastInputValue=e.element.value,r.prefix&&r.noImmediatePrefix&&!e.element.value&&this.onInput(r.prefix),t.Util.fixPrefixCursor(e.element,r.prefix,r.delimiter,r.delimiters)},onCut:function(e){t.Util.checkFullSelection(this.element.value)&&(this.copyClipboardData(e),this.onInput(""))},onCopy:function(e){t.Util.checkFullSelection(this.element.value)&&this.copyClipboardData(e)},copyClipboardData:function(e){var r=this,i=r.properties,a=t.Util,o=r.element.value,l="";i.copyDelimiter?l=o:l=a.stripDelimiters(o,i.delimiter,i.delimiters);try{e.clipboardData?e.clipboardData.setData("Text",l):window.clipboardData.setData("Text",l),e.preventDefault()}catch{}},onInput:function(e){var r=this,i=r.properties,a=t.Util,o=a.getPostDelimiter(e,i.delimiter,i.delimiters);if(!i.numeral&&i.postDelimiterBackspace&&!o&&(e=a.headStr(e,e.length-i.postDelimiterBackspace.length)),i.phone){i.prefix&&(!i.noImmediatePrefix||e.length)?i.result=i.prefix+i.phoneFormatter.format(e).slice(i.prefix.length):i.result=i.phoneFormatter.format(e),r.updateValueState();return}if(i.numeral){i.prefix&&i.noImmediatePrefix&&e.length===0?i.result="":i.result=i.numeralFormatter.format(e),r.updateValueState();return}if(i.date&&(e=i.dateFormatter.getValidatedDate(e)),i.time&&(e=i.timeFormatter.getValidatedTime(e)),e=a.stripDelimiters(e,i.delimiter,i.delimiters),e=a.getPrefixStrippedValue(e,i.prefix,i.prefixLength,i.result,i.delimiter,i.delimiters,i.noImmediatePrefix,i.tailPrefix,i.signBeforePrefix),e=i.numericOnly?a.strip(e,/[^\d]/g):e,e=i.uppercase?e.toUpperCase():e,e=i.lowercase?e.toLowerCase():e,i.prefix&&(i.tailPrefix?e=e+i.prefix:e=i.prefix+e,i.blocksLength===0)){i.result=e,r.updateValueState();return}i.creditCard&&r.updateCreditCardPropsByValue(e),e=a.headStr(e,i.maxLength),i.result=a.getFormattedValue(e,i.blocks,i.blocksLength,i.delimiter,i.delimiters,i.delimiterLazyShow),r.updateValueState()},updateCreditCardPropsByValue:function(e){var r=this,i=r.properties,a=t.Util,o;a.headStr(i.result,4)!==a.headStr(e,4)&&(o=t.CreditCardDetector.getInfo(e,i.creditCardStrictMode),i.blocks=o.blocks,i.blocksLength=i.blocks.length,i.maxLength=a.getMaxLength(i.blocks),i.creditCardType!==o.type&&(i.creditCardType=o.type,i.onCreditCardTypeChanged.call(r,i.creditCardType)))},updateValueState:function(){var e=this,r=t.Util,i=e.properties;if(e.element){var a=e.element.selectionEnd,o=e.element.value,l=i.result;if(a=r.getNextCursorPosition(a,o,l,i.delimiter,i.delimiters),e.isAndroid){window.setTimeout(function(){e.element.value=l,r.setSelection(e.element,a,i.document,!1),e.callOnValueChanged()},1);return}e.element.value=l,i.swapHiddenInput&&(e.elementSwapHidden.value=e.getRawValue()),r.setSelection(e.element,a,i.document,!1),e.callOnValueChanged()}},callOnValueChanged:function(){var e=this,r=e.properties;r.onValueChanged.call(e,{target:{name:e.element.name,value:r.result,rawValue:e.getRawValue()}})},setPhoneRegionCode:function(e){var r=this,i=r.properties;i.phoneRegionCode=e,r.initPhoneFormatter(),r.onChange()},setRawValue:function(e){var r=this,i=r.properties;e=e!=null?e.toString():"",i.numeral&&(e=e.replace(".",i.numeralDecimalMark)),i.postDelimiterBackspace=!1,r.element.value=e,r.onInput(e)},getRawValue:function(){var e=this,r=e.properties,i=t.Util,a=e.element.value;return r.rawValueTrimPrefix&&(a=i.getPrefixStrippedValue(a,r.prefix,r.prefixLength,r.result,r.delimiter,r.delimiters,r.noImmediatePrefix,r.tailPrefix,r.signBeforePrefix)),r.numeral?a=r.numeralFormatter.getRawValue(a):a=i.stripDelimiters(a,r.delimiter,r.delimiters),a},getISOFormatDate:function(){var e=this,r=e.properties;return r.date?r.dateFormatter.getISOFormatDate():""},getISOFormatTime:function(){var e=this,r=e.properties;return r.time?r.timeFormatter.getISOFormatTime():""},getFormattedValue:function(){return this.element.value},destroy:function(){var e=this;e.element.removeEventListener("input",e.onChangeListener),e.element.removeEventListener("keydown",e.onKeyDownListener),e.element.removeEventListener("focus",e.onFocusListener),e.element.removeEventListener("cut",e.onCutListener),e.element.removeEventListener("copy",e.onCopyListener)},toString:function(){return"[Cleave Object]"}},t.NumeralFormatter=s(1),t.DateFormatter=s(2),t.TimeFormatter=s(3),t.PhoneFormatter=s(4),t.CreditCardDetector=s(5),t.Util=s(6),t.DefaultProperties=s(7),(typeof n=="object"&&n?n:window).Cleave=t,f.exports=t}).call(p,function(){return this}())},function(f,p){var s=function(n,t,e,r,i,a,o,l,c,d){var m=this;m.numeralDecimalMark=n||".",m.numeralIntegerScale=t>0?t:0,m.numeralDecimalScale=e>=0?e:2,m.numeralThousandsGroupStyle=r||s.groupStyle.thousand,m.numeralPositiveOnly=!!i,m.stripLeadingZeroes=a!==!1,m.prefix=o||o===""?o:"",m.signBeforePrefix=!!l,m.tailPrefix=!!c,m.delimiter=d||d===""?d:",",m.delimiterRE=d?new RegExp("\\"+d,"g"):""};s.groupStyle={thousand:"thousand",lakh:"lakh",wan:"wan",none:"none"},s.prototype={getRawValue:function(n){return n.replace(this.delimiterRE,"").replace(this.numeralDecimalMark,".")},format:function(n){var t=this,e,r,i,a,o="";switch(n=n.replace(/[A-Za-z]/g,"").replace(t.numeralDecimalMark,"M").replace(/[^\dM-]/g,"").replace(/^\-/,"N").replace(/\-/g,"").replace("N",t.numeralPositiveOnly?"":"-").replace("M",t.numeralDecimalMark),t.stripLeadingZeroes&&(n=n.replace(/^(-)?0+(?=\d)/,"$1")),r=n.slice(0,1)==="-"?"-":"",typeof t.prefix<"u"?t.signBeforePrefix?i=r+t.prefix:i=t.prefix+r:i=r,a=n,n.indexOf(t.numeralDecimalMark)>=0&&(e=n.split(t.numeralDecimalMark),a=e[0],o=t.numeralDecimalMark+e[1].slice(0,t.numeralDecimalScale)),r==="-"&&(a=a.slice(1)),t.numeralIntegerScale>0&&(a=a.slice(0,t.numeralIntegerScale)),t.numeralThousandsGroupStyle){case s.groupStyle.lakh:a=a.replace(/(\d)(?=(\d\d)+\d$)/g,"$1"+t.delimiter);break;case s.groupStyle.wan:a=a.replace(/(\d)(?=(\d{4})+$)/g,"$1"+t.delimiter);break;case s.groupStyle.thousand:a=a.replace(/(\d)(?=(\d{3})+$)/g,"$1"+t.delimiter);break}return t.tailPrefix?r+a.toString()+(t.numeralDecimalScale>0?o.toString():"")+t.prefix:i+a.toString()+(t.numeralDecimalScale>0?o.toString():"")}},f.exports=s},function(f,p){var s=function(n,t,e){var r=this;r.date=[],r.blocks=[],r.datePattern=n,r.dateMin=t.split("-").reverse().map(function(i){return parseInt(i,10)}),r.dateMin.length===2&&r.dateMin.unshift(0),r.dateMax=e.split("-").reverse().map(function(i){return parseInt(i,10)}),r.dateMax.length===2&&r.dateMax.unshift(0),r.initBlocks()};s.prototype={initBlocks:function(){var n=this;n.datePattern.forEach(function(t){t==="Y"?n.blocks.push(4):n.blocks.push(2)})},getISOFormatDate:function(){var n=this,t=n.date;return t[2]?t[2]+"-"+n.addLeadingZero(t[1])+"-"+n.addLeadingZero(t[0]):""},getBlocks:function(){return this.blocks},getValidatedDate:function(n){var t=this,e="";return n=n.replace(/[^\d]/g,""),t.blocks.forEach(function(r,i){if(n.length>0){var a=n.slice(0,r),o=a.slice(0,1),l=n.slice(r);switch(t.datePattern[i]){case"d":a==="00"?a="01":parseInt(o,10)>3?a="0"+o:parseInt(a,10)>31&&(a="31");break;case"m":a==="00"?a="01":parseInt(o,10)>1?a="0"+o:parseInt(a,10)>12&&(a="12");break}e+=a,n=l}}),this.getFixedDateString(e)},getFixedDateString:function(n){var t=this,e=t.datePattern,r=[],i=0,a=0,o=0,l=0,c=0,d=0,m,u,h,g=!1;n.length===4&&e[0].toLowerCase()!=="y"&&e[1].toLowerCase()!=="y"&&(l=e[0]==="d"?0:2,c=2-l,m=parseInt(n.slice(l,l+2),10),u=parseInt(n.slice(c,c+2),10),r=this.getFixedDate(m,u,0)),n.length===8&&(e.forEach(function(w,D){switch(w){case"d":i=D;break;case"m":a=D;break;default:o=D;break}}),d=o*2,l=i<=o?i*2:i*2+2,c=a<=o?a*2:a*2+2,m=parseInt(n.slice(l,l+2),10),u=parseInt(n.slice(c,c+2),10),h=parseInt(n.slice(d,d+4),10),g=n.slice(d,d+4).length===4,r=this.getFixedDate(m,u,h)),n.length===4&&(e[0]==="y"||e[1]==="y")&&(c=e[0]==="m"?0:2,d=2-c,u=parseInt(n.slice(c,c+2),10),h=parseInt(n.slice(d,d+2),10),g=n.slice(d,d+2).length===2,r=[0,u,h]),n.length===6&&(e[0]==="Y"||e[1]==="Y")&&(c=e[0]==="m"?0:4,d=2-.5*c,u=parseInt(n.slice(c,c+2),10),h=parseInt(n.slice(d,d+4),10),g=n.slice(d,d+4).length===4,r=[0,u,h]),r=t.getRangeFixedDate(r),t.date=r;var x=r.length===0?n:e.reduce(function(w,D){switch(D){case"d":return w+(r[0]===0?"":t.addLeadingZero(r[0]));case"m":return w+(r[1]===0?"":t.addLeadingZero(r[1]));case"y":return w+(g?t.addLeadingZeroForYear(r[2],!1):"");case"Y":return w+(g?t.addLeadingZeroForYear(r[2],!0):"")}},"");return x},getRangeFixedDate:function(n){var t=this,e=t.datePattern,r=t.dateMin||[],i=t.dateMax||[];return!n.length||r.length<3&&i.length<3||e.find(function(a){return a.toLowerCase()==="y"})&&n[2]===0?n:i.length&&(i[2]<n[2]||i[2]===n[2]&&(i[1]<n[1]||i[1]===n[1]&&i[0]<n[0]))?i:r.length&&(r[2]>n[2]||r[2]===n[2]&&(r[1]>n[1]||r[1]===n[1]&&r[0]>n[0]))?r:n},getFixedDate:function(n,t,e){return n=Math.min(n,31),t=Math.min(t,12),e=parseInt(e||0,10),(t<7&&t%2===0||t>8&&t%2===1)&&(n=Math.min(n,t===2?this.isLeapYear(e)?29:28:30)),[n,t,e]},isLeapYear:function(n){return n%4===0&&n%100!==0||n%400===0},addLeadingZero:function(n){return(n<10?"0":"")+n},addLeadingZeroForYear:function(n,t){return t?(n<10?"000":n<100?"00":n<1e3?"0":"")+n:(n<10?"0":"")+n}},f.exports=s},function(f,p){var s=function(n,t){var e=this;e.time=[],e.blocks=[],e.timePattern=n,e.timeFormat=t,e.initBlocks()};s.prototype={initBlocks:function(){var n=this;n.timePattern.forEach(function(){n.blocks.push(2)})},getISOFormatTime:function(){var n=this,t=n.time;return t[2]?n.addLeadingZero(t[0])+":"+n.addLeadingZero(t[1])+":"+n.addLeadingZero(t[2]):""},getBlocks:function(){return this.blocks},getTimeFormatOptions:function(){var n=this;return String(n.timeFormat)==="12"?{maxHourFirstDigit:1,maxHours:12,maxMinutesFirstDigit:5,maxMinutes:60}:{maxHourFirstDigit:2,maxHours:23,maxMinutesFirstDigit:5,maxMinutes:60}},getValidatedTime:function(n){var t=this,e="";n=n.replace(/[^\d]/g,"");var r=t.getTimeFormatOptions();return t.blocks.forEach(function(i,a){if(n.length>0){var o=n.slice(0,i),l=o.slice(0,1),c=n.slice(i);switch(t.timePattern[a]){case"h":parseInt(l,10)>r.maxHourFirstDigit?o="0"+l:parseInt(o,10)>r.maxHours&&(o=r.maxHours+"");break;case"m":case"s":parseInt(l,10)>r.maxMinutesFirstDigit?o="0"+l:parseInt(o,10)>r.maxMinutes&&(o=r.maxMinutes+"");break}e+=o,n=c}}),this.getFixedTimeString(e)},getFixedTimeString:function(n){var t=this,e=t.timePattern,r=[],i=0,a=0,o=0,l=0,c=0,d=0,m,u,h;return n.length===6&&(e.forEach(function(g,x){switch(g){case"s":i=x*2;break;case"m":a=x*2;break;case"h":o=x*2;break}}),d=o,c=a,l=i,m=parseInt(n.slice(l,l+2),10),u=parseInt(n.slice(c,c+2),10),h=parseInt(n.slice(d,d+2),10),r=this.getFixedTime(h,u,m)),n.length===4&&t.timePattern.indexOf("s")<0&&(e.forEach(function(g,x){switch(g){case"m":a=x*2;break;case"h":o=x*2;break}}),d=o,c=a,m=0,u=parseInt(n.slice(c,c+2),10),h=parseInt(n.slice(d,d+2),10),r=this.getFixedTime(h,u,m)),t.time=r,r.length===0?n:e.reduce(function(g,x){switch(x){case"s":return g+t.addLeadingZero(r[2]);case"m":return g+t.addLeadingZero(r[1]);case"h":return g+t.addLeadingZero(r[0])}},"")},getFixedTime:function(n,t,e){return e=Math.min(parseInt(e||0,10),60),t=Math.min(t,60),n=Math.min(n,60),[n,t,e]},addLeadingZero:function(n){return(n<10?"0":"")+n}},f.exports=s},function(f,p){var s=function(n,t){var e=this;e.delimiter=t||t===""?t:" ",e.delimiterRE=t?new RegExp("\\"+t,"g"):"",e.formatter=n};s.prototype={setFormatter:function(n){this.formatter=n},format:function(n){var t=this;t.formatter.clear(),n=n.replace(/[^\d+]/g,""),n=n.replace(/^\+/,"B").replace(/\+/g,"").replace("B","+"),n=n.replace(t.delimiterRE,"");for(var e="",r,i=!1,a=0,o=n.length;a<o;a++)r=t.formatter.inputDigit(n.charAt(a)),/[\s()-]/g.test(r)?(e=r,i=!0):i||(e=r);return e=e.replace(/[()]/g,""),e=e.replace(/[\s-]/g,t.delimiter),e}},f.exports=s},function(f,p){var s={blocks:{uatp:[4,5,6],amex:[4,6,5],diners:[4,6,4],discover:[4,4,4,4],mastercard:[4,4,4,4],dankort:[4,4,4,4],instapayment:[4,4,4,4],jcb15:[4,6,5],jcb:[4,4,4,4],maestro:[4,4,4,4],visa:[4,4,4,4],mir:[4,4,4,4],unionPay:[4,4,4,4],general:[4,4,4,4]},re:{uatp:/^(?!1800)1\d{0,14}/,amex:/^3[47]\d{0,13}/,discover:/^(?:6011|65\d{0,2}|64[4-9]\d?)\d{0,12}/,diners:/^3(?:0([0-5]|9)|[689]\d?)\d{0,11}/,mastercard:/^(5[1-5]\d{0,2}|22[2-9]\d{0,1}|2[3-7]\d{0,2})\d{0,12}/,dankort:/^(5019|4175|4571)\d{0,12}/,instapayment:/^63[7-9]\d{0,13}/,jcb15:/^(?:2131|1800)\d{0,11}/,jcb:/^(?:35\d{0,2})\d{0,12}/,maestro:/^(?:5[0678]\d{0,2}|6304|67\d{0,2})\d{0,12}/,mir:/^220[0-4]\d{0,12}/,visa:/^4\d{0,15}/,unionPay:/^(62|81)\d{0,14}/},getStrictBlocks:function(n){var t=n.reduce(function(e,r){return e+r},0);return n.concat(19-t)},getInfo:function(n,t){var e=s.blocks,r=s.re;t=!!t;for(var i in r)if(r[i].test(n)){var a=e[i];return{type:i,blocks:t?this.getStrictBlocks(a):a}}return{type:"unknown",blocks:t?this.getStrictBlocks(e.general):e.general}}};f.exports=s},function(f,p){var s={noop:function(){},strip:function(n,t){return n.replace(t,"")},getPostDelimiter:function(n,t,e){if(e.length===0)return n.slice(-t.length)===t?t:"";var r="";return e.forEach(function(i){n.slice(-i.length)===i&&(r=i)}),r},getDelimiterREByDelimiter:function(n){return new RegExp(n.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),"g")},getNextCursorPosition:function(n,t,e,r,i){return t.length===n?e.length:n+this.getPositionOffset(n,t,e,r,i)},getPositionOffset:function(n,t,e,r,i){var a,o,l;return a=this.stripDelimiters(t.slice(0,n),r,i),o=this.stripDelimiters(e.slice(0,n),r,i),l=a.length-o.length,l!==0?l/Math.abs(l):0},stripDelimiters:function(n,t,e){var r=this;if(e.length===0){var i=t?r.getDelimiterREByDelimiter(t):"";return n.replace(i,"")}return e.forEach(function(a){a.split("").forEach(function(o){n=n.replace(r.getDelimiterREByDelimiter(o),"")})}),n},headStr:function(n,t){return n.slice(0,t)},getMaxLength:function(n){return n.reduce(function(t,e){return t+e},0)},getPrefixStrippedValue:function(n,t,e,r,i,a,o,l,c){if(e===0)return n;if(n===t&&n!=="")return"";if(c&&n.slice(0,1)=="-"){var d=r.slice(0,1)=="-"?r.slice(1):r;return"-"+this.getPrefixStrippedValue(n.slice(1),t,e,d,i,a,o,l,c)}if(r.slice(0,e)!==t&&!l)return o&&!r&&n?n:"";if(r.slice(-e)!==t&&l)return o&&!r&&n?n:"";var m=this.stripDelimiters(r,i,a);return n.slice(0,e)!==t&&!l?m.slice(e):n.slice(-e)!==t&&l?m.slice(0,-e-1):l?n.slice(0,-e):n.slice(e)},getFirstDiffIndex:function(n,t){for(var e=0;n.charAt(e)===t.charAt(e);)if(n.charAt(e++)==="")return-1;return e},getFormattedValue:function(n,t,e,r,i,a){var o="",l=i.length>0,c="";return e===0?n:(t.forEach(function(d,m){if(n.length>0){var u=n.slice(0,d),h=n.slice(d);l?c=i[a?m-1:m]||c:c=r,a?(m>0&&(o+=c),o+=u):(o+=u,u.length===d&&m<e-1&&(o+=c)),n=h}}),o)},fixPrefixCursor:function(n,t,e,r){if(n){var i=n.value,a=e||r[0]||" ";if(!(!n.setSelectionRange||!t||t.length+a.length<=i.length)){var o=i.length*2;setTimeout(function(){n.setSelectionRange(o,o)},1)}}},checkFullSelection:function(n){try{var t=window.getSelection()||document.getSelection()||{};return t.toString().length===n.length}catch{}return!1},setSelection:function(n,t,e){if(n===this.getActiveElement(e)&&!(n&&n.value.length<=t))if(n.createTextRange){var r=n.createTextRange();r.move("character",t),r.select()}else try{n.setSelectionRange(t,t)}catch{console.warn("The input element type does not support selection")}},getActiveElement:function(n){var t=n.activeElement;return t&&t.shadowRoot?this.getActiveElement(t.shadowRoot):t},isAndroid:function(){return navigator&&/android/i.test(navigator.userAgent)},isAndroidBackspaceKeydown:function(n,t){return!this.isAndroid()||!n||!t?!1:t===n.slice(0,-1)}};f.exports=s},function(f,p){(function(s){var n={assign:function(t,e){return t=t||{},e=e||{},t.creditCard=!!e.creditCard,t.creditCardStrictMode=!!e.creditCardStrictMode,t.creditCardType="",t.onCreditCardTypeChanged=e.onCreditCardTypeChanged||function(){},t.phone=!!e.phone,t.phoneRegionCode=e.phoneRegionCode||"AU",t.phoneFormatter={},t.time=!!e.time,t.timePattern=e.timePattern||["h","m","s"],t.timeFormat=e.timeFormat||"24",t.timeFormatter={},t.date=!!e.date,t.datePattern=e.datePattern||["d","m","Y"],t.dateMin=e.dateMin||"",t.dateMax=e.dateMax||"",t.dateFormatter={},t.numeral=!!e.numeral,t.numeralIntegerScale=e.numeralIntegerScale>0?e.numeralIntegerScale:0,t.numeralDecimalScale=e.numeralDecimalScale>=0?e.numeralDecimalScale:2,t.numeralDecimalMark=e.numeralDecimalMark||".",t.numeralThousandsGroupStyle=e.numeralThousandsGroupStyle||"thousand",t.numeralPositiveOnly=!!e.numeralPositiveOnly,t.stripLeadingZeroes=e.stripLeadingZeroes!==!1,t.signBeforePrefix=!!e.signBeforePrefix,t.tailPrefix=!!e.tailPrefix,t.swapHiddenInput=!!e.swapHiddenInput,t.numericOnly=t.creditCard||t.date||!!e.numericOnly,t.uppercase=!!e.uppercase,t.lowercase=!!e.lowercase,t.prefix=t.creditCard||t.date?"":e.prefix||"",t.noImmediatePrefix=!!e.noImmediatePrefix,t.prefixLength=t.prefix.length,t.rawValueTrimPrefix=!!e.rawValueTrimPrefix,t.copyDelimiter=!!e.copyDelimiter,t.initValue=e.initValue!==void 0&&e.initValue!==null?e.initValue.toString():"",t.delimiter=e.delimiter||e.delimiter===""?e.delimiter:e.date?"/":e.time?":":e.numeral?",":(e.phone," "),t.delimiterLength=t.delimiter.length,t.delimiterLazyShow=!!e.delimiterLazyShow,t.delimiters=e.delimiters||[],t.blocks=e.blocks||[],t.blocksLength=t.blocks.length,t.root=typeof s=="object"&&s?s:window,t.document=e.document||t.root.document,t.maxLength=0,t.backspace=!1,t.result="",t.onValueChanged=e.onValueChanged||function(){},t}};f.exports=n}).call(p,function(){return this}())}])})}(k)),k.exports}C();
