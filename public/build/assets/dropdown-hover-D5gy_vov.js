(function(t){if(!t||!t.fn)return;const d="[data-bs-toggle=dropdown][data-trigger=hover]",i=150;function s(o){let e=o.data("dd-timeout");e&&(clearTimeout(e),e=null,o.data("dd-timeout",e)),o.attr("aria-expanded")!=="true"&&o.dropdown("toggle")}function r(o){let e=o.data("dd-timeout");e&&clearTimeout(e),e=setTimeout(()=>{let n=o.data("dd-timeout");n&&(clearTimeout(n),n=null,o.data("dd-timeout",n)),o.attr("aria-expanded")==="true"&&o.dropdown("toggle")},i),o.data("dd-timeout",e)}t(function(){t("body").on("mouseenter",`${d}, ${d} ~ .dropdown-menu`,function(){t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle");const o=t(this).hasClass("dropdown-menu")?t(this):t(this).next(".dropdown-menu");window.getComputedStyle(o[0],null).getPropertyValue("position")!=="static"&&(t(this).is(d)&&t(this).data("hovered",!0),s(t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle")))}).on("mouseleave",`${d}, ${d} ~ .dropdown-menu`,function(){t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle");const o=t(this).hasClass("dropdown-menu")?t(this):t(this).next(".dropdown-menu");window.getComputedStyle(o[0],null).getPropertyValue("position")!=="static"&&(t(this).is(d)&&t(this).data("hovered",!1),r(t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle")))}).on("hide.bs.dropdown",function(o){t(this).find(d).data("hovered")&&o.preventDefault()})})})(window.jQuery);
