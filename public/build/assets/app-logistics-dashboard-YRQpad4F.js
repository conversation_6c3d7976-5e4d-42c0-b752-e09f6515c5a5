(function(){let l,r,s,t;isDarkStyle?(l=config.colors_dark.borderColor,r=config.colors_dark.textMuted,s=config.colors_dark.headingColor,t=config.colors_dark.bodyColor):(l=config.colors.borderColor,r=config.colors.textMuted,s=config.colors.headingColor,t=config.colors.bodyColor);const a={donut:{series1:config.colors.success,series2:"#53D28C",series3:"#7EDDA9",series4:"#A9E9C5"},line:{series1:config.colors.warning,series2:config.colors.primary,series3:"#7367f029"}},e=document.querySelector("#shipmentStatisticsChart"),o={series:[{name:"Shipment",type:"column",data:[38,45,33,38,32,50,48,40,42,37]},{name:"Delivery",type:"line",data:[23,28,23,32,28,44,32,38,26,34]}],chart:{height:320,type:"line",stacked:!1,parentHeightOffset:0,toolbar:{show:!1},zoom:{enabled:!1}},markers:{size:5,colors:[config.colors.white],strokeColors:a.line.series2,hover:{size:6},borderRadius:4},stroke:{curve:"smooth",width:[0,3],lineCap:"round"},legend:{show:!0,position:"bottom",markers:{width:8,height:8,offsetX:-3},height:40,itemMargin:{horizontal:10,vertical:0},fontSize:"15px",fontFamily:"Public Sans",fontWeight:400,labels:{colors:s,useSeriesColors:!1},offsetY:10},grid:{strokeDashArray:8,borderColor:l},colors:[a.line.series1,a.line.series2],fill:{opacity:[1,1]},plotOptions:{bar:{columnWidth:"30%",startingShape:"rounded",endingShape:"rounded",borderRadius:4}},dataLabels:{enabled:!1},xaxis:{tickAmount:10,categories:["1 Jan","2 Jan","3 Jan","4 Jan","5 Jan","6 Jan","7 Jan","8 Jan","9 Jan","10 Jan"],labels:{style:{colors:r,fontSize:"13px",fontWeight:400}},axisBorder:{show:!1},axisTicks:{show:!1}},yaxis:{tickAmount:4,min:0,max:50,labels:{style:{colors:r,fontSize:"13px",fontFamily:"Public Sans",fontWeight:400},formatter:function(i){return i+"%"}}},responsive:[{breakpoint:1400,options:{chart:{height:320},xaxis:{labels:{style:{fontSize:"10px"}}},legend:{itemMargin:{vertical:0,horizontal:10},fontSize:"13px",offsetY:12}}},{breakpoint:1025,options:{chart:{height:415},plotOptions:{bar:{columnWidth:"50%"}}}},{breakpoint:982,options:{plotOptions:{bar:{columnWidth:"30%"}}}},{breakpoint:480,options:{chart:{height:250},legend:{offsetY:7}}}]};typeof e!==void 0&&e!==null&&new ApexCharts(e,o).render();const n=document.querySelector("#deliveryExceptionsChart"),d={chart:{height:420,parentHeightOffset:0,type:"donut"},labels:["Incorrect address","Weather conditions","Federal Holidays","Damage during transit"],series:[13,25,22,40],colors:[a.donut.series1,a.donut.series2,a.donut.series3,a.donut.series4],stroke:{width:0},dataLabels:{enabled:!1,formatter:function(i,c){return parseInt(i)+"%"}},legend:{show:!0,position:"bottom",offsetY:10,markers:{width:8,height:8,offsetX:-3},itemMargin:{horizontal:15,vertical:5},fontSize:"13px",fontFamily:"Public Sans",fontWeight:400,labels:{colors:s,useSeriesColors:!1}},tooltip:{theme:!1},grid:{padding:{top:15}},plotOptions:{pie:{donut:{size:"77%",labels:{show:!0,value:{fontSize:"24px",fontFamily:"Public Sans",color:s,fontWeight:500,offsetY:-20,formatter:function(i){return parseInt(i)+"%"}},name:{offsetY:30,fontFamily:"Public Sans"},total:{show:!0,fontSize:"15px",fontFamily:"Public Sans",color:t,label:"AVG. Exceptions",formatter:function(i){return"30%"}}}}}},responsive:[{breakpoint:420,options:{chart:{height:360}}}]};typeof n!==void 0&&n!==null&&new ApexCharts(n,d).render()})();$(function(){var l=$(".dt-route-vehicles");l.length&&(l.DataTable({ajax:assetsPath+"json/logistics-dashboard.json",columns:[{data:"id"},{data:"id"},{data:"location"},{data:"start_city"},{data:"end_city"},{data:"warnings"},{data:"progress"}],columnDefs:[{className:"control",orderable:!1,searchable:!1,responsivePriority:2,targets:0,render:function(r,s,t,a){return""}},{targets:1,orderable:!1,searchable:!1,checkboxes:!0,responsivePriority:3,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,responsivePriority:1,render:function(r,s,t,a){var e=t.location,o='<div class="d-flex justify-content-start align-items-center user-name"><div class="avatar-wrapper"><div class="avatar me-4"><span class="avatar-initial rounded-circle bg-label-secondary"><i class="ti ti-car ti-28px"></i></span></div></div><div class="d-flex flex-column"><a class="text-heading fw-medium" href="'+baseUrl+'app/logistics/fleet">VOL-'+e+"</a></div></div>";return o}},{targets:3,render:function(r,s,t,a){var e=t.start_city,o=t.start_country,n='<div class="text-body">'+e+", "+o+"</div >";return n}},{targets:4,render:function(r,s,t,a){var e=t.end_city,o=t.end_country,n='<div class="text-body">'+e+", "+o+"</div >";return n}},{targets:-2,render:function(r,s,t,a){var e=t.warnings,o={1:{title:"No Warnings",class:"bg-label-success"},2:{title:"Temperature Not Optimal",class:"bg-label-warning"},3:{title:"Ecu Not Responding",class:"bg-label-danger"},4:{title:"Oil Leakage",class:"bg-label-info"},5:{title:"fuel problems",class:"bg-label-primary"}};return typeof o[e]>"u"?r:'<span class="badge rounded '+o[e].class+'">'+o[e].title+"</span>"}},{targets:-1,render:function(r,s,t,a){var e=t.progress,o='<div class="d-flex align-items-center"><div div class="progress w-100" style="height: 8px;"><div class="progress-bar" role="progressbar" style="width:'+e+'%;" aria-valuenow="'+e+'" aria-valuemin="0" aria-valuemax="100"></div></div><div class="text-body ms-3">'+e+"%</div></div>";return o}}],order:[2,"asc"],dom:'<"table-responsive"t><"row d-flex align-items-center"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:5,language:{paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(r){var s=r.data();return"Details of "+s.location}}),type:"column",renderer:function(r,s,t){var a=$.map(t,function(e,o){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return a?$('<table class="table"/><tbody />').append(a):!1}}}}),$(".dataTables_info").addClass("pt-0"))});
