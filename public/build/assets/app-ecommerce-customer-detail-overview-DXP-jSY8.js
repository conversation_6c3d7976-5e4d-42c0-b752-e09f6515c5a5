$(function(){var d=$(".datatables-customer-order"),o=baseUrl+"app/ecommerce/order/details",n={1:{title:"Ready to  Pickup",class:"bg-label-info"},2:{title:"Dispatched",class:"bg-label-warning"},3:{title:"Delivered",class:"bg-label-success"},4:{title:"Out for delivery",class:"bg-label-primary"}};if(d.length){var i=d.DataTable({ajax:assetsPath+"json/ecommerce-customer-order.json",columns:[{data:""},{data:"order"},{data:"date"},{data:"status"},{data:"spent"},{data:" "}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(a,r,t,s){return""}},{targets:1,responsivePriority:4,render:function(a,r,t,s){var e=t.order;return"<a href='"+o+"'><span>#"+e+"</span></a>"}},{targets:2,render:function(a,r,t,s){var e=new Date(t.date),l=e.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return'<span class="text-nowrap">'+l+"</span > "}},{targets:3,render:function(a,r,t,s){var e=t.status;return'<span class="badge '+n[e].class+'" text-capitalized>'+n[e].title+"</span>"}},{targets:4,render:function(a,r,t,s){var e=t.spent;return"<span >"+e+"</span>"}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(a,r,t,s){return'<div class="text-xxl-center"><button class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">View</a><a href="javascript:;" class="dropdown-item  delete-record">Delete</a></div></div>'}}],order:[[1,"desc"]],dom:'<"card-header flex-column flex-md-row py-0 mt-6 mt-md-0"<"head-label text-center pt-2 pt-md-0">f>t<"row mx-6"<"col-md-12 col-xxl-6 text-center text-xl-start pb-2 pb-xxl-0 pe-0"i><"col-md-12 col-xxl-6"p>>',lengthMenu:[6,30,50,70,100],language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search order",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var r=a.data();return"Details of "+r.order}}),type:"column",renderer:function(a,r,t){var s=$.map(t,function(e,l){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}}});$("div.head-label").html('<h5 class="card-title mb-0 text-nowrap">Orders placed</h5>')}$(".datatables-orders tbody").on("click",".delete-record",function(){i.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
