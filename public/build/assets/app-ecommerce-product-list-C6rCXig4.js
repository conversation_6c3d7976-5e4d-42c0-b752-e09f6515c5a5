$(function(){let l,d,u;isDarkStyle?(l=config.colors_dark.borderColor,d=config.colors_dark.bodyBg,u=config.colors_dark.headingColor):(l=config.colors.borderColor,d=config.colors.bodyBg,u=config.colors.headingColor);var m=$(".datatables-products"),g=baseUrl+"app/ecommerce/product/add",o={1:{title:"Scheduled",class:"bg-label-warning"},2:{title:"Publish",class:"bg-label-success"},3:{title:"Inactive",class:"bg-label-danger"}},p={0:{title:"Household"},1:{title:"Office"},2:{title:"Electronics"},3:{title:"Shoes"},4:{title:"Accessories"},5:{title:"Game"}},f={0:{title:"Out_of_Stock"},1:{title:"In_Stock"}},x={0:{title:"Out of Stock"},1:{title:"In Stock"}};if(m.length){var v=m.DataTable({ajax:assetsPath+"json/ecommerce-product-list.json",columns:[{data:"id"},{data:"id"},{data:"product_name"},{data:"category"},{data:"stock"},{data:"sku"},{data:"price"},{data:"quantity"},{data:"status"},{data:""}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(a,r,e,n){return""}},{targets:1,orderable:!1,checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'},render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input" >'},searchable:!1},{targets:2,responsivePriority:1,render:function(a,r,e,n){var t=e.product_name,i=e.id,s=e.product_brand,b=e.image;if(b)var h='<img src="'+assetsPath+"img/ecommerce-images/"+b+'" alt="Product-'+i+'" class="rounded-2">';else{var y=Math.floor(Math.random()*6),w=["success","danger","warning","info","dark","primary","secondary"],k=w[y],t=e.product_brand,c=t.match(/\b\w/g)||[];c=((c.shift()||"")+(c.pop()||"")).toUpperCase(),h='<span class="avatar-initial rounded-2 bg-label-'+k+'">'+c+"</span>"}var C='<div class="d-flex justify-content-start align-items-center product-name"><div class="avatar-wrapper"><div class="avatar avatar me-4 rounded-2 bg-label-secondary">'+h+'</div></div><div class="d-flex flex-column"><h6 class="text-nowrap mb-0">'+t+'</h6><small class="text-truncate d-none d-sm-block">'+s+"</small></div></div>";return C}},{targets:3,responsivePriority:5,render:function(a,r,e,n){var t=p[e.category].title,i={Household:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-warning me-4 p-3"><i class="ti ti-home-2 ti-sm"></i></span>',Office:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-info me-4 p-3"><i class="ti ti-briefcase ti-sm"></i></span>',Electronics:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-danger me-4 p-3"><i class="ti ti-device-mobile ti-sm"></i></span>',Shoes:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-success me-4"><i class="ti ti-shoe ti-sm"></i></span>',Accessories:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-secondary me-4"><i class="ti ti-device-watch ti-sm"></i></span>',Game:'<span class="avatar-sm rounded-circle d-flex justify-content-center align-items-center bg-label-primary me-4"><i class="ti ti-device-gamepad-2 ti-sm"></i></span>'};return"<span class='text-truncate d-flex align-items-center text-heading'>"+i[t]+t+"</span>"}},{targets:4,orderable:!1,responsivePriority:3,render:function(a,r,e,n){var t=e.stock,i={Out_of_Stock:'<label class="switch switch-primary switch-sm"><input type="checkbox" class="switch-input" id="switch"><span class="switch-toggle-slider"><span class="switch-off"></span></span></label>',In_Stock:'<label class="switch switch-primary switch-sm"><input type="checkbox" class="switch-input" checked=""><span class="switch-toggle-slider"><span class="switch-on"></span></span></label>'};return"<span class='text-truncate'>"+i[f[t].title]+'<span class="d-none">'+f[t].title+"</span></span>"}},{targets:5,render:function(a,r,e,n){var t=e.sku;return"<span>"+t+"</span>"}},{targets:6,render:function(a,r,e,n){var t=e.price;return"<span>"+t+"</span>"}},{targets:7,responsivePriority:4,render:function(a,r,e,n){var t=e.qty;return"<span>"+t+"</span>"}},{targets:-2,render:function(a,r,e,n){var t=e.status;return'<span class="badge '+o[t].class+'" text-capitalized>'+o[t].title+"</span>"}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(a,r,e,n){return'<div class="d-inline-block text-nowrap"><button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect waves-light"><i class="ti ti-edit ti-md"></i></button><button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect waves-light dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></button><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:0;" class="dropdown-item">View</a><a href="javascript:0;" class="dropdown-item">Suspend</a></div></div>'}}],order:[2,"asc"],dom:'<"card-header d-flex border-top rounded-0 flex-wrap py-0 flex-column flex-md-row align-items-start"<"me-5 ms-n4 pe-5 mb-n6 mb-md-0"f><"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex flex-column align-items-start align-items-sm-center justify-content-sm-center pt-0 gap-sm-4 gap-sm-0 flex-sm-row"lB>>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',lengthMenu:[7,10,20,50,70,100],language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search Product",info:"Displaying _START_ to _END_ of _TOTAL_ entries",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-secondary dropdown-toggle me-4 waves-effect waves-light",text:'<i class="ti ti-upload me-1 ti-xs"></i>Export',buttons:[{extend:"print",text:'<i class="ti ti-printer me-2" ></i>Print',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6,7],format:{body:function(a,r,e){if(a.length<=0)return a;var n=$.parseHTML(a),t="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("product-name")?t=t+s.lastChild.firstChild.textContent:s.innerText===void 0?t=t+s.textContent:t=t+s.innerText}),t}}},customize:function(a){$(a.document.body).css("color",u).css("border-color",l).css("background-color",d),$(a.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file me-2" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6,7],format:{body:function(a,r,e){if(a.length<=0)return a;var n=$.parseHTML(a),t="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("product-name")?t=t+s.lastChild.firstChild.textContent:s.innerText===void 0?t=t+s.textContent:t=t+s.innerText}),t}}}},{extend:"excel",text:'<i class="ti ti-file-export me-2"></i>Excel',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6,7],format:{body:function(a,r,e){if(a.length<=0)return a;var n=$.parseHTML(a),t="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("product-name")?t=t+s.lastChild.firstChild.textContent:s.innerText===void 0?t=t+s.textContent:t=t+s.innerText}),t}}}},{extend:"pdf",text:'<i class="ti ti-file-text me-2"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6,7],format:{body:function(a,r,e){if(a.length<=0)return a;var n=$.parseHTML(a),t="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("product-name")?t=t+s.lastChild.firstChild.textContent:s.innerText===void 0?t=t+s.textContent:t=t+s.innerText}),t}}}},{extend:"copy",text:'<i class="ti ti-copy me-2"></i>Copy',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6,7],format:{body:function(a,r,e){if(a.length<=0)return a;var n=$.parseHTML(a),t="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("product-name")?t=t+s.lastChild.firstChild.textContent:s.innerText===void 0?t=t+s.textContent:t=t+s.innerText}),t}}}}]},{text:'<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add Product</span>',className:"add-new btn btn-primary ms-2 ms-sm-0 waves-effect waves-light",action:function(){window.location.href=g}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var r=a.data();return"Details of "+r.product_name}}),type:"column",renderer:function(a,r,e){var n=$.map(e,function(t,i){return t.title!==""?'<tr data-dt-row="'+t.rowIndex+'" data-dt-column="'+t.columnIndex+'"><td>'+t.title+":</td> <td>"+t.data+"</td></tr>":""}).join("");return n?$('<table class="table"/><tbody />').append(n):!1}}},initComplete:function(){this.api().columns(-2).every(function(){var a=this,r=$('<select id="ProductStatus" class="form-select text-capitalize"><option value="">Status</option></select>').appendTo(".product_status").on("change",function(){var e=$.fn.dataTable.util.escapeRegex($(this).val());a.search(e?"^"+e+"$":"",!0,!1).draw()});a.data().unique().sort().each(function(e,n){r.append('<option value="'+o[e].title+'">'+o[e].title+"</option>")})}),this.api().columns(3).every(function(){var a=this,r=$('<select id="ProductCategory" class="form-select text-capitalize"><option value="">Category</option></select>').appendTo(".product_category").on("change",function(){var e=$.fn.dataTable.util.escapeRegex($(this).val());a.search(e?"^"+e+"$":"",!0,!1).draw()});a.data().unique().sort().each(function(e,n){r.append('<option value="'+p[e].title+'">'+p[e].title+"</option>")})}),this.api().columns(4).every(function(){var a=this,r=$('<select id="ProductStock" class="form-select text-capitalize"><option value=""> Stock </option></select>').appendTo(".product_stock").on("change",function(){var e=$.fn.dataTable.util.escapeRegex($(this).val());a.search(e?"^"+e+"$":"",!0,!1).draw()});a.data().unique().sort().each(function(e,n){r.append('<option value="'+f[e].title+'">'+x[e].title+"</option>")})})}});$(".dataTables_length").addClass("mx-n2"),$(".dt-buttons").addClass("d-flex flex-wrap mb-6 mb-sm-0")}$(".datatables-products tbody").on("click",".delete-record",function(){v.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
