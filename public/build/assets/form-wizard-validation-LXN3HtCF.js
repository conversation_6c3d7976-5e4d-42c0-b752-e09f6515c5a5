(function(){const r=$(".select2"),s=$(".selectpicker"),o=document.querySelector("#wizard-validation");if(typeof o!==void 0&&o!==null){const i=o.querySelector("#wizard-validation-form"),l=i.querySelector("#account-details-validation"),d=i.querySelector("#personal-info-validation"),m=i.querySelector("#social-links-validation"),c=[].slice.call(i.querySelectorAll(".btn-next")),u=[].slice.call(i.querySelectorAll(".btn-prev")),a=new Stepper(o,{linear:!0}),p=FormValidation.formValidation(l,{fields:{formValidationUsername:{validators:{notEmpty:{message:"The name is required"},stringLength:{min:6,max:30,message:"The name must be more than 6 and less than 30 characters long"},regexp:{regexp:/^[a-zA-Z0-9 ]+$/,message:"The name can only consist of alphabetical, number and space"}}},formValidationEmail:{validators:{notEmpty:{message:"The Email is required"},emailAddress:{message:"The value is not a valid email address"}}},formValidationPass:{validators:{notEmpty:{message:"The password is required"}}},formValidationConfirmPass:{validators:{notEmpty:{message:"The Confirm Password is required"},identical:{compare:function(){return l.querySelector('[name="formValidationPass"]').value},message:"The password and its confirm are not the same"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton},init:e=>{e.on("plugins.message.placed",function(t){t.element.parentElement.classList.contains("input-group")&&t.element.parentElement.insertAdjacentElement("afterend",t.messageElement)})}}).on("core.form.valid",function(){a.next()}),n=FormValidation.formValidation(d,{fields:{formValidationFirstName:{validators:{notEmpty:{message:"The first name is required"}}},formValidationLastName:{validators:{notEmpty:{message:"The last name is required"}}},formValidationCountry:{validators:{notEmpty:{message:"The Country is required"}}},formValidationLanguage:{validators:{notEmpty:{message:"The Languages is required"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){a.next()});s.length&&s.each(function(){var e=$(this);e.selectpicker().on("change",function(){n.revalidateField("formValidationLanguage")})}),r.length&&r.each(function(){var e=$(this);e.wrap('<div class="position-relative"></div>'),e.select2({placeholder:"Select an country",dropdownParent:e.parent()}).on("change",function(){n.revalidateField("formValidationCountry")})});const g=FormValidation.formValidation(m,{fields:{formValidationTwitter:{validators:{notEmpty:{message:"The Twitter URL is required"},uri:{message:"The URL is not proper"}}},formValidationFacebook:{validators:{notEmpty:{message:"The Facebook URL is required"},uri:{message:"The URL is not proper"}}},formValidationGoogle:{validators:{notEmpty:{message:"The Google URL is required"},uri:{message:"The URL is not proper"}}},formValidationLinkedIn:{validators:{notEmpty:{message:"The LinkedIn URL is required"},uri:{message:"The URL is not proper"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){alert("Submitted..!!")});c.forEach(e=>{e.addEventListener("click",t=>{switch(a._currentIndex){case 0:p.validate();break;case 1:n.validate();break;case 2:g.validate();break}})}),u.forEach(e=>{e.addEventListener("click",t=>{switch(a._currentIndex){case 2:a.previous();break;case 1:a.previous();break}})})}})();
