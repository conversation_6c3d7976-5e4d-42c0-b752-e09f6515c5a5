$(function(){let i,d,l;isDarkStyle?(i=config.colors_dark.borderColor,d=config.colors_dark.bodyBg,l=config.colors_dark.headingColor):(i=config.colors.borderColor,d=config.colors.bodyBg,l=config.colors.headingColor);var u=$(".datatables-customers"),f=$(".select2"),x=baseUrl+"app/ecommerce/customer/details/overview";if(f.length){var p=f;p.wrap('<div class="position-relative"></div>').select2({placeholder:"United States ",dropdownParent:p.parent()})}if(u.length){var v=u.DataTable({ajax:assetsPath+"json/ecommerce-customer-all.json",columns:[{data:""},{data:"id"},{data:"customer"},{data:"customer_id"},{data:"country"},{data:"order"},{data:"total_spent"}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(a,n,s,r){return""}},{targets:1,orderable:!1,searchable:!1,responsivePriority:3,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,responsivePriority:1,render:function(a,n,s,r){var e=s.customer,o=s.email,t=s.image;if(t)var c='<img src="'+assetsPath+"img/avatars/"+t+'" alt="Avatar" class="rounded-circle">';else{var g=Math.floor(Math.random()*6),h=["success","danger","warning","info","dark","primary","secondary"],b=h[g],e=s.customer,m=e.match(/\b\w/g)||[];m=((m.shift()||"")+(m.pop()||"")).toUpperCase(),c='<span class="avatar-initial rounded-circle bg-label-'+b+'">'+m+"</span>"}var y='<div class="d-flex justify-content-start align-items-center customer-name"><div class="avatar-wrapper"><div class="avatar avatar-sm me-3">'+c+'</div></div><div class="d-flex flex-column"><a href="'+x+'" class="text-heading" ><span class="fw-medium">'+e+"</span></a><small>"+o+"</small></div></div>";return y}},{targets:3,render:function(a,n,s,r){var e=s.customer_id;return"<span class='text-heading'>#"+e+"</span>"}},{targets:4,render:function(a,n,s,r){var e=s.country,o=s.country_code;if(o)var t=`<i class ="fis fi fi-${o} rounded-circle me-2 fs-4"></i>`;else var t='<i class ="fis fi fi-xx rounded-circle me-2 fs-4"></i>';var c='<div class="d-flex justify-content-start align-items-center customer-country"><div>'+t+"</div><div><span>"+e+"</span></div></div>";return c}},{targets:5,render:function(a,n,s,r){var e=s.order;return"<span>"+e+"</span>"}},{targets:6,render:function(a,n,s,r){var e=s.total_spent;return'<span class="fw-medium text-heading">'+e+"</span>"}}],order:[[2,"desc"]],dom:'<"card-header d-flex flex-wrap flex-md-row flex-column align-items-start align-items-sm-center py-0"<"d-flex align-items-center me-5"f><"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-md-end flex-wrap flex-sm-nowrap mb-6 mb-sm-0"lB>>t<"row mx-1"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search Order",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-secondary dropdown-toggle me-4 waves-effect waves-light",text:'<i class="ti ti-upload ti-xs me-2"></i>Export',buttons:[{extend:"print",text:'<i class="ti ti-printer me-2" ></i>Print',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("customer-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}},customize:function(a){$(a.document.body).css("color",l).css("border-color",i).css("background-color",d),$(a.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file me-2" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("customer-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"excel",text:'<i class="ti ti-file-export me-2"></i>Excel',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("customer-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"pdf",text:'<i class="ti ti-file-text me-2"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("customer-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"copy",text:'<i class="ti ti-copy me-2" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5,6],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("customer-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}}]},{text:'<i class="ti ti-plus me-0 me-sm-1 mb-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add Customer</span>',className:"add-new btn btn-primary waves-effect waves-light",attr:{"data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasEcommerceCustomerAdd"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var n=a.data();return"Details of "+n.customer}}),type:"column",renderer:function(a,n,s){var r=$.map(s,function(e,o){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return r?$('<table class="table"/><tbody />').append(r):!1}}}});$(".dataTables_length").addClass("ms-n2 me-2"),$(".dt-action-buttons").addClass("pt-0"),$(".dataTables_filter").addClass("ms-n3 mb-0 mb-md-6"),$(".dt-buttons").addClass("d-flex flex-wrap")}$(".datatables-customers tbody").on("click",".delete-record",function(){v.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});(function(){const i=document.querySelectorAll(".phone-mask"),d=document.getElementById("eCommerceCustomerAddForm");i&&i.forEach(function(l){new Cleave(l,{phone:!0,phoneRegionCode:"US"})}),FormValidation.formValidation(d,{fields:{customerName:{validators:{notEmpty:{message:"Please enter fullname "}}},customerEmail:{validators:{notEmpty:{message:"Please enter your email"},emailAddress:{message:"The value is not a valid email address"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(l,u){return".mb-6"}}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}})})();
