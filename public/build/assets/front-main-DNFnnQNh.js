window.isRtl=window.Helpers.isRtl();window.isDarkStyle=window.Helpers.isDarkStyle();(function(){var m,u;const n=document.getElementById("navbarSupportedContent"),o=document.querySelector(".layout-navbar"),w=document.querySelectorAll(".navbar-nav .nav-link");setTimeout(function(){window.Helpers.initCustomOptionCheck()},1e3),typeof Waves<"u"&&(Waves.init(),Waves.attach(".btn[class*='btn-']:not([class*='btn-outline-']):not([class*='btn-label-'])",["waves-light"]),Waves.attach("[class*='btn-outline-']"),Waves.attach("[class*='btn-label-']"),Waves.attach(".pagination .page-item .page-link")),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(t){return new bootstrap.Tooltip(t)});const c=function(t){t.type=="show.bs.collapse"||t.type=="show.bs.collapse"?t.target.closest(".accordion-item").classList.add("active"):t.target.closest(".accordion-item").classList.remove("active")};[].slice.call(document.querySelectorAll(".accordion")).map(function(t){t.addEventListener("show.bs.collapse",c),t.addEventListener("hide.bs.collapse",c)}),isRtl&&Helpers._addClass("dropdown-menu-end",document.querySelectorAll("#layout-navbar .dropdown-menu")),window.addEventListener("scroll",t=>{window.scrollY>10?o.classList.add("navbar-active"):o.classList.remove("navbar-active")}),window.addEventListener("load",t=>{window.scrollY>10?o.classList.add("navbar-active"):o.classList.remove("navbar-active")});function r(){n.classList.remove("show")}document.addEventListener("click",function(t){n.contains(t.target)||r()}),w.forEach(t=>{t.addEventListener("click",e=>{t.classList.contains("dropdown-toggle")?e.preventDefault():r()})}),isRtl&&Helpers._addClass("dropdown-menu-end",document.querySelectorAll(".dropdown-menu"));const d=document.querySelectorAll(".nav-link.mega-dropdown");d&&d.forEach(t=>{new MegaDropdown(t)});let i=document.querySelector(".dropdown-style-switcher");const p=document.documentElement.getAttribute("data-style");let l=localStorage.getItem("templateCustomizer-"+templateName+"--Style")||(((u=(m=window.templateCustomizer)==null?void 0:m.settings)==null?void 0:u.defaultStyle)??"light");//!if there is no Customizer then use default style as light
if(window.templateCustomizer&&i){[].slice.call(i.children[1].querySelectorAll(".dropdown-item")).forEach(function(s){s.classList.remove("active"),s.addEventListener("click",function(){let a=this.getAttribute("data-theme");a==="light"?window.templateCustomizer.setStyle("light"):a==="dark"?window.templateCustomizer.setStyle("dark"):window.templateCustomizer.setStyle("system")}),setTimeout(()=>{s.getAttribute("data-theme")===p&&s.classList.add("active")},1e3)});const e=i.querySelector("i");l==="light"?(e.classList.add("ti-sun"),new bootstrap.Tooltip(e,{title:"Light Mode",fallbackPlacements:["bottom"]})):l==="dark"?(e.classList.add("ti-moon-stars"),new bootstrap.Tooltip(e,{title:"Dark Mode",fallbackPlacements:["bottom"]})):(e.classList.add("ti-device-desktop-analytics"),new bootstrap.Tooltip(e,{title:"System Mode",fallbackPlacements:["bottom"]}))}v(l);function v(t){t==="system"&&(window.matchMedia("(prefers-color-scheme: dark)").matches?t="dark":t="light"),[].slice.call(document.querySelectorAll("[data-app-"+t+"-img]")).map(function(s){const a=s.getAttribute("data-app-"+t+"-img");s.src=assetsPath+"img/"+a})}})();
