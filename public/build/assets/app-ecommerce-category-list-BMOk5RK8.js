const m=document.querySelector(".comment-editor");m&&new Quill(m,{modules:{toolbar:".comment-toolbar"},placeholder:"Write a Comment...",theme:"snow"});$(function(){isDarkStyle?(config.colors_dark.borderColor,config.colors_dark.bodyBg,config.colors_dark.headingColor):(config.colors.borderColor,config.colors.bodyBg,config.colors.headingColor);var o=$(".datatables-category-list"),l=$(".select2");l.length&&l.each(function(){var a=$(this);a.wrap('<div class="position-relative"></div>').select2({dropdownParent:a.parent(),placeholder:a.data("placeholder")})}),o.length&&(o.DataTable({ajax:assetsPath+"json/ecommerce-category-list.json",columns:[{data:""},{data:"id"},{data:"categories"},{data:"total_products"},{data:"total_earnings"},{data:""}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:1,targets:0,render:function(a,s,e,r){return""}},{targets:1,orderable:!1,searchable:!1,responsivePriority:4,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,responsivePriority:2,render:function(a,s,e,r){var t=e.categories,i=e.category_detail,d=e.cat_image,u=e.id;if(d)var c='<img src="'+assetsPath+"img/ecommerce-images/"+d+'" alt="Product-'+u+'" class="rounded-2">';else{var g=Math.floor(Math.random()*6),f=["success","danger","warning","info","dark","primary","secondary"],p=f[g],t=e.category_detail,n=t.match(/\b\w/g)||[];n=((n.shift()||"")+(n.pop()||"")).toUpperCase(),c='<span class="avatar-initial rounded-2 bg-label-'+p+'">'+n+"</span>"}var b='<div class="d-flex align-items-center"><div class="avatar-wrapper me-3 rounded-2 bg-label-secondary"><div class="avatar">'+c+'</div></div><div class="d-flex flex-column justify-content-center"><span class="text-heading text-wrap fw-medium">'+t+'</span><span class="text-truncate mb-0 d-none d-sm-block"><small>'+i+"</small></span></div></div>";return b}},{targets:3,responsivePriority:3,render:function(a,s,e,r){var t=e.total_products;return'<div class="text-sm-end">'+t+"</div>"}},{targets:4,orderable:!1,render:function(a,s,e,r){var t=e.total_earnings;return"<div class='mb-0 text-sm-end'>"+t+"</div"}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(a,s,e,r){return'<div class="d-flex align-items-sm-center justify-content-sm-center"><button class="btn btn-icon btn-text-secondary rounded-pill waves-effect waves-light"><i class="ti ti-edit"></i></button><button class="btn btn-icon btn-text-secondary rounded-pill waves-effect waves-light dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></button><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:0;" class="dropdown-item">View</a><a href="javascript:0;" class="dropdown-item">Suspend</a></div></div>'}}],order:[2,"desc"],dom:'<"card-header d-flex flex-wrap py-0 flex-column flex-sm-row"<f><"d-flex justify-content-center justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex justify-content-center flex-md-row align-items-baseline"lB>>>t<"row mx-1"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',lengthMenu:[7,10,20,50,70,100],language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search Category",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{text:'<i class="ti ti-plus ti-xs me-0 me-sm-2"></i><span class="d-none d-sm-inline-block">Add Category</span>',className:"add-new btn btn-primary ms-2 waves-effect waves-light",attr:{"data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasEcommerceCategoryList"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var s=a.data();return"Details of "+s.categories}}),type:"column",renderer:function(a,s,e){var r=$.map(e,function(t,i){return t.title!==""?'<tr data-dt-row="'+t.rowIndex+'" data-dt-column="'+t.columnIndex+'"><td> '+t.title+':</td> <td class="ps-0">'+t.data+"</td></tr>":""}).join("");return r?$('<table class="table"/><tbody />').append(r):!1}}}}),$(".dt-action-buttons").addClass("pt-0"),$(".dataTables_filter").addClass("me-3 mb-sm-6 mb-0 ps-0")),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_filter .form-control").addClass("ms-0"),$(".dataTables_length .form-select").removeClass("form-select-sm"),$(".dataTables_length .form-select").addClass("ms-0")},300)});(function(){const o=document.getElementById("eCommerceCategoryListForm");FormValidation.formValidation(o,{fields:{categoryTitle:{validators:{notEmpty:{message:"Please enter category title"}}},slug:{validators:{notEmpty:{message:"Please enter slug"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"is-valid",rowSelector:function(l,a){return".mb-6"}}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}})})();
