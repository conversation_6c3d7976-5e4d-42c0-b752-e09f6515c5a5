(function(){const o=document.querySelector("#basic-alert"),s=document.querySelector("#with-title"),a=document.querySelector("#footer-alert"),c=document.querySelector("#html-alert"),l=document.querySelector("#position-top-start"),r=document.querySelector("#position-top-end"),u=document.querySelector("#position-bottom-start"),f=document.querySelector("#position-bottom-end"),m=document.querySelector("#bounce-in-animation"),w=document.querySelector("#fade-in-animation"),b=document.querySelector("#flip-x-animation"),h=document.querySelector("#tada-animation"),d=document.querySelector("#shake-animation"),g=document.querySelector("#type-success"),S=document.querySelector("#type-info"),p=document.querySelector("#type-warning"),y=document.querySelector("#type-error"),v=document.querySelector("#type-question"),C=document.querySelector("#custom-image"),B=document.querySelector("#auto-close"),k=document.querySelector("#outside-click"),x=document.querySelector("#progress-steps"),q=document.querySelector("#ajax-request"),A=document.querySelector("#confirm-text"),T=document.querySelector("#confirm-color");o&&(o.onclick=function(){Swal.fire({title:"Any fool can use a computer",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),s&&(s.onclick=function(){Swal.fire({title:"The Internet?,",text:"That thing is still around?",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),a&&(a.onclick=function(){Swal.fire({icon:"error",title:"Oops...",text:"Something went wrong!",footer:"<a href>Why do I have this issue?</a>",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),c&&(c.onclick=function(){Swal.fire({title:'<span class="fw-medium">HTML <u>example</u></span>',icon:"info",html:'You can use <b>bold text</b>, <a href="https://pixinvent.com/" target="_blank">links</a> and other HTML tags',showCloseButton:!0,showCancelButton:!0,focusConfirm:!1,confirmButtonText:'<i class="ti ti-thumb-up"></i> Great!',confirmButtonAriaLabel:"Thumbs up, great!",cancelButtonText:'<i class="ti ti-thumb-down"></i>',cancelButtonAriaLabel:"Thumbs down",customClass:{confirmButton:"btn btn-primary me-3 waves-effect waves-light",cancelButton:"btn btn-label-secondary waves-effect waves-light"},buttonsStyling:!1})}),l&&(l.onclick=function(){Swal.fire({position:"top-start",icon:"success",title:"Your work has been saved",showConfirmButton:!1,timer:1500,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),r&&(r.onclick=function(){Swal.fire({position:"top-end",icon:"success",title:"Your work has been saved",showConfirmButton:!1,timer:1500,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),u&&(u.onclick=function(){Swal.fire({position:"bottom-start",icon:"success",title:"Your work has been saved",showConfirmButton:!1,timer:1500,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),f&&(f.onclick=function(){Swal.fire({position:"bottom-end",icon:"success",title:"Your work has been saved",showConfirmButton:!1,timer:1500,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),m&&(m.onclick=function(){Swal.fire({title:"Bounce In Animation",showClass:{popup:"animate__animated animate__bounceIn"},customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),w&&(w.onclick=function(){Swal.fire({title:"Fade In Animation",showClass:{popup:"animate__animated animate__fadeIn"},customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),b&&(b.onclick=function(){Swal.fire({title:"Flip In Animation",showClass:{popup:"animate__animated animate__flipInX"},customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),h&&(h.onclick=function(){Swal.fire({title:"Tada Animation",showClass:{popup:"animate__animated animate__tada"},customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),d&&(d.onclick=function(){Swal.fire({title:"Shake Animation",showClass:{popup:"animate__animated animate__shakeX"},customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),g&&(g.onclick=function(){Swal.fire({title:"Good job!",text:"You clicked the button!",icon:"success",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),S&&(S.onclick=function(){Swal.fire({title:"Info!",text:"You clicked the button!",icon:"info",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),p&&(p.onclick=function(){Swal.fire({title:"Warning!",text:" You clicked the button!",icon:"warning",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),y&&(y.onclick=function(){Swal.fire({title:"Error!",text:" You clicked the button!",icon:"error",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),v&&(v.onclick=function(){Swal.fire({title:"Question!",text:" You clicked the button!",icon:"question",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),C&&(C.onclick=function(){Swal.fire({title:"Sweet!",text:"Modal with a custom image.",imageUrl:assetsPath+"img/backgrounds/5.jpg",imageWidth:400,imageAlt:"Custom image",customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),B&&(B.onclick=function(){var t;Swal.fire({title:"Auto close alert!",html:"I will close in <strong></strong> seconds.",timer:2e3,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1,willOpen:function(){Swal.showLoading(),t=setInterval(function(){Swal.getHtmlContainer().querySelector("strong").textContent=Swal.getTimerLeft()},100)},willClose:function(){clearInterval(t)}}).then(function(e){e.dismiss===Swal.DismissReason.timer&&console.log("I was closed by the timer")})}),k&&(k.onclick=function(){Swal.fire({title:"Click outside to close!",text:"This is a cool message!",backdrop:!0,allowOutsideClick:!0,customClass:{confirmButton:"btn btn-primary waves-effect waves-light"},buttonsStyling:!1})}),x&&(x.onclick=function(){const t=["1","2","3"],e=Swal.mixin({confirmButtonText:"Forward",cancelButtonText:"Back",progressSteps:t,input:"text",inputAttributes:{required:!0},validationMessage:"This field is required"});async function I(){const _=[];let n;for(n=0;n<t.length;){const i=await new e({title:"Question "+t[n],showCancelButton:n>0,currentProgressStep:n});i.value?(_[n]=i.value,n++):i.dismiss==="cancel"&&n--}Swal.fire(JSON.stringify(_))}I()}),q&&(q.onclick=function(){Swal.fire({title:"Submit your Github username",input:"text",inputAttributes:{autocapitalize:"off"},showCancelButton:!0,confirmButtonText:"Look up",showLoaderOnConfirm:!0,customClass:{confirmButton:"btn btn-primary me-3 waves-effect waves-light",cancelButton:"btn btn-label-danger waves-effect waves-light"},preConfirm:t=>fetch("//api.github.com/users/"+t).then(e=>{if(!e.ok)throw new Error(e.statusText);return e.json()}).catch(e=>{Swal.showValidationMessage("Request failed:"+e)}),backdrop:!0,allowOutsideClick:()=>!Swal.isLoading()}).then(t=>{t.isConfirmed&&Swal.fire({title:t.value.login+"'s avatar",imageUrl:t.value.avatar_url,customClass:{confirmButtonText:"Close me!",confirmButton:"btn btn-primary waves-effect waves-light"}})})}),A&&(A.onclick=function(){Swal.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",customClass:{confirmButton:"btn btn-primary me-3 waves-effect waves-light",cancelButton:"btn btn-label-secondary waves-effect waves-light"},buttonsStyling:!1}).then(function(t){t.value&&Swal.fire({icon:"success",title:"Deleted!",text:"Your file has been deleted.",customClass:{confirmButton:"btn btn-success waves-effect waves-light"}})})}),T&&(T.onclick=function(){Swal.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",customClass:{confirmButton:"btn btn-primary me-3 waves-effect waves-light",cancelButton:"btn btn-label-secondary waves-effect waves-light"},buttonsStyling:!1}).then(function(t){t.value?Swal.fire({icon:"success",title:"Deleted!",text:"Your file has been deleted.",customClass:{confirmButton:"btn btn-success waves-effect waves-light"}}):t.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"Your imaginary file is safe :)",icon:"error",customClass:{confirmButton:"btn btn-success waves-effect waves-light"}})})})})();
