$(function(){let l,c,o;isDarkStyle?(l=config.colors_dark.borderColor,c=config.colors_dark.bodyBg,o=config.colors_dark.headingColor):(l=config.colors.borderColor,c=config.colors.bodyBg,o=config.colors.headingColor);var f=$(".datatables-users"),p=$(".select2"),m=baseUrl+"app/user/view/account",d={1:{title:"Pending",class:"bg-label-warning"},2:{title:"Active",class:"bg-label-success"},3:{title:"Inactive",class:"bg-label-secondary"}};if(p.length){var v=p;v.wrap('<div class="position-relative"></div>').select2({placeholder:"Select Country",dropdownParent:v.parent()})}if(f.length)var h=f.DataTable({ajax:assetsPath+"json/user-list.json",columns:[{data:"id"},{data:"id"},{data:"full_name"},{data:"role"},{data:"current_plan"},{data:"billing"},{data:"status"},{data:"action"}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(t,r,a,n){return""}},{targets:1,orderable:!1,checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'},render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input" >'},searchable:!1},{targets:2,responsivePriority:4,render:function(t,r,a,n){var e=a.full_name,i=a.email,s=a.avatar;if(s)var x='<img src="'+assetsPath+"img/avatars/"+s+'" alt="Avatar" class="rounded-circle">';else{var g=Math.floor(Math.random()*6),b=["success","danger","warning","info","primary","secondary"],w=b[g],e=a.full_name,u=e.match(/\b\w/g)||[];u=((u.shift()||"")+(u.pop()||"")).toUpperCase(),x='<span class="avatar-initial rounded-circle bg-label-'+w+'">'+u+"</span>"}var y='<div class="d-flex justify-content-start align-items-center user-name"><div class="avatar-wrapper"><div class="avatar avatar-sm me-4">'+x+'</div></div><div class="d-flex flex-column"><a href="'+m+'" class="text-heading text-truncate"><span class="fw-medium">'+e+"</span></a><small>"+i+"</small></div></div>";return y}},{targets:3,render:function(t,r,a,n){var e=a.role,i={Subscriber:'<i class="ti ti-crown ti-md text-primary me-2"></i>',Author:'<i class="ti ti-edit ti-md text-warning me-2"></i>',Maintainer:'<i class="ti ti-user ti-md text-success me-2"></i>',Editor:'<i class="ti ti-chart-pie ti-md text-info me-2"></i>',Admin:'<i class="ti ti-device-desktop ti-md text-danger me-2"></i>'};return"<span class='text-truncate d-flex align-items-center text-heading'>"+i[e]+e+"</span>"}},{targets:4,render:function(t,r,a,n){var e=a.current_plan;return'<span class="text-heading">'+e+"</span>"}},{targets:6,render:function(t,r,a,n){var e=a.status;return'<span class="badge '+d[e].class+'" text-capitalized>'+d[e].title+"</span>"}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(t,r,a,n){return'<div class="d-flex align-items-center"><a href="javascript:;" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill delete-record"><i class="ti ti-trash ti-md"></i></a><a href="'+m+'" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill"><i class="ti ti-eye ti-md"></i></a><a href="javascript:;" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;"" class="dropdown-item">Edit</a><a href="javascript:;" class="dropdown-item">Suspend</a></div></div>'}}],order:[[2,"desc"]],dom:'<"row"<"col-md-2"<"ms-n2"l>><"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search User",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-secondary dropdown-toggle mx-4 waves-effect waves-light",text:'<i class="ti ti-upload me-2 ti-xs"></i>Export',buttons:[{extend:"print",text:'<i class="ti ti-printer me-2" ></i>Print',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,r,a){if(t.length<=0)return t;var n=$.parseHTML(t),e="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}},customize:function(t){$(t.document.body).css("color",o).css("border-color",l).css("background-color",c),$(t.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file-text me-2" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,r,a){if(t.length<=0)return t;var n=$.parseHTML(t),e="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"excel",text:'<i class="ti ti-file-spreadsheet me-2"></i>Excel',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,r,a){if(t.length<=0)return t;var n=$.parseHTML(t),e="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"pdf",text:'<i class="ti ti-file-code-2 me-2"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,r,a){if(t.length<=0)return t;var n=$.parseHTML(t),e="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"copy",text:'<i class="ti ti-copy me-2" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,r,a){if(t.length<=0)return t;var n=$.parseHTML(t),e="";return $.each(n,function(i,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}}]},{text:'<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New User</span>',className:"add-new btn btn-primary waves-effect waves-light",attr:{"data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasAddUser"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var r=t.data();return"Details of "+r.full_name}}),type:"column",renderer:function(t,r,a){var n=$.map(a,function(e,i){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return n?$('<table class="table"/><tbody />').append(n):!1}}},initComplete:function(){this.api().columns(3).every(function(){var t=this,r=$('<select id="UserRole" class="form-select text-capitalize"><option value=""> Select Role </option></select>').appendTo(".user_role").on("change",function(){var a=$.fn.dataTable.util.escapeRegex($(this).val());t.search(a?"^"+a+"$":"",!0,!1).draw()});t.data().unique().sort().each(function(a,n){r.append('<option value="'+a+'">'+a+"</option>")})}),this.api().columns(4).every(function(){var t=this,r=$('<select id="UserPlan" class="form-select text-capitalize"><option value=""> Select Plan </option></select>').appendTo(".user_plan").on("change",function(){var a=$.fn.dataTable.util.escapeRegex($(this).val());t.search(a?"^"+a+"$":"",!0,!1).draw()});t.data().unique().sort().each(function(a,n){r.append('<option value="'+a+'">'+a+"</option>")})}),this.api().columns(6).every(function(){var t=this,r=$('<select id="FilterTransaction" class="form-select text-capitalize"><option value=""> Select Status </option></select>').appendTo(".user_status").on("change",function(){var a=$.fn.dataTable.util.escapeRegex($(this).val());t.search(a?"^"+a+"$":"",!0,!1).draw()});t.data().unique().sort().each(function(a,n){r.append('<option value="'+d[a].title+'" class="text-capitalize">'+d[a].title+"</option>")})})}});$(".datatables-users tbody").on("click",".delete-record",function(){h.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});(function(){const l=document.querySelectorAll(".phone-mask"),c=document.getElementById("addNewUserForm");l&&l.forEach(function(o){new Cleave(o,{phone:!0,phoneRegionCode:"US"})}),FormValidation.formValidation(c,{fields:{userFullname:{validators:{notEmpty:{message:"Please enter fullname "}}},userEmail:{validators:{notEmpty:{message:"Please enter your email"},emailAddress:{message:"The value is not a valid email address"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(o,f){return".mb-6"}}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}})})();
