$(function(){let i,d,c;isDarkStyle?(i=config.colors_dark.borderColor,d=config.colors_dark.bodyBg,c=config.colors_dark.headingColor):(i=config.colors.borderColor,d=config.colors.bodyBg,c=config.colors.headingColor);var u=$(".datatables-referral"),m=baseUrl+"app/ecommerce/customer/details/overview",f={1:{title:"Paid",class:"bg-label-success"},2:{title:"Unpaid",class:"bg-label-warning"},3:{title:"Rejected",class:"bg-label-danger"}};u.length&&(u.DataTable({ajax:assetsPath+"json/ecommerce-referral.json",columns:[{data:""},{data:"id"},{data:"user"},{data:"referred_id"},{data:"status"},{data:"value"},{data:"earning"}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(a,n,s,r){return""}},{targets:1,orderable:!1,searchable:!1,responsivePriority:3,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,responsivePriority:1,render:function(a,n,s,r){var e=s.user,o=s.email,t=s.avatar;if(t)var p='<img src="'+assetsPath+"img/avatars/"+t+'" alt="Avatar" class="rounded-circle">';else{var x=Math.floor(Math.random()*6),b=["success","danger","warning","info","dark","primary","secondary"],h=b[x],e=s.user,l=e.match(/\b\w/g)||[];l=((l.shift()||"")+(l.pop()||"")).toUpperCase(),p='<span class="avatar-initial rounded-circle bg-label-'+h+'">'+l+"</span>"}var g='<div class="d-flex justify-content-start align-items-center customer-name"><div class="avatar-wrapper"><div class="avatar avatar-sm me-4">'+p+'</div></div><div class="d-flex flex-column"><a href="'+m+'" class="text-heading"><span class="fw-medium">'+e+'</span></a><small class="text-nowrap">'+o+"</small></div></div>";return g}},{targets:3,render:function(a,n,s,r){var e=s.referred_id;return"<span>"+e+"</span>"}},{targets:4,render:function(a,n,s,r){var e=s.status;return'<span class="badge '+f[e].class+'" text-capitalized>'+f[e].title+"</span>"}},{targets:5,render:function(a,n,s,r){var e=s.value;return"<span>"+e+"</span>"}},{targets:6,render:function(a,n,s,r){var e=s.earning;return'<span class="text-heading">'+e+"</span > "}}],order:[[2,"asc"]],dom:'<"card-header d-flex flex-column flex-sm-row align-items-center py-0"<"head-label"><"d-flex align-items-center justify-content-end"l<"dt-action-buttons"B>>>t<"row mx-1"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',language:{sLengthMenu:"_MENU_",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-secondary dropdown-toggle waves-effect waves-light",text:'<i class="ti ti-upload ti-xs me-2"></i>Export',buttons:[{extend:"print",text:'<i class="ti ti-printer me-2"></i>Print',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("user-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}},customize:function(a){$(a.document.body).css("color",c).css("border-color",i).css("background-color",d),$(a.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file me-2" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("user-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"excel",text:'<i class="ti ti-file-export me-2"></i>Excel',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("user-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"pdf",text:'<i class="ti ti-file-text me-2"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("user-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}},{extend:"copy",text:'<i class="ti ti-copy me-2" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(a,n,s){if(a.length<=0)return a;var r=$.parseHTML(a),e="";return $.each(r,function(o,t){t.classList!==void 0&&t.classList.contains("user-name")?e=e+t.lastChild.firstChild.textContent:t.innerText===void 0?e=e+t.textContent:e=e+t.innerText}),e}}}}]}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var n=a.data();return"Details of "+n.user}}),type:"column",renderer:function(a,n,s){var r=$.map(s,function(e,o){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return r?$('<table class="table"/><tbody />').append(r):!1}}}}),$("div.head-label").html('<h5 class="card-title mb-0 text-nowrap mt-6 mt-sm-0">Referred users</h5>'),$(".dataTables_length").addClass("me-2 ms-n2 ms-sm-0"),$(".dt-action-buttons").addClass("pt-0")),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
