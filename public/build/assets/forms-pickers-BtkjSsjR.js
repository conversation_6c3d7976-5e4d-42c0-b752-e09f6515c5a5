(function(){const e=document.querySelector("#flatpickr-date"),t=document.querySelector("#flatpickr-time"),i=document.querySelector("#flatpickr-datetime"),r=document.querySelector("#flatpickr-multi"),a=document.querySelector("#flatpickr-range"),n=document.querySelector("#flatpickr-inline"),o=document.querySelector("#flatpickr-human-friendly"),c=document.querySelector("#flatpickr-disabled-range");if(e&&e.flatpickr({monthSelectorType:"static"}),t&&t.flatpickr({enableTime:!0,noCalendar:!0}),i&&i.flatpickr({enableTime:!0,dateFormat:"Y-m-d H:i"}),r&&r.flatpickr({weekNumbers:!0,enableTime:!0,mode:"multiple",minDate:"today"}),typeof a!=null&&a.flatpickr({mode:"range"}),n&&n.flatpickr({inline:!0,allowInput:!1,monthSelectorType:"static"}),o&&o.flatpickr({altInput:!0,altFormat:"F j, Y",dateFormat:"Y-m-d"}),c){const l=new Date(Date.now()-1728e5),s=new Date(Date.now()+3600*1e3*48);c.flatpickr({dateFormat:"Y-m-d",disable:[{from:l.toISOString().split("T")[0],to:s.toISOString().split("T")[0]}]})}})();$(function(){var e=$("#bs-datepicker-basic"),t=$("#bs-datepicker-format"),i=$("#bs-datepicker-daterange"),r=$("#bs-datepicker-disabled-days"),a=$("#bs-datepicker-multidate"),n=$("#bs-datepicker-options"),o=$("#bs-datepicker-autoclose"),c=$("#bs-datepicker-inline");e.length&&e.datepicker({todayHighlight:!0,orientation:isRtl?"auto right":"auto left"}),t.length&&t.datepicker({todayHighlight:!0,format:"dd/mm/yyyy",orientation:isRtl?"auto right":"auto left"}),i.length&&i.datepicker({todayHighlight:!0,orientation:isRtl?"auto right":"auto left"}),r.length&&r.datepicker({todayHighlight:!0,daysOfWeekDisabled:[0,6],orientation:isRtl?"auto right":"auto left"}),a.length&&a.datepicker({multidate:!0,todayHighlight:!0,orientation:isRtl?"auto right":"auto left"}),n.length&&n.datepicker({calendarWeeks:!0,clearBtn:!0,todayHighlight:!0,orientation:isRtl?"auto left":"auto right"}),o.length&&o.datepicker({todayHighlight:!0,autoclose:!0,orientation:isRtl?"auto right":"auto left"}),c.length&&c.datepicker({todayHighlight:!0});var l=$("#bs-rangepicker-basic"),s=$("#bs-rangepicker-single"),p=$("#bs-rangepicker-time"),g=$("#bs-rangepicker-range"),k=$("#bs-rangepicker-week-num"),d=$("#bs-rangepicker-dropdown"),u=document.getElementsByClassName("cancelBtn");l.length&&l.daterangepicker({opens:isRtl?"left":"right"}),s.length&&s.daterangepicker({singleDatePicker:!0,opens:isRtl?"left":"right"}),p.length&&p.daterangepicker({timePicker:!0,timePickerIncrement:30,locale:{format:"MM/DD/YYYY h:mm A"},opens:isRtl?"left":"right"}),g.length&&g.daterangepicker({ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]},opens:isRtl?"left":"right"}),k.length&&k.daterangepicker({showWeekNumbers:!0,opens:isRtl?"left":"right"}),d.length&&d.daterangepicker({showDropdowns:!0,opens:isRtl?"left":"right"});for(var m=0;m<u.length;m++)u[m].classList.remove("btn-default"),u[m].classList.add("btn-label-primary");var f=$("#timepicker-basic"),h=$("#timepicker-min-max"),b=$("#timepicker-disabled-times"),y=$("#timepicker-format"),R=$("#timepicker-step"),D=$("#timepicker-24hours");f.length&&f.timepicker({orientation:isRtl?"r":"l"}),h.length&&h.timepicker({minTime:"2:00pm",maxTime:"7:00pm",showDuration:!0,orientation:isRtl?"r":"l"}),b.length&&b.timepicker({disableTimeRanges:[["12am","3am"],["4am","4:30am"]],orientation:isRtl?"r":"l"}),y.length&&y.timepicker({timeFormat:"H:i:s",orientation:isRtl?"r":"l"}),R.length&&R.timepicker({step:15,orientation:isRtl?"r":"l"}),D.length&&D.timepicker({show:"24:00",timeFormat:"H:i:s",orientation:isRtl?"r":"l"})});(function(){const e=document.querySelector("#color-picker-classic"),t=document.querySelector("#color-picker-monolith"),i=document.querySelector("#color-picker-nano");e&&pickr.create({el:e,theme:"classic",default:"rgba(102, 108, 232, 1)",swatches:["rgba(102, 108, 232, 1)","rgba(40, 208, 148, 1)","rgba(255, 73, 97, 1)","rgba(255, 145, 73, 1)","rgba(30, 159, 242, 1)"],components:{preview:!0,opacity:!0,hue:!0,interaction:{hex:!0,rgba:!0,hsla:!0,hsva:!0,cmyk:!0,input:!0,clear:!0,save:!0}}}),t&&pickr.create({el:t,theme:"monolith",default:"rgba(40, 208, 148, 1)",swatches:["rgba(102, 108, 232, 1)","rgba(40, 208, 148, 1)","rgba(255, 73, 97, 1)","rgba(255, 145, 73, 1)","rgba(30, 159, 242, 1)"],components:{preview:!0,opacity:!0,hue:!0,interaction:{hex:!0,rgba:!0,hsla:!0,hsva:!0,cmyk:!0,input:!0,clear:!0,save:!0}}}),i&&pickr.create({el:i,theme:"nano",default:"rgba(255, 73, 97, 1)",swatches:["rgba(102, 108, 232, 1)","rgba(40, 208, 148, 1)","rgba(255, 73, 97, 1)","rgba(255, 145, 73, 1)","rgba(30, 159, 242, 1)"],components:{preview:!0,opacity:!0,hue:!0,interaction:{hex:!0,rgba:!0,hsla:!0,hsva:!0,cmyk:!0,input:!0,clear:!0,save:!0}}})})();
