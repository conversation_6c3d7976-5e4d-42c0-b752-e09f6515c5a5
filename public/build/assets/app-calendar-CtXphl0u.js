let A="ltr";isRtl&&(A="rtl");document.addEventListener("DOMContentLoaded",function(){(function(){const C=document.getElementById("calendar"),k=document.querySelector(".app-calendar-sidebar"),w=document.getElementById("addEventSidebar"),D=document.querySelector(".app-overlay"),F={Business:"primary",Holiday:"success",Personal:"danger",Family:"warning",ETC:"info"},c=document.querySelector(".offcanvas-title"),L=document.querySelector(".btn-toggle-sidebar"),l=document.querySelector("#addEventBtn"),m=document.querySelector(".btn-delete-event"),Y=document.querySelector(".btn-cancel"),p=document.querySelector("#eventTitle"),i=document.querySelector("#eventStartDate"),d=document.querySelector("#eventEndDate"),g=document.querySelector("#eventURL"),o=$("#eventLabel"),s=$("#eventGuests"),b=document.querySelector("#eventLocation"),h=document.querySelector("#eventDescription"),v=document.querySelector(".allDay-switch"),y=document.querySelector(".select-all"),T=[].slice.call(document.querySelectorAll(".input-filter")),q=document.querySelector(".inline-calendar");let n,u=events,S=!1,x;const f=new bootstrap.Offcanvas(w);//! TODO: Update Event label and guest code to JS once select removes jQuery dependency
if(o.length){let e=function(t){if(!t.id)return t.text;var a="<span class='badge badge-dot bg-"+$(t.element).data("label")+" me-2'> </span>"+t.text;return a};var _=e;o.wrap('<div class="position-relative"></div>').select2({placeholder:"Select value",dropdownParent:o.parent(),templateResult:e,templateSelection:e,minimumResultsForSearch:-1,escapeMarkup:function(t){return t}})}if(s.length){let e=function(t){if(!t.id)return t.text;var a="<div class='d-flex flex-wrap align-items-center'><div class='avatar avatar-xs me-2'><img src='"+assetsPath+"img/avatars/"+$(t.element).data("avatar")+"' alt='avatar' class='rounded-circle' /></div>"+t.text+"</div>";return a};var z=e;s.wrap('<div class="position-relative"></div>').select2({placeholder:"Select value",dropdownParent:s.parent(),closeOnSelect:!1,templateResult:e,templateSelection:e,escapeMarkup:function(t){return t}})}if(i)var I=i.flatpickr({enableTime:!0,altFormat:"Y-m-dTH:i:S",onReady:function(e,t,a){a.isMobile&&a.mobileInput.setAttribute("step",null)}});if(d)var P=d.flatpickr({enableTime:!0,altFormat:"Y-m-dTH:i:S",onReady:function(e,t,a){a.isMobile&&a.mobileInput.setAttribute("step",null)}});q&&(x=q.flatpickr({monthSelectorType:"static",inline:!0}));function V(e){n=e.event,n.url&&(e.jsEvent.preventDefault(),window.open(n.url,"_blank")),f.show(),c&&(c.innerHTML="Update Event"),l.innerHTML="Update",l.classList.add("btn-update-event"),l.classList.remove("btn-add-event"),m.classList.remove("d-none"),p.value=n.title,I.setDate(n.start,!0,"Y-m-d"),n.allDay===!0?v.checked=!0:v.checked=!1,n.end!==null?P.setDate(n.end,!0,"Y-m-d"):P.setDate(n.start,!0,"Y-m-d"),o.val(n.extendedProps.calendar).trigger("change"),n.extendedProps.location!==void 0&&(b.value=n.extendedProps.location),n.extendedProps.guests!==void 0&&s.val(n.extendedProps.guests).trigger("change"),n.extendedProps.description!==void 0&&(h.value=n.extendedProps.description)}function E(){const e=document.querySelector(".fc-sidebarToggle-button");for(e.classList.remove("fc-button-primary"),e.classList.add("d-lg-none","d-inline-block","ps-0");e.firstChild;)e.firstChild.remove();e.setAttribute("data-bs-toggle","sidebar"),e.setAttribute("data-overlay",""),e.setAttribute("data-target","#app-calendar-sidebar"),e.insertAdjacentHTML("beforeend",'<i class="ti ti-menu-2 ti-lg text-heading"></i>')}function B(){let e=[];return[].slice.call(document.querySelectorAll(".input-filter:checked")).forEach(a=>{e.push(a.getAttribute("data-value"))}),e}function H(e,t){let a=B(),N=u.filter(function(j){return a.includes(j.extendedProps.calendar.toLowerCase())});t(N)}let r=new Calendar(C,{initialView:"dayGridMonth",events:H,plugins:[dayGridPlugin,interactionPlugin,listPlugin,timegridPlugin],editable:!0,dragScroll:!0,dayMaxEvents:2,eventResizableFromStart:!0,customButtons:{sidebarToggle:{text:"Sidebar"}},headerToolbar:{start:"sidebarToggle, prev,next, title",end:"dayGridMonth,timeGridWeek,timeGridDay,listMonth"},direction:A,initialDate:new Date,navLinks:!0,eventClassNames:function({event:e}){return["fc-event-"+F[e._def.extendedProps.calendar]]},dateClick:function(e){let t=moment(e.date).format("YYYY-MM-DD");M(),f.show(),c&&(c.innerHTML="Add Event"),l.innerHTML="Add",l.classList.remove("btn-update-event"),l.classList.add("btn-add-event"),m.classList.add("d-none"),i.value=t,d.value=t},eventClick:function(e){V(e)},datesSet:function(){E()},viewDidMount:function(){E()}});r.render(),E();const R=document.getElementById("eventForm");FormValidation.formValidation(R,{fields:{eventTitle:{validators:{notEmpty:{message:"Please enter event title "}}},eventStartDate:{validators:{notEmpty:{message:"Please enter start date "}}},eventEndDate:{validators:{notEmpty:{message:"Please enter end date "}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(e,t){return".mb-5"}}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}}).on("core.form.valid",function(){S=!0}).on("core.form.invalid",function(){S=!1}),L&&L.addEventListener("click",e=>{Y.classList.remove("d-none")});function G(e){u.push(e),r.refetchEvents()}function U(e){e.id=parseInt(e.id),u[u.findIndex(t=>t.id===e.id)]=e,r.refetchEvents()}function O(e){u=u.filter(function(t){return t.id!=e}),r.refetchEvents()}l.addEventListener("click",e=>{if(l.classList.contains("btn-add-event")){if(S){let t={id:r.getEvents().length+1,title:p.value,start:i.value,end:d.value,startStr:i.value,endStr:d.value,display:"block",extendedProps:{location:b.value,guests:s.val(),calendar:o.val(),description:h.value}};g.value&&(t.url=g.value),v.checked&&(t.allDay=!0),G(t),f.hide()}}else if(S){let t={id:n.id,title:p.value,start:i.value,end:d.value,url:g.value,extendedProps:{location:b.value,guests:s.val(),calendar:o.val(),description:h.value},display:"block",allDay:!!v.checked};U(t),f.hide()}}),m.addEventListener("click",e=>{O(parseInt(n.id)),f.hide()});function M(){d.value="",g.value="",i.value="",p.value="",b.value="",v.checked=!1,s.val("").trigger("change"),h.value=""}w.addEventListener("hidden.bs.offcanvas",function(){M()}),L.addEventListener("click",e=>{c&&(c.innerHTML="Add Event"),l.innerHTML="Add",l.classList.remove("btn-update-event"),l.classList.add("btn-add-event"),m.classList.add("d-none"),k.classList.remove("show"),D.classList.remove("show")}),y&&y.addEventListener("click",e=>{e.currentTarget.checked?document.querySelectorAll(".input-filter").forEach(t=>t.checked=1):document.querySelectorAll(".input-filter").forEach(t=>t.checked=0),r.refetchEvents()}),T&&T.forEach(e=>{e.addEventListener("click",()=>{document.querySelectorAll(".input-filter:checked").length<document.querySelectorAll(".input-filter").length?y.checked=!1:y.checked=!0,r.refetchEvents()})}),x.config.onChange.push(function(e){r.changeView(r.view.type,moment(e[0]).format("YYYY-MM-DD")),E(),k.classList.remove("show"),D.classList.remove("show")})})()});
