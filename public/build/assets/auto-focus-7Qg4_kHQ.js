import{r as b}from"./index-CSdwt0eW.js";var s={exports:{}},u={},c={exports:{}},f={};/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-field-status
 * @version 2.4.0
 */var g;function S(){if(g)return f;g=1;var l=b(),a=function(r,i){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},a(r,i)},d=function(r){function i(t){var e=r.call(this,t)||this;return e.statuses=new Map,e.opts=Object.assign({},{onStatusChanged:function(){}},t),e.elementValidatingHandler=e.onElementValidating.bind(e),e.elementValidatedHandler=e.onElementValidated.bind(e),e.elementNotValidatedHandler=e.onElementNotValidated.bind(e),e.elementIgnoredHandler=e.onElementIgnored.bind(e),e.fieldAddedHandler=e.onFieldAdded.bind(e),e.fieldRemovedHandler=e.onFieldRemoved.bind(e),e}return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}a(t,e),t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}(i,r),i.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},i.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},i.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every(function(t){return t==="Valid"||t==="NotValidated"||t==="Ignored"})},i.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},i.prototype.onFieldAdded=function(t){this.statuses.set(t.field,"NotValidated")},i.prototype.onFieldRemoved=function(t){this.statuses.has(t.field)&&this.statuses.delete(t.field),this.handleStatusChanged(this.areFieldsValid())},i.prototype.onElementValidating=function(t){this.statuses.set(t.field,"Validating"),this.handleStatusChanged(!1)},i.prototype.onElementValidated=function(t){this.statuses.set(t.field,t.valid?"Valid":"Invalid"),t.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},i.prototype.onElementNotValidated=function(t){this.statuses.set(t.field,"NotValidated"),this.handleStatusChanged(!1)},i.prototype.onElementIgnored=function(t){this.statuses.set(t.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},i.prototype.handleStatusChanged=function(t){this.isEnabled&&this.opts.onStatusChanged(t)},i}(l.Plugin);return f.FieldStatus=d,f}var v;function I(){return v||(v=1,c.exports=S()),c.exports}/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-auto-focus
 * @version 2.4.0
 */var y;function V(){if(y)return u;y=1;var l=b(),a=I(),d=function(i,t){return d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])},d(i,t)},r=function(i){function t(e){var n=i.call(this,e)||this;return n.opts=Object.assign({},{onPrefocus:function(){}},e),n.invalidFormHandler=n.onFormInvalid.bind(n),n}return function(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function o(){this.constructor=e}d(e,n),e.prototype=n===null?Object.create(n):(o.prototype=n.prototype,new o)}(t,i),t.prototype.install=function(){this.core.on("core.form.invalid",this.invalidFormHandler).registerPlugin(t.FIELD_STATUS_PLUGIN,new a.FieldStatus)},t.prototype.uninstall=function(){this.core.off("core.form.invalid",this.invalidFormHandler).deregisterPlugin(t.FIELD_STATUS_PLUGIN)},t.prototype.onEnabled=function(){this.core.enablePlugin(t.FIELD_STATUS_PLUGIN)},t.prototype.onDisabled=function(){this.core.disablePlugin(t.FIELD_STATUS_PLUGIN)},t.prototype.onFormInvalid=function(){if(this.isEnabled){var e=this.core.getPlugin(t.FIELD_STATUS_PLUGIN).getStatuses(),n=Object.keys(this.core.getFields()).filter(function(F){return e.get(F)==="Invalid"});if(n.length>0){var o=n[0],h=this.core.getElements(o);if(h.length>0){var p=h[0],m={firstElement:p,field:o};this.core.emit("plugins.autofocus.prefocus",m),this.opts.onPrefocus(m),p.focus()}}}},t.FIELD_STATUS_PLUGIN="___autoFocusFieldStatus",t}(l.Plugin);return u.AutoFocus=r,u}var _;function E(){return _||(_=1,s.exports=V()),s.exports}var H=E();try{FormValidation.plugins.AutoFocus=H.AutoFocus}catch{}
