var ts=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);import{r as as}from"./jquery-BQXThELV.js";import{a as ss}from"./_commonjsHelpers-BosuxZz1.js";var dl=ts((yl,Je)=>{var Be={exports:{}};//! moment.js
//! version : 2.30.1
//! authors : <PERSON>, <PERSON><PERSON>, Moment.js contributors
//! license : MIT
//! momentjs.com
var la;function f(){return la.apply(null,arguments)}function is(e){la=e}function Z(e){return e instanceof Array||Object.prototype.toString.call(e)==="[object Array]"}function pe(e){return e!=null&&Object.prototype.toString.call(e)==="[object Object]"}function w(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function bt(e){if(Object.getOwnPropertyNames)return Object.getOwnPropertyNames(e).length===0;var t;for(t in e)if(w(e,t))return!1;return!0}function E(e){return e===void 0}function oe(e){return typeof e=="number"||Object.prototype.toString.call(e)==="[object Number]"}function Le(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function oa(e,t){var a=[],s,i=e.length;for(s=0;s<i;++s)a.push(t(e[s],s));return a}function fe(e,t){for(var a in t)w(t,a)&&(e[a]=t[a]);return w(t,"toString")&&(e.toString=t.toString),w(t,"valueOf")&&(e.valueOf=t.valueOf),e}function ee(e,t,a,s){return Ta(e,t,a,s,!0).utc()}function rs(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function g(e){return e._pf==null&&(e._pf=rs()),e._pf}var kt;Array.prototype.some?kt=Array.prototype.some:kt=function(e){var t=Object(this),a=t.length>>>0,s;for(s=0;s<a;s++)if(s in t&&e.call(this,t[s],s,t))return!0;return!1};function Ot(e){var t=null,a=!1,s=e._d&&!isNaN(e._d.getTime());if(s&&(t=g(e),a=kt.call(t.parsedDateParts,function(i){return i!=null}),s=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&a),e._strict&&(s=s&&t.charsLeftOver===0&&t.unusedTokens.length===0&&t.bigHour===void 0)),Object.isFrozen==null||!Object.isFrozen(e))e._isValid=s;else return s;return e._isValid}function tt(e){var t=ee(NaN);return e!=null?fe(g(t),e):g(t).userInvalidated=!0,t}var ea=f.momentProperties=[],yt=!1;function Ct(e,t){var a,s,i,r=ea.length;if(E(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),E(t._i)||(e._i=t._i),E(t._f)||(e._f=t._f),E(t._l)||(e._l=t._l),E(t._strict)||(e._strict=t._strict),E(t._tzm)||(e._tzm=t._tzm),E(t._isUTC)||(e._isUTC=t._isUTC),E(t._offset)||(e._offset=t._offset),E(t._pf)||(e._pf=g(t)),E(t._locale)||(e._locale=t._locale),r>0)for(a=0;a<r;a++)s=ea[a],i=t[s],E(i)||(e[s]=i);return e}function Fe(e){Ct(this,e),this._d=new Date(e._d!=null?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),yt===!1&&(yt=!0,f.updateOffset(this),yt=!1)}function q(e){return e instanceof Fe||e!=null&&e._isAMomentObject!=null}function ha(e){f.suppressDeprecationWarnings===!1&&typeof console<"u"&&console.warn&&console.warn("Deprecation warning: "+e)}function j(e,t){var a=!0;return fe(function(){if(f.deprecationHandler!=null&&f.deprecationHandler(null,e),a){var s=[],i,r,n,l=arguments.length;for(r=0;r<l;r++){if(i="",typeof arguments[r]=="object"){i+=`
[`+r+"] ";for(n in arguments[0])w(arguments[0],n)&&(i+=n+": "+arguments[0][n]+", ");i=i.slice(0,-2)}else i=arguments[r];s.push(i)}ha(e+`
Arguments: `+Array.prototype.slice.call(s).join("")+`
`+new Error().stack),a=!1}return t.apply(this,arguments)},t)}var ta={};function da(e,t){f.deprecationHandler!=null&&f.deprecationHandler(e,t),ta[e]||(ha(t),ta[e]=!0)}f.suppressDeprecationWarnings=!1;f.deprecationHandler=null;function te(e){return typeof Function<"u"&&e instanceof Function||Object.prototype.toString.call(e)==="[object Function]"}function ns(e){var t,a;for(a in e)w(e,a)&&(t=e[a],te(t)?this[a]=t:this["_"+a]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function _t(e,t){var a=fe({},e),s;for(s in t)w(t,s)&&(pe(e[s])&&pe(t[s])?(a[s]={},fe(a[s],e[s]),fe(a[s],t[s])):t[s]!=null?a[s]=t[s]:delete a[s]);for(s in e)w(e,s)&&!w(t,s)&&pe(e[s])&&(a[s]=fe({},a[s]));return a}function xt(e){e!=null&&this.set(e)}var vt;Object.keys?vt=Object.keys:vt=function(e){var t,a=[];for(t in e)w(e,t)&&a.push(t);return a};var ls={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function os(e,t,a){var s=this._calendar[e]||this._calendar.sameElse;return te(s)?s.call(t,a):s}function K(e,t,a){var s=""+Math.abs(e),i=t-s.length,r=e>=0;return(r?a?"+":"":"-")+Math.pow(10,Math.max(0,i)).toString().substr(1)+s}var Pt=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,je=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,pt={},we={};function m(e,t,a,s){var i=s;typeof s=="string"&&(i=function(){return this[s]()}),e&&(we[e]=i),t&&(we[t[0]]=function(){return K(i.apply(this,arguments),t[1],t[2])}),a&&(we[a]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function hs(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function ds(e){var t=e.match(Pt),a,s;for(a=0,s=t.length;a<s;a++)we[t[a]]?t[a]=we[t[a]]:t[a]=hs(t[a]);return function(i){var r="",n;for(n=0;n<s;n++)r+=te(t[n])?t[n].call(i,e):t[n];return r}}function ze(e,t){return e.isValid()?(t=fa(t,e.localeData()),pt[t]=pt[t]||ds(t),pt[t](e)):e.localeData().invalidDate()}function fa(e,t){var a=5;function s(i){return t.longDateFormat(i)||i}for(je.lastIndex=0;a>=0&&je.test(e);)e=e.replace(je,s),je.lastIndex=0,a-=1;return e}var fs={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function cs(e){var t=this._longDateFormat[e],a=this._longDateFormat[e.toUpperCase()];return t||!a?t:(this._longDateFormat[e]=a.match(Pt).map(function(s){return s==="MMMM"||s==="MM"||s==="DD"||s==="dddd"?s.slice(1):s}).join(""),this._longDateFormat[e])}var us="Invalid date";function ms(){return this._invalidDate}var ys="%d",ps=/\d{1,2}/;function Ds(e){return this._ordinal.replace("%d",e)}var gs={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function ks(e,t,a,s){var i=this._relativeTime[a];return te(i)?i(e,t,a,s):i.replace(/%d/i,e)}function _s(e,t){var a=this._relativeTime[e>0?"future":"past"];return te(a)?a(t):a.replace(/%s/i,t)}var aa={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function G(e){return typeof e=="string"?aa[e]||aa[e.toLowerCase()]:void 0}function Tt(e){var t={},a,s;for(s in e)w(e,s)&&(a=G(s),a&&(t[a]=e[s]));return t}var vs={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};function ws(e){var t=[],a;for(a in e)w(e,a)&&t.push({unit:a,priority:vs[a]});return t.sort(function(s,i){return s.priority-i.priority}),t}var ca=/\d/,H=/\d\d/,ua=/\d{3}/,Nt=/\d{4}/,at=/[+-]?\d{6}/,x=/\d\d?/,ma=/\d\d\d\d?/,ya=/\d\d\d\d\d\d?/,st=/\d{1,3}/,Wt=/\d{1,4}/,it=/[+-]?\d{1,6}/,Se=/\d+/,rt=/[+-]?\d+/,Ys=/Z|[+-]\d\d:?\d\d/gi,nt=/Z|[+-]\d\d(?::?\d\d)?/gi,Ms=/[+-]?\d+(\.\d{1,3})?/,Ae=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,be=/^[1-9]\d?/,It=/^([1-9]\d|\d)/,Qe;Qe={};function c(e,t,a){Qe[e]=te(t)?t:function(s,i){return s&&a?a:t}}function Ss(e,t){return w(Qe,e)?Qe[e](t._strict,t._locale):new RegExp(bs(e))}function bs(e){return ne(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,a,s,i,r){return a||s||i||r}))}function ne(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function V(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function _(e){var t=+e,a=0;return t!==0&&isFinite(t)&&(a=V(t)),a}var wt={};function M(e,t){var a,s=t,i;for(typeof e=="string"&&(e=[e]),oe(t)&&(s=function(r,n){n[t]=_(r)}),i=e.length,a=0;a<i;a++)wt[e[a]]=s}function Ee(e,t){M(e,function(a,s,i,r){i._w=i._w||{},t(a,i._w,i,r)})}function Os(e,t,a){t!=null&&w(wt,e)&&wt[e](t,a._a,a,e)}function lt(e){return e%4===0&&e%100!==0||e%400===0}var F=0,ie=1,X=2,I=3,z=4,re=5,ye=6,Cs=7,xs=8;m("Y",0,0,function(){var e=this.year();return e<=9999?K(e,4):"+"+e});m(0,["YY",2],0,function(){return this.year()%100});m(0,["YYYY",4],0,"year");m(0,["YYYYY",5],0,"year");m(0,["YYYYYY",6,!0],0,"year");c("Y",rt);c("YY",x,H);c("YYYY",Wt,Nt);c("YYYYY",it,at);c("YYYYYY",it,at);M(["YYYYY","YYYYYY"],F);M("YYYY",function(e,t){t[F]=e.length===2?f.parseTwoDigitYear(e):_(e)});M("YY",function(e,t){t[F]=f.parseTwoDigitYear(e)});M("Y",function(e,t){t[F]=parseInt(e,10)});function Te(e){return lt(e)?366:365}f.parseTwoDigitYear=function(e){return _(e)+(_(e)>68?1900:2e3)};var pa=Oe("FullYear",!0);function Ps(){return lt(this.year())}function Oe(e,t){return function(a){return a!=null?(Da(this,e,a),f.updateOffset(this,t),this):Ne(this,e)}}function Ne(e,t){if(!e.isValid())return NaN;var a=e._d,s=e._isUTC;switch(t){case"Milliseconds":return s?a.getUTCMilliseconds():a.getMilliseconds();case"Seconds":return s?a.getUTCSeconds():a.getSeconds();case"Minutes":return s?a.getUTCMinutes():a.getMinutes();case"Hours":return s?a.getUTCHours():a.getHours();case"Date":return s?a.getUTCDate():a.getDate();case"Day":return s?a.getUTCDay():a.getDay();case"Month":return s?a.getUTCMonth():a.getMonth();case"FullYear":return s?a.getUTCFullYear():a.getFullYear();default:return NaN}}function Da(e,t,a){var s,i,r,n,l;if(!(!e.isValid()||isNaN(a))){switch(s=e._d,i=e._isUTC,t){case"Milliseconds":return void(i?s.setUTCMilliseconds(a):s.setMilliseconds(a));case"Seconds":return void(i?s.setUTCSeconds(a):s.setSeconds(a));case"Minutes":return void(i?s.setUTCMinutes(a):s.setMinutes(a));case"Hours":return void(i?s.setUTCHours(a):s.setHours(a));case"Date":return void(i?s.setUTCDate(a):s.setDate(a));case"FullYear":break;default:return}r=a,n=e.month(),l=e.date(),l=l===29&&n===1&&!lt(r)?28:l,i?s.setUTCFullYear(r,n,l):s.setFullYear(r,n,l)}}function Ts(e){return e=G(e),te(this[e])?this[e]():this}function Ns(e,t){if(typeof e=="object"){e=Tt(e);var a=ws(e),s,i=a.length;for(s=0;s<i;s++)this[a[s].unit](e[a[s].unit])}else if(e=G(e),te(this[e]))return this[e](t);return this}function Ws(e,t){return(e%t+t)%t}var W;Array.prototype.indexOf?W=Array.prototype.indexOf:W=function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1};function Rt(e,t){if(isNaN(e)||isNaN(t))return NaN;var a=Ws(t,12);return e+=(t-a)/12,a===1?lt(e)?29:28:31-a%7%2}m("M",["MM",2],"Mo",function(){return this.month()+1});m("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)});m("MMMM",0,0,function(e){return this.localeData().months(this,e)});c("M",x,be);c("MM",x,H);c("MMM",function(e,t){return t.monthsShortRegex(e)});c("MMMM",function(e,t){return t.monthsRegex(e)});M(["M","MM"],function(e,t){t[ie]=_(e)-1});M(["MMM","MMMM"],function(e,t,a,s){var i=a._locale.monthsParse(e,s,a._strict);i!=null?t[ie]=i:g(a).invalidMonth=e});var Is="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ga="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),ka=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Rs=Ae,Ls=Ae;function Fs(e,t){return e?Z(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||ka).test(t)?"format":"standalone"][e.month()]:Z(this._months)?this._months:this._months.standalone}function As(e,t){return e?Z(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[ka.test(t)?"format":"standalone"][e.month()]:Z(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function Es(e,t,a){var s,i,r,n=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],s=0;s<12;++s)r=ee([2e3,s]),this._shortMonthsParse[s]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[s]=this.months(r,"").toLocaleLowerCase();return a?t==="MMM"?(i=W.call(this._shortMonthsParse,n),i!==-1?i:null):(i=W.call(this._longMonthsParse,n),i!==-1?i:null):t==="MMM"?(i=W.call(this._shortMonthsParse,n),i!==-1?i:(i=W.call(this._longMonthsParse,n),i!==-1?i:null)):(i=W.call(this._longMonthsParse,n),i!==-1?i:(i=W.call(this._shortMonthsParse,n),i!==-1?i:null))}function Hs(e,t,a){var s,i,r;if(this._monthsParseExact)return Es.call(this,e,t,a);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),s=0;s<12;s++){if(i=ee([2e3,s]),a&&!this._longMonthsParse[s]&&(this._longMonthsParse[s]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[s]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),!a&&!this._monthsParse[s]&&(r="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[s]=new RegExp(r.replace(".",""),"i")),a&&t==="MMMM"&&this._longMonthsParse[s].test(e))return s;if(a&&t==="MMM"&&this._shortMonthsParse[s].test(e))return s;if(!a&&this._monthsParse[s].test(e))return s}}function _a(e,t){if(!e.isValid())return e;if(typeof t=="string"){if(/^\d+$/.test(t))t=_(t);else if(t=e.localeData().monthsParse(t),!oe(t))return e}var a=t,s=e.date();return s=s<29?s:Math.min(s,Rt(e.year(),a)),e._isUTC?e._d.setUTCMonth(a,s):e._d.setMonth(a,s),e}function va(e){return e!=null?(_a(this,e),f.updateOffset(this,!0),this):Ne(this,"Month")}function Us(){return Rt(this.year(),this.month())}function Vs(e){return this._monthsParseExact?(w(this,"_monthsRegex")||wa.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(w(this,"_monthsShortRegex")||(this._monthsShortRegex=Rs),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)}function js(e){return this._monthsParseExact?(w(this,"_monthsRegex")||wa.call(this),e?this._monthsStrictRegex:this._monthsRegex):(w(this,"_monthsRegex")||(this._monthsRegex=Ls),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)}function wa(){function e(h,d){return d.length-h.length}var t=[],a=[],s=[],i,r,n,l;for(i=0;i<12;i++)r=ee([2e3,i]),n=ne(this.monthsShort(r,"")),l=ne(this.months(r,"")),t.push(n),a.push(l),s.push(l),s.push(n);t.sort(e),a.sort(e),s.sort(e),this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+t.join("|")+")","i")}function Gs(e,t,a,s,i,r,n){var l;return e<100&&e>=0?(l=new Date(e+400,t,a,s,i,r,n),isFinite(l.getFullYear())&&l.setFullYear(e)):l=new Date(e,t,a,s,i,r,n),l}function We(e){var t,a;return e<100&&e>=0?(a=Array.prototype.slice.call(arguments),a[0]=e+400,t=new Date(Date.UTC.apply(null,a)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Xe(e,t,a){var s=7+t-a,i=(7+We(e,0,s).getUTCDay()-t)%7;return-i+s-1}function Ya(e,t,a,s,i){var r=(7+a-s)%7,n=Xe(e,s,i),l=1+7*(t-1)+r+n,h,d;return l<=0?(h=e-1,d=Te(h)+l):l>Te(e)?(h=e+1,d=l-Te(e)):(h=e,d=l),{year:h,dayOfYear:d}}function Ie(e,t,a){var s=Xe(e.year(),t,a),i=Math.floor((e.dayOfYear()-s-1)/7)+1,r,n;return i<1?(n=e.year()-1,r=i+le(n,t,a)):i>le(e.year(),t,a)?(r=i-le(e.year(),t,a),n=e.year()+1):(n=e.year(),r=i),{week:r,year:n}}function le(e,t,a){var s=Xe(e,t,a),i=Xe(e+1,t,a);return(Te(e)-s+i)/7}m("w",["ww",2],"wo","week");m("W",["WW",2],"Wo","isoWeek");c("w",x,be);c("ww",x,H);c("W",x,be);c("WW",x,H);Ee(["w","ww","W","WW"],function(e,t,a,s){t[s.substr(0,1)]=_(e)});function Bs(e){return Ie(e,this._week.dow,this._week.doy).week}var zs={dow:0,doy:6};function Zs(){return this._week.dow}function qs(){return this._week.doy}function Js(e){var t=this.localeData().week(this);return e==null?t:this.add((e-t)*7,"d")}function Qs(e){var t=Ie(this,1,4).week;return e==null?t:this.add((e-t)*7,"d")}m("d",0,"do","day");m("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)});m("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)});m("dddd",0,0,function(e){return this.localeData().weekdays(this,e)});m("e",0,0,"weekday");m("E",0,0,"isoWeekday");c("d",x);c("e",x);c("E",x);c("dd",function(e,t){return t.weekdaysMinRegex(e)});c("ddd",function(e,t){return t.weekdaysShortRegex(e)});c("dddd",function(e,t){return t.weekdaysRegex(e)});Ee(["dd","ddd","dddd"],function(e,t,a,s){var i=a._locale.weekdaysParse(e,s,a._strict);i!=null?t.d=i:g(a).invalidWeekday=e});Ee(["d","e","E"],function(e,t,a,s){t[s]=_(e)});function Xs(e,t){return typeof e!="string"?e:isNaN(e)?(e=t.weekdaysParse(e),typeof e=="number"?e:null):parseInt(e,10)}function Ks(e,t){return typeof e=="string"?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}function Lt(e,t){return e.slice(t,7).concat(e.slice(0,t))}var $s="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ma="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),ei="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),ti=Ae,ai=Ae,si=Ae;function ii(e,t){var a=Z(this._weekdays)?this._weekdays:this._weekdays[e&&e!==!0&&this._weekdays.isFormat.test(t)?"format":"standalone"];return e===!0?Lt(a,this._week.dow):e?a[e.day()]:a}function ri(e){return e===!0?Lt(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort}function ni(e){return e===!0?Lt(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin}function li(e,t,a){var s,i,r,n=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],s=0;s<7;++s)r=ee([2e3,1]).day(s),this._minWeekdaysParse[s]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[s]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[s]=this.weekdays(r,"").toLocaleLowerCase();return a?t==="dddd"?(i=W.call(this._weekdaysParse,n),i!==-1?i:null):t==="ddd"?(i=W.call(this._shortWeekdaysParse,n),i!==-1?i:null):(i=W.call(this._minWeekdaysParse,n),i!==-1?i:null):t==="dddd"?(i=W.call(this._weekdaysParse,n),i!==-1||(i=W.call(this._shortWeekdaysParse,n),i!==-1)?i:(i=W.call(this._minWeekdaysParse,n),i!==-1?i:null)):t==="ddd"?(i=W.call(this._shortWeekdaysParse,n),i!==-1||(i=W.call(this._weekdaysParse,n),i!==-1)?i:(i=W.call(this._minWeekdaysParse,n),i!==-1?i:null)):(i=W.call(this._minWeekdaysParse,n),i!==-1||(i=W.call(this._weekdaysParse,n),i!==-1)?i:(i=W.call(this._shortWeekdaysParse,n),i!==-1?i:null))}function oi(e,t,a){var s,i,r;if(this._weekdaysParseExact)return li.call(this,e,t,a);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),s=0;s<7;s++){if(i=ee([2e3,1]).day(s),a&&!this._fullWeekdaysParse[s]&&(this._fullWeekdaysParse[s]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[s]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[s]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[s]||(r="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[s]=new RegExp(r.replace(".",""),"i")),a&&t==="dddd"&&this._fullWeekdaysParse[s].test(e))return s;if(a&&t==="ddd"&&this._shortWeekdaysParse[s].test(e))return s;if(a&&t==="dd"&&this._minWeekdaysParse[s].test(e))return s;if(!a&&this._weekdaysParse[s].test(e))return s}}function hi(e){if(!this.isValid())return e!=null?this:NaN;var t=Ne(this,"Day");return e!=null?(e=Xs(e,this.localeData()),this.add(e-t,"d")):t}function di(e){if(!this.isValid())return e!=null?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return e==null?t:this.add(e-t,"d")}function fi(e){if(!this.isValid())return e!=null?this:NaN;if(e!=null){var t=Ks(e,this.localeData());return this.day(this.day()%7?t:t-7)}else return this.day()||7}function ci(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Ft.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(w(this,"_weekdaysRegex")||(this._weekdaysRegex=ti),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)}function ui(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Ft.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(w(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=ai),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function mi(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Ft.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(w(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=si),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Ft(){function e(u,D){return D.length-u.length}var t=[],a=[],s=[],i=[],r,n,l,h,d;for(r=0;r<7;r++)n=ee([2e3,1]).day(r),l=ne(this.weekdaysMin(n,"")),h=ne(this.weekdaysShort(n,"")),d=ne(this.weekdays(n,"")),t.push(l),a.push(h),s.push(d),i.push(l),i.push(h),i.push(d);t.sort(e),a.sort(e),s.sort(e),i.sort(e),this._weekdaysRegex=new RegExp("^("+i.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+t.join("|")+")","i")}function At(){return this.hours()%12||12}function yi(){return this.hours()||24}m("H",["HH",2],0,"hour");m("h",["hh",2],0,At);m("k",["kk",2],0,yi);m("hmm",0,0,function(){return""+At.apply(this)+K(this.minutes(),2)});m("hmmss",0,0,function(){return""+At.apply(this)+K(this.minutes(),2)+K(this.seconds(),2)});m("Hmm",0,0,function(){return""+this.hours()+K(this.minutes(),2)});m("Hmmss",0,0,function(){return""+this.hours()+K(this.minutes(),2)+K(this.seconds(),2)});function Sa(e,t){m(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}Sa("a",!0);Sa("A",!1);function ba(e,t){return t._meridiemParse}c("a",ba);c("A",ba);c("H",x,It);c("h",x,be);c("k",x,be);c("HH",x,H);c("hh",x,H);c("kk",x,H);c("hmm",ma);c("hmmss",ya);c("Hmm",ma);c("Hmmss",ya);M(["H","HH"],I);M(["k","kk"],function(e,t,a){var s=_(e);t[I]=s===24?0:s});M(["a","A"],function(e,t,a){a._isPm=a._locale.isPM(e),a._meridiem=e});M(["h","hh"],function(e,t,a){t[I]=_(e),g(a).bigHour=!0});M("hmm",function(e,t,a){var s=e.length-2;t[I]=_(e.substr(0,s)),t[z]=_(e.substr(s)),g(a).bigHour=!0});M("hmmss",function(e,t,a){var s=e.length-4,i=e.length-2;t[I]=_(e.substr(0,s)),t[z]=_(e.substr(s,2)),t[re]=_(e.substr(i)),g(a).bigHour=!0});M("Hmm",function(e,t,a){var s=e.length-2;t[I]=_(e.substr(0,s)),t[z]=_(e.substr(s))});M("Hmmss",function(e,t,a){var s=e.length-4,i=e.length-2;t[I]=_(e.substr(0,s)),t[z]=_(e.substr(s,2)),t[re]=_(e.substr(i))});function pi(e){return(e+"").toLowerCase().charAt(0)==="p"}var Di=/[ap]\.?m?\.?/i,gi=Oe("Hours",!0);function ki(e,t,a){return e>11?a?"pm":"PM":a?"am":"AM"}var Oa={calendar:ls,longDateFormat:fs,invalidDate:us,ordinal:ys,dayOfMonthOrdinalParse:ps,relativeTime:gs,months:Is,monthsShort:ga,week:zs,weekdays:$s,weekdaysMin:ei,weekdaysShort:Ma,meridiemParse:Di},T={},xe={},Re;function _i(e,t){var a,s=Math.min(e.length,t.length);for(a=0;a<s;a+=1)if(e[a]!==t[a])return a;return s}function sa(e){return e&&e.toLowerCase().replace("_","-")}function vi(e){for(var t=0,a,s,i,r;t<e.length;){for(r=sa(e[t]).split("-"),a=r.length,s=sa(e[t+1]),s=s?s.split("-"):null;a>0;){if(i=ot(r.slice(0,a).join("-")),i)return i;if(s&&s.length>=a&&_i(r,s)>=a-1)break;a--}t++}return Re}function wi(e){return!!(e&&e.match("^[^/\\\\]*$"))}function ot(e){var t=null,a;if(T[e]===void 0&&typeof Je<"u"&&Je&&Je.exports&&wi(e))try{t=Re._abbr,a=require,a("./locale/"+e),ue(t)}catch{T[e]=null}return T[e]}function ue(e,t){var a;return e&&(E(t)?a=he(e):a=Et(e,t),a?Re=a:typeof console<"u"&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Re._abbr}function Et(e,t){if(t!==null){var a,s=Oa;if(t.abbr=e,T[e]!=null)da("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),s=T[e]._config;else if(t.parentLocale!=null)if(T[t.parentLocale]!=null)s=T[t.parentLocale]._config;else if(a=ot(t.parentLocale),a!=null)s=a._config;else return xe[t.parentLocale]||(xe[t.parentLocale]=[]),xe[t.parentLocale].push({name:e,config:t}),null;return T[e]=new xt(_t(s,t)),xe[e]&&xe[e].forEach(function(i){Et(i.name,i.config)}),ue(e),T[e]}else return delete T[e],null}function Yi(e,t){if(t!=null){var a,s,i=Oa;T[e]!=null&&T[e].parentLocale!=null?T[e].set(_t(T[e]._config,t)):(s=ot(e),s!=null&&(i=s._config),t=_t(i,t),s==null&&(t.abbr=e),a=new xt(t),a.parentLocale=T[e],T[e]=a),ue(e)}else T[e]!=null&&(T[e].parentLocale!=null?(T[e]=T[e].parentLocale,e===ue()&&ue(e)):T[e]!=null&&delete T[e]);return T[e]}function he(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Re;if(!Z(e)){if(t=ot(e),t)return t;e=[e]}return vi(e)}function Mi(){return vt(T)}function Ht(e){var t,a=e._a;return a&&g(e).overflow===-2&&(t=a[ie]<0||a[ie]>11?ie:a[X]<1||a[X]>Rt(a[F],a[ie])?X:a[I]<0||a[I]>24||a[I]===24&&(a[z]!==0||a[re]!==0||a[ye]!==0)?I:a[z]<0||a[z]>59?z:a[re]<0||a[re]>59?re:a[ye]<0||a[ye]>999?ye:-1,g(e)._overflowDayOfYear&&(t<F||t>X)&&(t=X),g(e)._overflowWeeks&&t===-1&&(t=Cs),g(e)._overflowWeekday&&t===-1&&(t=xs),g(e).overflow=t),e}var Si=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,bi=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Oi=/Z|[+-]\d\d(?::?\d\d)?/,Ge=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],Dt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Ci=/^\/?Date\((-?\d+)/i,xi=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Pi={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Ca(e){var t,a,s=e._i,i=Si.exec(s)||bi.exec(s),r,n,l,h,d=Ge.length,u=Dt.length;if(i){for(g(e).iso=!0,t=0,a=d;t<a;t++)if(Ge[t][1].exec(i[1])){n=Ge[t][0],r=Ge[t][2]!==!1;break}if(n==null){e._isValid=!1;return}if(i[3]){for(t=0,a=u;t<a;t++)if(Dt[t][1].exec(i[3])){l=(i[2]||" ")+Dt[t][0];break}if(l==null){e._isValid=!1;return}}if(!r&&l!=null){e._isValid=!1;return}if(i[4])if(Oi.exec(i[4]))h="Z";else{e._isValid=!1;return}e._f=n+(l||"")+(h||""),Vt(e)}else e._isValid=!1}function Ti(e,t,a,s,i,r){var n=[Ni(e),ga.indexOf(t),parseInt(a,10),parseInt(s,10),parseInt(i,10)];return r&&n.push(parseInt(r,10)),n}function Ni(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function Wi(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function Ii(e,t,a){if(e){var s=Ma.indexOf(e),i=new Date(t[0],t[1],t[2]).getDay();if(s!==i)return g(a).weekdayMismatch=!0,a._isValid=!1,!1}return!0}function Ri(e,t,a){if(e)return Pi[e];if(t)return 0;var s=parseInt(a,10),i=s%100,r=(s-i)/100;return r*60+i}function xa(e){var t=xi.exec(Wi(e._i)),a;if(t){if(a=Ti(t[4],t[3],t[2],t[5],t[6],t[7]),!Ii(t[1],a,e))return;e._a=a,e._tzm=Ri(t[8],t[9],t[10]),e._d=We.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),g(e).rfc2822=!0}else e._isValid=!1}function Li(e){var t=Ci.exec(e._i);if(t!==null){e._d=new Date(+t[1]);return}if(Ca(e),e._isValid===!1)delete e._isValid;else return;if(xa(e),e._isValid===!1)delete e._isValid;else return;e._strict?e._isValid=!1:f.createFromInputFallback(e)}f.createFromInputFallback=j("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))});function _e(e,t,a){return e??t??a}function Fi(e){var t=new Date(f.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function Ut(e){var t,a,s=[],i,r,n;if(!e._d){for(i=Fi(e),e._w&&e._a[X]==null&&e._a[ie]==null&&Ai(e),e._dayOfYear!=null&&(n=_e(e._a[F],i[F]),(e._dayOfYear>Te(n)||e._dayOfYear===0)&&(g(e)._overflowDayOfYear=!0),a=We(n,0,e._dayOfYear),e._a[ie]=a.getUTCMonth(),e._a[X]=a.getUTCDate()),t=0;t<3&&e._a[t]==null;++t)e._a[t]=s[t]=i[t];for(;t<7;t++)e._a[t]=s[t]=e._a[t]==null?t===2?1:0:e._a[t];e._a[I]===24&&e._a[z]===0&&e._a[re]===0&&e._a[ye]===0&&(e._nextDay=!0,e._a[I]=0),e._d=(e._useUTC?We:Gs).apply(null,s),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),e._tzm!=null&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[I]=24),e._w&&typeof e._w.d<"u"&&e._w.d!==r&&(g(e).weekdayMismatch=!0)}}function Ai(e){var t,a,s,i,r,n,l,h,d;t=e._w,t.GG!=null||t.W!=null||t.E!=null?(r=1,n=4,a=_e(t.GG,e._a[F],Ie(C(),1,4).year),s=_e(t.W,1),i=_e(t.E,1),(i<1||i>7)&&(h=!0)):(r=e._locale._week.dow,n=e._locale._week.doy,d=Ie(C(),r,n),a=_e(t.gg,e._a[F],d.year),s=_e(t.w,d.week),t.d!=null?(i=t.d,(i<0||i>6)&&(h=!0)):t.e!=null?(i=t.e+r,(t.e<0||t.e>6)&&(h=!0)):i=r),s<1||s>le(a,r,n)?g(e)._overflowWeeks=!0:h!=null?g(e)._overflowWeekday=!0:(l=Ya(a,s,i,r,n),e._a[F]=l.year,e._dayOfYear=l.dayOfYear)}f.ISO_8601=function(){};f.RFC_2822=function(){};function Vt(e){if(e._f===f.ISO_8601){Ca(e);return}if(e._f===f.RFC_2822){xa(e);return}e._a=[],g(e).empty=!0;var t=""+e._i,a,s,i,r,n,l=t.length,h=0,d,u;for(i=fa(e._f,e._locale).match(Pt)||[],u=i.length,a=0;a<u;a++)r=i[a],s=(t.match(Ss(r,e))||[])[0],s&&(n=t.substr(0,t.indexOf(s)),n.length>0&&g(e).unusedInput.push(n),t=t.slice(t.indexOf(s)+s.length),h+=s.length),we[r]?(s?g(e).empty=!1:g(e).unusedTokens.push(r),Os(r,s,e)):e._strict&&!s&&g(e).unusedTokens.push(r);g(e).charsLeftOver=l-h,t.length>0&&g(e).unusedInput.push(t),e._a[I]<=12&&g(e).bigHour===!0&&e._a[I]>0&&(g(e).bigHour=void 0),g(e).parsedDateParts=e._a.slice(0),g(e).meridiem=e._meridiem,e._a[I]=Ei(e._locale,e._a[I],e._meridiem),d=g(e).era,d!==null&&(e._a[F]=e._locale.erasConvertYear(d,e._a[F])),Ut(e),Ht(e)}function Ei(e,t,a){var s;return a==null?t:e.meridiemHour!=null?e.meridiemHour(t,a):(e.isPM!=null&&(s=e.isPM(a),s&&t<12&&(t+=12),!s&&t===12&&(t=0)),t)}function Hi(e){var t,a,s,i,r,n,l=!1,h=e._f.length;if(h===0){g(e).invalidFormat=!0,e._d=new Date(NaN);return}for(i=0;i<h;i++)r=0,n=!1,t=Ct({},e),e._useUTC!=null&&(t._useUTC=e._useUTC),t._f=e._f[i],Vt(t),Ot(t)&&(n=!0),r+=g(t).charsLeftOver,r+=g(t).unusedTokens.length*10,g(t).score=r,l?r<s&&(s=r,a=t):(s==null||r<s||n)&&(s=r,a=t,n&&(l=!0));fe(e,a||t)}function Ui(e){if(!e._d){var t=Tt(e._i),a=t.day===void 0?t.date:t.day;e._a=oa([t.year,t.month,a,t.hour,t.minute,t.second,t.millisecond],function(s){return s&&parseInt(s,10)}),Ut(e)}}function Vi(e){var t=new Fe(Ht(Pa(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}function Pa(e){var t=e._i,a=e._f;return e._locale=e._locale||he(e._l),t===null||a===void 0&&t===""?tt({nullInput:!0}):(typeof t=="string"&&(e._i=t=e._locale.preparse(t)),q(t)?new Fe(Ht(t)):(Le(t)?e._d=t:Z(a)?Hi(e):a?Vt(e):ji(e),Ot(e)||(e._d=null),e))}function ji(e){var t=e._i;E(t)?e._d=new Date(f.now()):Le(t)?e._d=new Date(t.valueOf()):typeof t=="string"?Li(e):Z(t)?(e._a=oa(t.slice(0),function(a){return parseInt(a,10)}),Ut(e)):pe(t)?Ui(e):oe(t)?e._d=new Date(t):f.createFromInputFallback(e)}function Ta(e,t,a,s,i){var r={};return(t===!0||t===!1)&&(s=t,t=void 0),(a===!0||a===!1)&&(s=a,a=void 0),(pe(e)&&bt(e)||Z(e)&&e.length===0)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=a,r._i=e,r._f=t,r._strict=s,Vi(r)}function C(e,t,a,s){return Ta(e,t,a,s,!1)}var Gi=j("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=C.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:tt()}),Bi=j("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=C.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:tt()});function Na(e,t){var a,s;if(t.length===1&&Z(t[0])&&(t=t[0]),!t.length)return C();for(a=t[0],s=1;s<t.length;++s)(!t[s].isValid()||t[s][e](a))&&(a=t[s]);return a}function zi(){var e=[].slice.call(arguments,0);return Na("isBefore",e)}function Zi(){var e=[].slice.call(arguments,0);return Na("isAfter",e)}var qi=function(){return Date.now?Date.now():+new Date},Pe=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ji(e){var t,a=!1,s,i=Pe.length;for(t in e)if(w(e,t)&&!(W.call(Pe,t)!==-1&&(e[t]==null||!isNaN(e[t]))))return!1;for(s=0;s<i;++s)if(e[Pe[s]]){if(a)return!1;parseFloat(e[Pe[s]])!==_(e[Pe[s]])&&(a=!0)}return!0}function Qi(){return this._isValid}function Xi(){return J(NaN)}function ht(e){var t=Tt(e),a=t.year||0,s=t.quarter||0,i=t.month||0,r=t.week||t.isoWeek||0,n=t.day||0,l=t.hour||0,h=t.minute||0,d=t.second||0,u=t.millisecond||0;this._isValid=Ji(t),this._milliseconds=+u+d*1e3+h*6e4+l*1e3*60*60,this._days=+n+r*7,this._months=+i+s*3+a*12,this._data={},this._locale=he(),this._bubble()}function Ze(e){return e instanceof ht}function Yt(e){return e<0?Math.round(-1*e)*-1:Math.round(e)}function Ki(e,t,a){var s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0,n;for(n=0;n<s;n++)_(e[n])!==_(t[n])&&r++;return r+i}function Wa(e,t){m(e,0,0,function(){var a=this.utcOffset(),s="+";return a<0&&(a=-a,s="-"),s+K(~~(a/60),2)+t+K(~~a%60,2)})}Wa("Z",":");Wa("ZZ","");c("Z",nt);c("ZZ",nt);M(["Z","ZZ"],function(e,t,a){a._useUTC=!0,a._tzm=jt(nt,e)});var $i=/([\+\-]|\d\d)/gi;function jt(e,t){var a=(t||"").match(e),s,i,r;return a===null?null:(s=a[a.length-1]||[],i=(s+"").match($i)||["-",0,0],r=+(i[1]*60)+_(i[2]),r===0?0:i[0]==="+"?r:-r)}function Gt(e,t){var a,s;return t._isUTC?(a=t.clone(),s=(q(e)||Le(e)?e.valueOf():C(e).valueOf())-a.valueOf(),a._d.setTime(a._d.valueOf()+s),f.updateOffset(a,!1),a):C(e).local()}function Mt(e){return-Math.round(e._d.getTimezoneOffset())}f.updateOffset=function(){};function er(e,t,a){var s=this._offset||0,i;if(!this.isValid())return e!=null?this:NaN;if(e!=null){if(typeof e=="string"){if(e=jt(nt,e),e===null)return this}else Math.abs(e)<16&&!a&&(e=e*60);return!this._isUTC&&t&&(i=Mt(this)),this._offset=e,this._isUTC=!0,i!=null&&this.add(i,"m"),s!==e&&(!t||this._changeInProgress?La(this,J(e-s,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,f.updateOffset(this,!0),this._changeInProgress=null)),this}else return this._isUTC?s:Mt(this)}function tr(e,t){return e!=null?(typeof e!="string"&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()}function ar(e){return this.utcOffset(0,e)}function sr(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Mt(this),"m")),this}function ir(){if(this._tzm!=null)this.utcOffset(this._tzm,!1,!0);else if(typeof this._i=="string"){var e=jt(Ys,this._i);e!=null?this.utcOffset(e):this.utcOffset(0,!0)}return this}function rr(e){return this.isValid()?(e=e?C(e).utcOffset():0,(this.utcOffset()-e)%60===0):!1}function nr(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function lr(){if(!E(this._isDSTShifted))return this._isDSTShifted;var e={},t;return Ct(e,this),e=Pa(e),e._a?(t=e._isUTC?ee(e._a):C(e._a),this._isDSTShifted=this.isValid()&&Ki(e._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function or(){return this.isValid()?!this._isUTC:!1}function hr(){return this.isValid()?this._isUTC:!1}function Ia(){return this.isValid()?this._isUTC&&this._offset===0:!1}var dr=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,fr=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function J(e,t){var a=e,s=null,i,r,n;return Ze(e)?a={ms:e._milliseconds,d:e._days,M:e._months}:oe(e)||!isNaN(+e)?(a={},t?a[t]=+e:a.milliseconds=+e):(s=dr.exec(e))?(i=s[1]==="-"?-1:1,a={y:0,d:_(s[X])*i,h:_(s[I])*i,m:_(s[z])*i,s:_(s[re])*i,ms:_(Yt(s[ye]*1e3))*i}):(s=fr.exec(e))?(i=s[1]==="-"?-1:1,a={y:me(s[2],i),M:me(s[3],i),w:me(s[4],i),d:me(s[5],i),h:me(s[6],i),m:me(s[7],i),s:me(s[8],i)}):a==null?a={}:typeof a=="object"&&("from"in a||"to"in a)&&(n=cr(C(a.from),C(a.to)),a={},a.ms=n.milliseconds,a.M=n.months),r=new ht(a),Ze(e)&&w(e,"_locale")&&(r._locale=e._locale),Ze(e)&&w(e,"_isValid")&&(r._isValid=e._isValid),r}J.fn=ht.prototype;J.invalid=Xi;function me(e,t){var a=e&&parseFloat(e.replace(",","."));return(isNaN(a)?0:a)*t}function ia(e,t){var a={};return a.months=t.month()-e.month()+(t.year()-e.year())*12,e.clone().add(a.months,"M").isAfter(t)&&--a.months,a.milliseconds=+t-+e.clone().add(a.months,"M"),a}function cr(e,t){var a;return e.isValid()&&t.isValid()?(t=Gt(t,e),e.isBefore(t)?a=ia(e,t):(a=ia(t,e),a.milliseconds=-a.milliseconds,a.months=-a.months),a):{milliseconds:0,months:0}}function Ra(e,t){return function(a,s){var i,r;return s!==null&&!isNaN(+s)&&(da(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),r=a,a=s,s=r),i=J(a,s),La(this,i,e),this}}function La(e,t,a,s){var i=t._milliseconds,r=Yt(t._days),n=Yt(t._months);e.isValid()&&(s=s??!0,n&&_a(e,Ne(e,"Month")+n*a),r&&Da(e,"Date",Ne(e,"Date")+r*a),i&&e._d.setTime(e._d.valueOf()+i*a),s&&f.updateOffset(e,r||n))}var ur=Ra(1,"add"),mr=Ra(-1,"subtract");function Fa(e){return typeof e=="string"||e instanceof String}function yr(e){return q(e)||Le(e)||Fa(e)||oe(e)||Dr(e)||pr(e)||e===null||e===void 0}function pr(e){var t=pe(e)&&!bt(e),a=!1,s=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],i,r,n=s.length;for(i=0;i<n;i+=1)r=s[i],a=a||w(e,r);return t&&a}function Dr(e){var t=Z(e),a=!1;return t&&(a=e.filter(function(s){return!oe(s)&&Fa(e)}).length===0),t&&a}function gr(e){var t=pe(e)&&!bt(e),a=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i,r;for(i=0;i<s.length;i+=1)r=s[i],a=a||w(e,r);return t&&a}function kr(e,t){var a=e.diff(t,"days",!0);return a<-6?"sameElse":a<-1?"lastWeek":a<0?"lastDay":a<1?"sameDay":a<2?"nextDay":a<7?"nextWeek":"sameElse"}function _r(e,t){arguments.length===1&&(arguments[0]?yr(arguments[0])?(e=arguments[0],t=void 0):gr(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var a=e||C(),s=Gt(a,this).startOf("day"),i=f.calendarFormat(this,s)||"sameElse",r=t&&(te(t[i])?t[i].call(this,a):t[i]);return this.format(r||this.localeData().calendar(i,this,C(a)))}function vr(){return new Fe(this)}function wr(e,t){var a=q(e)?e:C(e);return this.isValid()&&a.isValid()?(t=G(t)||"millisecond",t==="millisecond"?this.valueOf()>a.valueOf():a.valueOf()<this.clone().startOf(t).valueOf()):!1}function Yr(e,t){var a=q(e)?e:C(e);return this.isValid()&&a.isValid()?(t=G(t)||"millisecond",t==="millisecond"?this.valueOf()<a.valueOf():this.clone().endOf(t).valueOf()<a.valueOf()):!1}function Mr(e,t,a,s){var i=q(e)?e:C(e),r=q(t)?t:C(t);return this.isValid()&&i.isValid()&&r.isValid()?(s=s||"()",(s[0]==="("?this.isAfter(i,a):!this.isBefore(i,a))&&(s[1]===")"?this.isBefore(r,a):!this.isAfter(r,a))):!1}function Sr(e,t){var a=q(e)?e:C(e),s;return this.isValid()&&a.isValid()?(t=G(t)||"millisecond",t==="millisecond"?this.valueOf()===a.valueOf():(s=a.valueOf(),this.clone().startOf(t).valueOf()<=s&&s<=this.clone().endOf(t).valueOf())):!1}function br(e,t){return this.isSame(e,t)||this.isAfter(e,t)}function Or(e,t){return this.isSame(e,t)||this.isBefore(e,t)}function Cr(e,t,a){var s,i,r;if(!this.isValid())return NaN;if(s=Gt(e,this),!s.isValid())return NaN;switch(i=(s.utcOffset()-this.utcOffset())*6e4,t=G(t),t){case"year":r=qe(this,s)/12;break;case"month":r=qe(this,s);break;case"quarter":r=qe(this,s)/3;break;case"second":r=(this-s)/1e3;break;case"minute":r=(this-s)/6e4;break;case"hour":r=(this-s)/36e5;break;case"day":r=(this-s-i)/864e5;break;case"week":r=(this-s-i)/6048e5;break;default:r=this-s}return a?r:V(r)}function qe(e,t){if(e.date()<t.date())return-qe(t,e);var a=(t.year()-e.year())*12+(t.month()-e.month()),s=e.clone().add(a,"months"),i,r;return t-s<0?(i=e.clone().add(a-1,"months"),r=(t-s)/(s-i)):(i=e.clone().add(a+1,"months"),r=(t-s)/(i-s)),-(a+r)||0}f.defaultFormat="YYYY-MM-DDTHH:mm:ssZ";f.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function xr(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function Pr(e){if(!this.isValid())return null;var t=e!==!0,a=t?this.clone().utc():this;return a.year()<0||a.year()>9999?ze(a,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):te(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+this.utcOffset()*60*1e3).toISOString().replace("Z",ze(a,"Z")):ze(a,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function Tr(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e="moment",t="",a,s,i,r;return this.isLocal()||(e=this.utcOffset()===0?"moment.utc":"moment.parseZone",t="Z"),a="["+e+'("]',s=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",i="-MM-DD[T]HH:mm:ss.SSS",r=t+'[")]',this.format(a+s+i+r)}function Nr(e){e||(e=this.isUtc()?f.defaultFormatUtc:f.defaultFormat);var t=ze(this,e);return this.localeData().postformat(t)}function Wr(e,t){return this.isValid()&&(q(e)&&e.isValid()||C(e).isValid())?J({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function Ir(e){return this.from(C(),e)}function Rr(e,t){return this.isValid()&&(q(e)&&e.isValid()||C(e).isValid())?J({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function Lr(e){return this.to(C(),e)}function Aa(e){var t;return e===void 0?this._locale._abbr:(t=he(e),t!=null&&(this._locale=t),this)}var Ea=j("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return e===void 0?this.localeData():this.locale(e)});function Ha(){return this._locale}var Ke=1e3,Ye=60*Ke,$e=60*Ye,Ua=(365*400+97)*24*$e;function Me(e,t){return(e%t+t)%t}function Va(e,t,a){return e<100&&e>=0?new Date(e+400,t,a)-Ua:new Date(e,t,a).valueOf()}function ja(e,t,a){return e<100&&e>=0?Date.UTC(e+400,t,a)-Ua:Date.UTC(e,t,a)}function Fr(e){var t,a;if(e=G(e),e===void 0||e==="millisecond"||!this.isValid())return this;switch(a=this._isUTC?ja:Va,e){case"year":t=a(this.year(),0,1);break;case"quarter":t=a(this.year(),this.month()-this.month()%3,1);break;case"month":t=a(this.year(),this.month(),1);break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=a(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=Me(t+(this._isUTC?0:this.utcOffset()*Ye),$e);break;case"minute":t=this._d.valueOf(),t-=Me(t,Ye);break;case"second":t=this._d.valueOf(),t-=Me(t,Ke);break}return this._d.setTime(t),f.updateOffset(this,!0),this}function Ar(e){var t,a;if(e=G(e),e===void 0||e==="millisecond"||!this.isValid())return this;switch(a=this._isUTC?ja:Va,e){case"year":t=a(this.year()+1,0,1)-1;break;case"quarter":t=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=a(this.year(),this.month()+1,1)-1;break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=$e-Me(t+(this._isUTC?0:this.utcOffset()*Ye),$e)-1;break;case"minute":t=this._d.valueOf(),t+=Ye-Me(t,Ye)-1;break;case"second":t=this._d.valueOf(),t+=Ke-Me(t,Ke)-1;break}return this._d.setTime(t),f.updateOffset(this,!0),this}function Er(){return this._d.valueOf()-(this._offset||0)*6e4}function Hr(){return Math.floor(this.valueOf()/1e3)}function Ur(){return new Date(this.valueOf())}function Vr(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function jr(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function Gr(){return this.isValid()?this.toISOString():null}function Br(){return Ot(this)}function zr(){return fe({},g(this))}function Zr(){return g(this).overflow}function qr(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}m("N",0,0,"eraAbbr");m("NN",0,0,"eraAbbr");m("NNN",0,0,"eraAbbr");m("NNNN",0,0,"eraName");m("NNNNN",0,0,"eraNarrow");m("y",["y",1],"yo","eraYear");m("y",["yy",2],0,"eraYear");m("y",["yyy",3],0,"eraYear");m("y",["yyyy",4],0,"eraYear");c("N",Bt);c("NN",Bt);c("NNN",Bt);c("NNNN",nn);c("NNNNN",ln);M(["N","NN","NNN","NNNN","NNNNN"],function(e,t,a,s){var i=a._locale.erasParse(e,s,a._strict);i?g(a).era=i:g(a).invalidEra=e});c("y",Se);c("yy",Se);c("yyy",Se);c("yyyy",Se);c("yo",on);M(["y","yy","yyy","yyyy"],F);M(["yo"],function(e,t,a,s){var i;a._locale._eraYearOrdinalRegex&&(i=e.match(a._locale._eraYearOrdinalRegex)),a._locale.eraYearOrdinalParse?t[F]=a._locale.eraYearOrdinalParse(e,i):t[F]=parseInt(e,10)});function Jr(e,t){var a,s,i,r=this._eras||he("en")._eras;for(a=0,s=r.length;a<s;++a){switch(typeof r[a].since){case"string":i=f(r[a].since).startOf("day"),r[a].since=i.valueOf();break}switch(typeof r[a].until){case"undefined":r[a].until=1/0;break;case"string":i=f(r[a].until).startOf("day").valueOf(),r[a].until=i.valueOf();break}}return r}function Qr(e,t,a){var s,i,r=this.eras(),n,l,h;for(e=e.toUpperCase(),s=0,i=r.length;s<i;++s)if(n=r[s].name.toUpperCase(),l=r[s].abbr.toUpperCase(),h=r[s].narrow.toUpperCase(),a)switch(t){case"N":case"NN":case"NNN":if(l===e)return r[s];break;case"NNNN":if(n===e)return r[s];break;case"NNNNN":if(h===e)return r[s];break}else if([n,l,h].indexOf(e)>=0)return r[s]}function Xr(e,t){var a=e.since<=e.until?1:-1;return t===void 0?f(e.since).year():f(e.since).year()+(t-e.offset)*a}function Kr(){var e,t,a,s=this.localeData().eras();for(e=0,t=s.length;e<t;++e)if(a=this.clone().startOf("day").valueOf(),s[e].since<=a&&a<=s[e].until||s[e].until<=a&&a<=s[e].since)return s[e].name;return""}function $r(){var e,t,a,s=this.localeData().eras();for(e=0,t=s.length;e<t;++e)if(a=this.clone().startOf("day").valueOf(),s[e].since<=a&&a<=s[e].until||s[e].until<=a&&a<=s[e].since)return s[e].narrow;return""}function en(){var e,t,a,s=this.localeData().eras();for(e=0,t=s.length;e<t;++e)if(a=this.clone().startOf("day").valueOf(),s[e].since<=a&&a<=s[e].until||s[e].until<=a&&a<=s[e].since)return s[e].abbr;return""}function tn(){var e,t,a,s,i=this.localeData().eras();for(e=0,t=i.length;e<t;++e)if(a=i[e].since<=i[e].until?1:-1,s=this.clone().startOf("day").valueOf(),i[e].since<=s&&s<=i[e].until||i[e].until<=s&&s<=i[e].since)return(this.year()-f(i[e].since).year())*a+i[e].offset;return this.year()}function an(e){return w(this,"_erasNameRegex")||zt.call(this),e?this._erasNameRegex:this._erasRegex}function sn(e){return w(this,"_erasAbbrRegex")||zt.call(this),e?this._erasAbbrRegex:this._erasRegex}function rn(e){return w(this,"_erasNarrowRegex")||zt.call(this),e?this._erasNarrowRegex:this._erasRegex}function Bt(e,t){return t.erasAbbrRegex(e)}function nn(e,t){return t.erasNameRegex(e)}function ln(e,t){return t.erasNarrowRegex(e)}function on(e,t){return t._eraYearOrdinalRegex||Se}function zt(){var e=[],t=[],a=[],s=[],i,r,n,l,h,d=this.eras();for(i=0,r=d.length;i<r;++i)n=ne(d[i].name),l=ne(d[i].abbr),h=ne(d[i].narrow),t.push(n),e.push(l),a.push(h),s.push(n),s.push(l),s.push(h);this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+t.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+e.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+a.join("|")+")","i")}m(0,["gg",2],0,function(){return this.weekYear()%100});m(0,["GG",2],0,function(){return this.isoWeekYear()%100});function dt(e,t){m(0,[e,e.length],0,t)}dt("gggg","weekYear");dt("ggggg","weekYear");dt("GGGG","isoWeekYear");dt("GGGGG","isoWeekYear");c("G",rt);c("g",rt);c("GG",x,H);c("gg",x,H);c("GGGG",Wt,Nt);c("gggg",Wt,Nt);c("GGGGG",it,at);c("ggggg",it,at);Ee(["gggg","ggggg","GGGG","GGGGG"],function(e,t,a,s){t[s.substr(0,2)]=_(e)});Ee(["gg","GG"],function(e,t,a,s){t[s]=f.parseTwoDigitYear(e)});function hn(e){return Ga.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function dn(e){return Ga.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)}function fn(){return le(this.year(),1,4)}function cn(){return le(this.isoWeekYear(),1,4)}function un(){var e=this.localeData()._week;return le(this.year(),e.dow,e.doy)}function mn(){var e=this.localeData()._week;return le(this.weekYear(),e.dow,e.doy)}function Ga(e,t,a,s,i){var r;return e==null?Ie(this,s,i).year:(r=le(e,s,i),t>r&&(t=r),yn.call(this,e,t,a,s,i))}function yn(e,t,a,s,i){var r=Ya(e,t,a,s,i),n=We(r.year,0,r.dayOfYear);return this.year(n.getUTCFullYear()),this.month(n.getUTCMonth()),this.date(n.getUTCDate()),this}m("Q",0,"Qo","quarter");c("Q",ca);M("Q",function(e,t){t[ie]=(_(e)-1)*3});function pn(e){return e==null?Math.ceil((this.month()+1)/3):this.month((e-1)*3+this.month()%3)}m("D",["DD",2],"Do","date");c("D",x,be);c("DD",x,H);c("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient});M(["D","DD"],X);M("Do",function(e,t){t[X]=_(e.match(x)[0])});var Ba=Oe("Date",!0);m("DDD",["DDDD",3],"DDDo","dayOfYear");c("DDD",st);c("DDDD",ua);M(["DDD","DDDD"],function(e,t,a){a._dayOfYear=_(e)});function Dn(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return e==null?t:this.add(e-t,"d")}m("m",["mm",2],0,"minute");c("m",x,It);c("mm",x,H);M(["m","mm"],z);var gn=Oe("Minutes",!1);m("s",["ss",2],0,"second");c("s",x,It);c("ss",x,H);M(["s","ss"],re);var kn=Oe("Seconds",!1);m("S",0,0,function(){return~~(this.millisecond()/100)});m(0,["SS",2],0,function(){return~~(this.millisecond()/10)});m(0,["SSS",3],0,"millisecond");m(0,["SSSS",4],0,function(){return this.millisecond()*10});m(0,["SSSSS",5],0,function(){return this.millisecond()*100});m(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3});m(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4});m(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5});m(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6});c("S",st,ca);c("SS",st,H);c("SSS",st,ua);var ce,za;for(ce="SSSS";ce.length<=9;ce+="S")c(ce,Se);function _n(e,t){t[ye]=_(("0."+e)*1e3)}for(ce="S";ce.length<=9;ce+="S")M(ce,_n);za=Oe("Milliseconds",!1);m("z",0,0,"zoneAbbr");m("zz",0,0,"zoneName");function vn(){return this._isUTC?"UTC":""}function wn(){return this._isUTC?"Coordinated Universal Time":""}var o=Fe.prototype;o.add=ur;o.calendar=_r;o.clone=vr;o.diff=Cr;o.endOf=Ar;o.format=Nr;o.from=Wr;o.fromNow=Ir;o.to=Rr;o.toNow=Lr;o.get=Ts;o.invalidAt=Zr;o.isAfter=wr;o.isBefore=Yr;o.isBetween=Mr;o.isSame=Sr;o.isSameOrAfter=br;o.isSameOrBefore=Or;o.isValid=Br;o.lang=Ea;o.locale=Aa;o.localeData=Ha;o.max=Bi;o.min=Gi;o.parsingFlags=zr;o.set=Ns;o.startOf=Fr;o.subtract=mr;o.toArray=Vr;o.toObject=jr;o.toDate=Ur;o.toISOString=Pr;o.inspect=Tr;typeof Symbol<"u"&&Symbol.for!=null&&(o[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"});o.toJSON=Gr;o.toString=xr;o.unix=Hr;o.valueOf=Er;o.creationData=qr;o.eraName=Kr;o.eraNarrow=$r;o.eraAbbr=en;o.eraYear=tn;o.year=pa;o.isLeapYear=Ps;o.weekYear=hn;o.isoWeekYear=dn;o.quarter=o.quarters=pn;o.month=va;o.daysInMonth=Us;o.week=o.weeks=Js;o.isoWeek=o.isoWeeks=Qs;o.weeksInYear=un;o.weeksInWeekYear=mn;o.isoWeeksInYear=fn;o.isoWeeksInISOWeekYear=cn;o.date=Ba;o.day=o.days=hi;o.weekday=di;o.isoWeekday=fi;o.dayOfYear=Dn;o.hour=o.hours=gi;o.minute=o.minutes=gn;o.second=o.seconds=kn;o.millisecond=o.milliseconds=za;o.utcOffset=er;o.utc=ar;o.local=sr;o.parseZone=ir;o.hasAlignedHourOffset=rr;o.isDST=nr;o.isLocal=or;o.isUtcOffset=hr;o.isUtc=Ia;o.isUTC=Ia;o.zoneAbbr=vn;o.zoneName=wn;o.dates=j("dates accessor is deprecated. Use date instead.",Ba);o.months=j("months accessor is deprecated. Use month instead",va);o.years=j("years accessor is deprecated. Use year instead",pa);o.zone=j("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",tr);o.isDSTShifted=j("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",lr);function Yn(e){return C(e*1e3)}function Mn(){return C.apply(null,arguments).parseZone()}function Za(e){return e}var Y=xt.prototype;Y.calendar=os;Y.longDateFormat=cs;Y.invalidDate=ms;Y.ordinal=Ds;Y.preparse=Za;Y.postformat=Za;Y.relativeTime=ks;Y.pastFuture=_s;Y.set=ns;Y.eras=Jr;Y.erasParse=Qr;Y.erasConvertYear=Xr;Y.erasAbbrRegex=sn;Y.erasNameRegex=an;Y.erasNarrowRegex=rn;Y.months=Fs;Y.monthsShort=As;Y.monthsParse=Hs;Y.monthsRegex=js;Y.monthsShortRegex=Vs;Y.week=Bs;Y.firstDayOfYear=qs;Y.firstDayOfWeek=Zs;Y.weekdays=ii;Y.weekdaysMin=ni;Y.weekdaysShort=ri;Y.weekdaysParse=oi;Y.weekdaysRegex=ci;Y.weekdaysShortRegex=ui;Y.weekdaysMinRegex=mi;Y.isPM=pi;Y.meridiem=ki;function et(e,t,a,s){var i=he(),r=ee().set(s,t);return i[a](r,e)}function qa(e,t,a){if(oe(e)&&(t=e,e=void 0),e=e||"",t!=null)return et(e,t,a,"month");var s,i=[];for(s=0;s<12;s++)i[s]=et(e,s,a,"month");return i}function Zt(e,t,a,s){typeof e=="boolean"?(oe(t)&&(a=t,t=void 0),t=t||""):(t=e,a=t,e=!1,oe(t)&&(a=t,t=void 0),t=t||"");var i=he(),r=e?i._week.dow:0,n,l=[];if(a!=null)return et(t,(a+r)%7,s,"day");for(n=0;n<7;n++)l[n]=et(t,(n+r)%7,s,"day");return l}function Sn(e,t){return qa(e,t,"months")}function bn(e,t){return qa(e,t,"monthsShort")}function On(e,t,a){return Zt(e,t,a,"weekdays")}function Cn(e,t,a){return Zt(e,t,a,"weekdaysShort")}function xn(e,t,a){return Zt(e,t,a,"weekdaysMin")}ue("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,a=_(e%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return e+a}});f.lang=j("moment.lang is deprecated. Use moment.locale instead.",ue);f.langData=j("moment.langData is deprecated. Use moment.localeData instead.",he);var ae=Math.abs;function Pn(){var e=this._data;return this._milliseconds=ae(this._milliseconds),this._days=ae(this._days),this._months=ae(this._months),e.milliseconds=ae(e.milliseconds),e.seconds=ae(e.seconds),e.minutes=ae(e.minutes),e.hours=ae(e.hours),e.months=ae(e.months),e.years=ae(e.years),this}function Ja(e,t,a,s){var i=J(t,a);return e._milliseconds+=s*i._milliseconds,e._days+=s*i._days,e._months+=s*i._months,e._bubble()}function Tn(e,t){return Ja(this,e,t,1)}function Nn(e,t){return Ja(this,e,t,-1)}function ra(e){return e<0?Math.floor(e):Math.ceil(e)}function Wn(){var e=this._milliseconds,t=this._days,a=this._months,s=this._data,i,r,n,l,h;return e>=0&&t>=0&&a>=0||e<=0&&t<=0&&a<=0||(e+=ra(St(a)+t)*864e5,t=0,a=0),s.milliseconds=e%1e3,i=V(e/1e3),s.seconds=i%60,r=V(i/60),s.minutes=r%60,n=V(r/60),s.hours=n%24,t+=V(n/24),h=V(Qa(t)),a+=h,t-=ra(St(h)),l=V(a/12),a%=12,s.days=t,s.months=a,s.years=l,this}function Qa(e){return e*4800/146097}function St(e){return e*146097/4800}function In(e){if(!this.isValid())return NaN;var t,a,s=this._milliseconds;if(e=G(e),e==="month"||e==="quarter"||e==="year")switch(t=this._days+s/864e5,a=this._months+Qa(t),e){case"month":return a;case"quarter":return a/3;case"year":return a/12}else switch(t=this._days+Math.round(St(this._months)),e){case"week":return t/7+s/6048e5;case"day":return t+s/864e5;case"hour":return t*24+s/36e5;case"minute":return t*1440+s/6e4;case"second":return t*86400+s/1e3;case"millisecond":return Math.floor(t*864e5)+s;default:throw new Error("Unknown unit "+e)}}function de(e){return function(){return this.as(e)}}var Xa=de("ms"),Rn=de("s"),Ln=de("m"),Fn=de("h"),An=de("d"),En=de("w"),Hn=de("M"),Un=de("Q"),Vn=de("y"),jn=Xa;function Gn(){return J(this)}function Bn(e){return e=G(e),this.isValid()?this[e+"s"]():NaN}function De(e){return function(){return this.isValid()?this._data[e]:NaN}}var zn=De("milliseconds"),Zn=De("seconds"),qn=De("minutes"),Jn=De("hours"),Qn=De("days"),Xn=De("months"),Kn=De("years");function $n(){return V(this.days()/7)}var se=Math.round,ve={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function el(e,t,a,s,i){return i.relativeTime(t||1,!!a,e,s)}function tl(e,t,a,s){var i=J(e).abs(),r=se(i.as("s")),n=se(i.as("m")),l=se(i.as("h")),h=se(i.as("d")),d=se(i.as("M")),u=se(i.as("w")),D=se(i.as("y")),k=r<=a.ss&&["s",r]||r<a.s&&["ss",r]||n<=1&&["m"]||n<a.m&&["mm",n]||l<=1&&["h"]||l<a.h&&["hh",l]||h<=1&&["d"]||h<a.d&&["dd",h];return a.w!=null&&(k=k||u<=1&&["w"]||u<a.w&&["ww",u]),k=k||d<=1&&["M"]||d<a.M&&["MM",d]||D<=1&&["y"]||["yy",D],k[2]=t,k[3]=+e>0,k[4]=s,el.apply(null,k)}function al(e){return e===void 0?se:typeof e=="function"?(se=e,!0):!1}function sl(e,t){return ve[e]===void 0?!1:t===void 0?ve[e]:(ve[e]=t,e==="s"&&(ve.ss=t-1),!0)}function il(e,t){if(!this.isValid())return this.localeData().invalidDate();var a=!1,s=ve,i,r;return typeof e=="object"&&(t=e,e=!1),typeof e=="boolean"&&(a=e),typeof t=="object"&&(s=Object.assign({},ve,t),t.s!=null&&t.ss==null&&(s.ss=t.s-1)),i=this.localeData(),r=tl(this,!a,s,i),a&&(r=i.pastFuture(+this,r)),i.postformat(r)}var gt=Math.abs;function ke(e){return(e>0)-(e<0)||+e}function ft(){if(!this.isValid())return this.localeData().invalidDate();var e=gt(this._milliseconds)/1e3,t=gt(this._days),a=gt(this._months),s,i,r,n,l=this.asSeconds(),h,d,u,D;return l?(s=V(e/60),i=V(s/60),e%=60,s%=60,r=V(a/12),a%=12,n=e?e.toFixed(3).replace(/\.?0+$/,""):"",h=l<0?"-":"",d=ke(this._months)!==ke(l)?"-":"",u=ke(this._days)!==ke(l)?"-":"",D=ke(this._milliseconds)!==ke(l)?"-":"",h+"P"+(r?d+r+"Y":"")+(a?d+a+"M":"")+(t?u+t+"D":"")+(i||s||e?"T":"")+(i?D+i+"H":"")+(s?D+s+"M":"")+(e?D+n+"S":"")):"P0D"}var v=ht.prototype;v.isValid=Qi;v.abs=Pn;v.add=Tn;v.subtract=Nn;v.as=In;v.asMilliseconds=Xa;v.asSeconds=Rn;v.asMinutes=Ln;v.asHours=Fn;v.asDays=An;v.asWeeks=En;v.asMonths=Hn;v.asQuarters=Un;v.asYears=Vn;v.valueOf=jn;v._bubble=Wn;v.clone=Gn;v.get=Bn;v.milliseconds=zn;v.seconds=Zn;v.minutes=qn;v.hours=Jn;v.days=Qn;v.weeks=$n;v.months=Xn;v.years=Kn;v.humanize=il;v.toISOString=ft;v.toString=ft;v.toJSON=ft;v.locale=Aa;v.localeData=Ha;v.toIsoString=j("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",ft);v.lang=Ea;m("X",0,0,"unix");m("x",0,0,"valueOf");c("x",rt);c("X",Ms);M("X",function(e,t,a){a._d=new Date(parseFloat(e)*1e3)});M("x",function(e,t,a){a._d=new Date(_(e))});//! moment.js
f.version="2.30.1";is(C);f.fn=o;f.min=zi;f.max=Zi;f.now=qi;f.utc=ee;f.unix=Yn;f.months=Sn;f.isDate=Le;f.locale=ue;f.invalid=tt;f.duration=J;f.isMoment=q;f.weekdays=On;f.parseZone=Mn;f.localeData=he;f.isDuration=Ze;f.monthsShort=bn;f.weekdaysMin=xn;f.defineLocale=Et;f.updateLocale=Yi;f.locales=Mi;f.weekdaysShort=Cn;f.normalizeUnits=G;f.relativeTimeRounding=al;f.relativeTimeThreshold=sl;f.calendarFormat=kr;f.prototype=o;f.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"};const rl=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"})),nl=ss(rl);/**
* @version: 3.1
* @author: Dan Grossman http://www.dangrossman.info/
* @copyright: Copyright (c) 2012-2019 Dan Grossman. All rights reserved.
* @license: Licensed under the MIT license. See http://www.opensource.org/licenses/mit-license.php
* @website: http://www.daterangepicker.com/
*/var ll=Be.exports,na;function ol(){return na||(na=1,function(e){(function(t,a){if(e.exports){var s=typeof window<"u"?window.jQuery:void 0;s||(s=as(),s.fn||(s.fn={}));var i=typeof window<"u"&&typeof window.moment<"u"?window.moment:nl;e.exports=a(i,s)}else t.daterangepicker=a(t.moment,t.jQuery)})(ll,function(t,a){var s=function(i,r,n){if(this.parentEl="body",this.element=a(i),this.startDate=t().startOf("day"),this.endDate=t().endOf("day"),this.minDate=!1,this.maxDate=!1,this.maxSpan=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.minYear=t().subtract(100,"year").format("YYYY"),this.maxYear=t().add(100,"year").format("YYYY"),this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyButtonClasses="btn-primary",this.cancelButtonClasses="btn-default",this.locale={direction:"ltr",format:t.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:t.weekdaysMin(),monthNames:t.monthsShort(),firstDay:t.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},(typeof r!="object"||r===null)&&(r={}),r=a.extend(this.element.data(),r),typeof r.template!="string"&&!(r.template instanceof a)&&(r.template='<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>'),this.parentEl=r.parentEl&&a(r.parentEl).length?a(r.parentEl):a(this.parentEl),this.container=a(r.template).appendTo(this.parentEl),typeof r.locale=="object"&&(typeof r.locale.direction=="string"&&(this.locale.direction=r.locale.direction),typeof r.locale.format=="string"&&(this.locale.format=r.locale.format),typeof r.locale.separator=="string"&&(this.locale.separator=r.locale.separator),typeof r.locale.daysOfWeek=="object"&&(this.locale.daysOfWeek=r.locale.daysOfWeek.slice()),typeof r.locale.monthNames=="object"&&(this.locale.monthNames=r.locale.monthNames.slice()),typeof r.locale.firstDay=="number"&&(this.locale.firstDay=r.locale.firstDay),typeof r.locale.applyLabel=="string"&&(this.locale.applyLabel=r.locale.applyLabel),typeof r.locale.cancelLabel=="string"&&(this.locale.cancelLabel=r.locale.cancelLabel),typeof r.locale.weekLabel=="string"&&(this.locale.weekLabel=r.locale.weekLabel),typeof r.locale.customRangeLabel=="string")){var l=document.createElement("textarea");l.innerHTML=r.locale.customRangeLabel;var h=l.value;this.locale.customRangeLabel=h}if(this.container.addClass(this.locale.direction),typeof r.startDate=="string"&&(this.startDate=t(r.startDate,this.locale.format)),typeof r.endDate=="string"&&(this.endDate=t(r.endDate,this.locale.format)),typeof r.minDate=="string"&&(this.minDate=t(r.minDate,this.locale.format)),typeof r.maxDate=="string"&&(this.maxDate=t(r.maxDate,this.locale.format)),typeof r.startDate=="object"&&(this.startDate=t(r.startDate)),typeof r.endDate=="object"&&(this.endDate=t(r.endDate)),typeof r.minDate=="object"&&(this.minDate=t(r.minDate)),typeof r.maxDate=="object"&&(this.maxDate=t(r.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),typeof r.applyButtonClasses=="string"&&(this.applyButtonClasses=r.applyButtonClasses),typeof r.applyClass=="string"&&(this.applyButtonClasses=r.applyClass),typeof r.cancelButtonClasses=="string"&&(this.cancelButtonClasses=r.cancelButtonClasses),typeof r.cancelClass=="string"&&(this.cancelButtonClasses=r.cancelClass),typeof r.maxSpan=="object"&&(this.maxSpan=r.maxSpan),typeof r.dateLimit=="object"&&(this.maxSpan=r.dateLimit),typeof r.opens=="string"&&(this.opens=r.opens),typeof r.drops=="string"&&(this.drops=r.drops),typeof r.showWeekNumbers=="boolean"&&(this.showWeekNumbers=r.showWeekNumbers),typeof r.showISOWeekNumbers=="boolean"&&(this.showISOWeekNumbers=r.showISOWeekNumbers),typeof r.buttonClasses=="string"&&(this.buttonClasses=r.buttonClasses),typeof r.buttonClasses=="object"&&(this.buttonClasses=r.buttonClasses.join(" ")),typeof r.showDropdowns=="boolean"&&(this.showDropdowns=r.showDropdowns),typeof r.minYear=="number"&&(this.minYear=r.minYear),typeof r.maxYear=="number"&&(this.maxYear=r.maxYear),typeof r.showCustomRangeLabel=="boolean"&&(this.showCustomRangeLabel=r.showCustomRangeLabel),typeof r.singleDatePicker=="boolean"&&(this.singleDatePicker=r.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),typeof r.timePicker=="boolean"&&(this.timePicker=r.timePicker),typeof r.timePickerSeconds=="boolean"&&(this.timePickerSeconds=r.timePickerSeconds),typeof r.timePickerIncrement=="number"&&(this.timePickerIncrement=r.timePickerIncrement),typeof r.timePicker24Hour=="boolean"&&(this.timePicker24Hour=r.timePicker24Hour),typeof r.autoApply=="boolean"&&(this.autoApply=r.autoApply),typeof r.autoUpdateInput=="boolean"&&(this.autoUpdateInput=r.autoUpdateInput),typeof r.linkedCalendars=="boolean"&&(this.linkedCalendars=r.linkedCalendars),typeof r.isInvalidDate=="function"&&(this.isInvalidDate=r.isInvalidDate),typeof r.isCustomDate=="function"&&(this.isCustomDate=r.isCustomDate),typeof r.alwaysShowCalendars=="boolean"&&(this.alwaysShowCalendars=r.alwaysShowCalendars),this.locale.firstDay!=0)for(var d=this.locale.firstDay;d>0;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),d--;var u,D,k;if(typeof r.startDate>"u"&&typeof r.endDate>"u"&&a(this.element).is(":text")){var p=a(this.element).val(),L=p.split(this.locale.separator);u=D=null,L.length==2?(u=t(L[0],this.locale.format),D=t(L[1],this.locale.format)):this.singleDatePicker&&p!==""&&(u=t(p,this.locale.format),D=t(p,this.locale.format)),u!==null&&D!==null&&(this.setStartDate(u),this.setEndDate(D))}if(typeof r.ranges=="object"){for(k in r.ranges){typeof r.ranges[k][0]=="string"?u=t(r.ranges[k][0],this.locale.format):u=t(r.ranges[k][0]),typeof r.ranges[k][1]=="string"?D=t(r.ranges[k][1],this.locale.format):D=t(r.ranges[k][1]),this.minDate&&u.isBefore(this.minDate)&&(u=this.minDate.clone());var P=this.maxDate;if(this.maxSpan&&P&&u.clone().add(this.maxSpan).isAfter(P)&&(P=u.clone().add(this.maxSpan)),P&&D.isAfter(P)&&(D=P.clone()),!(this.minDate&&D.isBefore(this.minDate,this.timepicker?"minute":"day")||P&&u.isAfter(P,this.timepicker?"minute":"day"))){var l=document.createElement("textarea");l.innerHTML=k;var h=l.value;this.ranges[h]=[u,D]}}var O="<ul>";for(k in this.ranges)O+='<li data-range-key="'+k+'">'+k+"</li>";this.showCustomRangeLabel&&(O+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),O+="</ul>",this.container.find(".ranges").prepend(O)}typeof n=="function"&&(this.callback=n),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&this.container.addClass("auto-apply"),typeof r.ranges=="object"&&this.container.addClass("show-ranges"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".drp-calendar.left").addClass("single"),this.container.find(".drp-calendar.left").show(),this.container.find(".drp-calendar.right").hide(),!this.timePicker&&this.autoApply&&this.container.addClass("auto-apply")),(typeof r.ranges>"u"&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyButtonClasses.length&&this.container.find(".applyBtn").addClass(this.applyButtonClasses),this.cancelButtonClasses.length&&this.container.find(".cancelBtn").addClass(this.cancelButtonClasses),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".drp-calendar").on("click.daterangepicker",".prev",a.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",a.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",a.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",a.proxy(this.hoverDate,this)).on("change.daterangepicker","select.yearselect",a.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",a.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",a.proxy(this.timeChanged,this)),this.container.find(".ranges").on("click.daterangepicker","li",a.proxy(this.clickRange,this)),this.container.find(".drp-buttons").on("click.daterangepicker","button.applyBtn",a.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",a.proxy(this.clickCancel,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":a.proxy(this.show,this),"focus.daterangepicker":a.proxy(this.show,this),"keyup.daterangepicker":a.proxy(this.elementChanged,this),"keydown.daterangepicker":a.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",a.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",a.proxy(this.toggle,this))),this.updateElement()};return s.prototype={constructor:s,setStartDate:function(i){typeof i=="string"&&(this.startDate=t(i,this.locale.format)),typeof i=="object"&&(this.startDate=t(i)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(i){typeof i=="string"&&(this.endDate=t(i,this.locale.format)),typeof i=="object"&&(this.endDate=t(i)),this.timePicker||(this.endDate=this.endDate.endOf("day")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.maxSpan&&this.startDate.clone().add(this.maxSpan).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.maxSpan)),this.previousRightTime=this.endDate.clone(),this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").prop("disabled",!1).removeClass("disabled"):this.container.find(".right .calendar-time select").prop("disabled",!0).addClass("disabled")),this.endDate&&this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),!this.linkedCalendars&&(this.endDate.month()!=this.startDate.month()||this.endDate.year()!=this.startDate.year())?this.rightCalendar.month=this.endDate.clone().date(2):this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month")}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){if(this.timePicker){var i,r,n;if(this.endDate){if(i=parseInt(this.container.find(".left .hourselect").val(),10),r=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(r)&&(r=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),n=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,!this.timePicker24Hour){var l=this.container.find(".left .ampmselect").val();l==="PM"&&i<12&&(i+=12),l==="AM"&&i===12&&(i=0)}}else if(i=parseInt(this.container.find(".right .hourselect").val(),10),r=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(r)&&(r=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),n=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,!this.timePicker24Hour){var l=this.container.find(".right .ampmselect").val();l==="PM"&&i<12&&(i+=12),l==="AM"&&i===12&&(i=0)}this.leftCalendar.month.hour(i).minute(r).second(n),this.rightCalendar.month.hour(i).minute(r).second(n)}this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),this.endDate!=null&&this.calculateChosenLabel()},renderCalendar:function(i){var y=i=="left"?this.leftCalendar:this.rightCalendar,r=y.month.month(),n=y.month.year(),l=y.month.hour(),h=y.month.minute(),d=y.month.second(),u=t([n,r]).daysInMonth(),D=t([n,r,1]),k=t([n,r,u]),p=t(D).subtract(1,"month").month(),L=t(D).subtract(1,"month").year(),P=t([L,p]).daysInMonth(),O=D.day(),y=[];y.firstDay=D,y.lastDay=k;for(var R=0;R<6;R++)y[R]=[];var U=P-O+this.locale.firstDay+1;U>P&&(U-=7),O==this.locale.firstDay&&(U=P-6);for(var He=t([L,p,U,12,h,d]),S,b,R=0,S=0,b=0;R<42;R++,S++,He=t(He).add(24,"hour"))R>0&&S%7===0&&(S=0,b++),y[b][S]=He.clone().hour(l).minute(h).second(d),He.hour(12),this.minDate&&y[b][S].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&y[b][S].isBefore(this.minDate)&&i=="left"&&(y[b][S]=this.minDate.clone()),this.maxDate&&y[b][S].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&y[b][S].isAfter(this.maxDate)&&i=="right"&&(y[b][S]=this.maxDate.clone());i=="left"?this.leftCalendar.calendar=y:this.rightCalendar.calendar=y;var ge=i=="left"?this.minDate:this.startDate,B=this.maxDate;i=="left"?this.startDate:this.endDate,this.locale.direction=="ltr";var N='<table class="table-condensed">';N+="<thead>",N+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(N+="<th></th>"),(!ge||ge.isBefore(y.firstDay))&&(!this.linkedCalendars||i=="left")?N+='<th class="prev available"><span></span></th>':N+="<th></th>";var qt=this.locale.monthNames[y[1][1].month()]+y[1][1].format(" YYYY");if(this.showDropdowns){for(var Jt=y[1][1].month(),ct=y[1][1].year(),Qt=B&&B.year()||this.maxYear,Xt=ge&&ge.year()||this.minYear,Ka=ct==Xt,$a=ct==Qt,Ue='<select class="monthselect">',Q=0;Q<12;Q++)(!Ka||ge&&Q>=ge.month())&&(!$a||B&&Q<=B.month())?Ue+="<option value='"+Q+"'"+(Q===Jt?" selected='selected'":"")+">"+this.locale.monthNames[Q]+"</option>":Ue+="<option value='"+Q+"'"+(Q===Jt?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[Q]+"</option>";Ue+="</select>";for(var ut='<select class="yearselect">',Ce=Xt;Ce<=Qt;Ce++)ut+='<option value="'+Ce+'"'+(Ce===ct?' selected="selected"':"")+">"+Ce+"</option>";ut+="</select>",qt=Ue+ut}if(N+='<th colspan="5" class="month">'+qt+"</th>",(!B||B.isAfter(y.lastDay))&&(!this.linkedCalendars||i=="right"||this.singleDatePicker)?N+='<th class="next available"><span></span></th>':N+="<th></th>",N+="</tr>",N+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(N+='<th class="week">'+this.locale.weekLabel+"</th>"),a.each(this.locale.daysOfWeek,function(fl,es){N+="<th>"+es+"</th>"}),N+="</tr>",N+="</thead>",N+="<tbody>",this.endDate==null&&this.maxSpan){var Kt=this.startDate.clone().add(this.maxSpan).endOf("day");(!B||Kt.isBefore(B))&&(B=Kt)}for(var b=0;b<6;b++){N+="<tr>",this.showWeekNumbers?N+='<td class="week">'+y[b][0].week()+"</td>":this.showISOWeekNumbers&&(N+='<td class="week">'+y[b][0].isoWeek()+"</td>");for(var S=0;S<7;S++){var A=[];y[b][S].isSame(new Date,"day")&&A.push("today"),y[b][S].isoWeekday()>5&&A.push("weekend"),y[b][S].month()!=y[1][1].month()&&A.push("off","ends"),this.minDate&&y[b][S].isBefore(this.minDate,"day")&&A.push("off","disabled"),B&&y[b][S].isAfter(B,"day")&&A.push("off","disabled"),this.isInvalidDate(y[b][S])&&A.push("off","disabled"),y[b][S].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&A.push("active","start-date"),this.endDate!=null&&y[b][S].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&A.push("active","end-date"),this.endDate!=null&&y[b][S]>this.startDate&&y[b][S]<this.endDate&&A.push("in-range");var Ve=this.isCustomDate(y[b][S]);Ve!==!1&&(typeof Ve=="string"?A.push(Ve):Array.prototype.push.apply(A,Ve));for(var mt="",$t=!1,R=0;R<A.length;R++)mt+=A[R]+" ",A[R]=="disabled"&&($t=!0);$t||(mt+="available"),N+='<td class="'+mt.replace(/^\s+|\s+$/g,"")+'" data-title="r'+b+"c"+S+'">'+y[b][S].date()+"</td>"}N+="</tr>"}N+="</tbody>",N+="</table>",this.container.find(".drp-calendar."+i+" .calendar-table").html(N)},renderTimePicker:function(i){if(!(i=="right"&&!this.endDate)){var r,n,l,h=this.maxDate;if(this.maxSpan&&(!this.maxDate||this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate))&&(h=this.startDate.clone().add(this.maxSpan)),i=="left")n=this.startDate.clone(),l=this.minDate;else if(i=="right"){n=this.endDate.clone(),l=this.startDate;var d=this.container.find(".drp-calendar.right .calendar-time");if(d.html()!=""&&(n.hour(isNaN(n.hour())?d.find(".hourselect option:selected").val():n.hour()),n.minute(isNaN(n.minute())?d.find(".minuteselect option:selected").val():n.minute()),n.second(isNaN(n.second())?d.find(".secondselect option:selected").val():n.second()),!this.timePicker24Hour)){var u=d.find(".ampmselect option:selected").val();u==="PM"&&n.hour()<12&&n.hour(n.hour()+12),u==="AM"&&n.hour()===12&&n.hour(0)}n.isBefore(this.startDate)&&(n=this.startDate.clone()),h&&n.isAfter(h)&&(n=h.clone())}r='<select class="hourselect">';for(var D=this.timePicker24Hour?0:1,k=this.timePicker24Hour?23:12,p=D;p<=k;p++){var L=p;this.timePicker24Hour||(L=n.hour()>=12?p==12?12:p+12:p==12?0:p);var P=n.clone().hour(L),O=!1;l&&P.minute(59).isBefore(l)&&(O=!0),h&&P.minute(0).isAfter(h)&&(O=!0),L==n.hour()&&!O?r+='<option value="'+p+'" selected="selected">'+p+"</option>":O?r+='<option value="'+p+'" disabled="disabled" class="disabled">'+p+"</option>":r+='<option value="'+p+'">'+p+"</option>"}r+="</select> ",r+=': <select class="minuteselect">';for(var p=0;p<60;p+=this.timePickerIncrement){var y=p<10?"0"+p:p,P=n.clone().minute(p),O=!1;l&&P.second(59).isBefore(l)&&(O=!0),h&&P.second(0).isAfter(h)&&(O=!0),n.minute()==p&&!O?r+='<option value="'+p+'" selected="selected">'+y+"</option>":O?r+='<option value="'+p+'" disabled="disabled" class="disabled">'+y+"</option>":r+='<option value="'+p+'">'+y+"</option>"}if(r+="</select> ",this.timePickerSeconds){r+=': <select class="secondselect">';for(var p=0;p<60;p++){var y=p<10?"0"+p:p,P=n.clone().second(p),O=!1;l&&P.isBefore(l)&&(O=!0),h&&P.isAfter(h)&&(O=!0),n.second()==p&&!O?r+='<option value="'+p+'" selected="selected">'+y+"</option>":O?r+='<option value="'+p+'" disabled="disabled" class="disabled">'+y+"</option>":r+='<option value="'+p+'">'+y+"</option>"}r+="</select> "}if(!this.timePicker24Hour){r+='<select class="ampmselect">';var R="",U="";l&&n.clone().hour(12).minute(0).second(0).isBefore(l)&&(R=' disabled="disabled" class="disabled"'),h&&n.clone().hour(0).minute(0).second(0).isAfter(h)&&(U=' disabled="disabled" class="disabled"'),n.hour()>=12?r+='<option value="AM"'+R+'>AM</option><option value="PM" selected="selected"'+U+">PM</option>":r+='<option value="AM" selected="selected"'+R+'>AM</option><option value="PM"'+U+">PM</option>",r+="</select>"}this.container.find(".drp-calendar."+i+" .calendar-time").html(r)}},updateFormInputs:function(){this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").prop("disabled",!1):this.container.find("button.applyBtn").prop("disabled",!0)},move:function(){var i={top:0,left:0},r,n=this.drops,l=a(window).width();switch(this.parentEl.is("body")||(i={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},l=this.parentEl[0].clientWidth+this.parentEl.offset().left),n){case"auto":r=this.element.offset().top+this.element.outerHeight()-i.top,r+this.container.outerHeight()>=this.parentEl[0].scrollHeight&&(r=this.element.offset().top-this.container.outerHeight()-i.top,n="up");break;case"up":r=this.element.offset().top-this.container.outerHeight()-i.top;break;default:r=this.element.offset().top+this.element.outerHeight()-i.top;break}this.container.css({top:0,left:0,right:"auto"});var h=this.container.outerWidth();if(this.container.toggleClass("drop-up",n=="up"),this.opens=="left"){var d=l-this.element.offset().left-this.element.outerWidth();h+d>a(window).width()?this.container.css({top:r,right:"auto",left:9}):this.container.css({top:r,right:d,left:"auto"})}else if(this.opens=="center"){var u=this.element.offset().left-i.left+this.element.outerWidth()/2-h/2;u<0?this.container.css({top:r,right:"auto",left:9}):u+h>a(window).width()?this.container.css({top:r,left:"auto",right:0}):this.container.css({top:r,left:u,right:"auto"})}else{var u=this.element.offset().left-i.left;u+h>a(window).width()?this.container.css({top:r,left:"auto",right:0}):this.container.css({top:r,left:u,right:"auto"})}},show:function(i){this.isShowing||(this._outsideClickProxy=a.proxy(function(r){this.outsideClick(r)},this),a(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),a(window).on("resize.daterangepicker",a.proxy(function(r){this.move(r)},this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(i){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),(!this.startDate.isSame(this.oldStartDate)||!this.endDate.isSame(this.oldEndDate))&&this.callback(this.startDate.clone(),this.endDate.clone(),this.chosenLabel),this.updateElement(),a(document).off(".daterangepicker"),a(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(i){this.isShowing?this.hide():this.show()},outsideClick:function(i){var r=a(i.target);i.type=="focusin"||r.closest(this.element).length||r.closest(this.container).length||r.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},clickRange:function(i){var r=i.target.getAttribute("data-range-key");if(this.chosenLabel=r,r==this.locale.customRangeLabel)this.showCalendars();else{var n=this.ranges[r];this.startDate=n[0],this.endDate=n[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply()}},clickPrev:function(i){var r=a(i.target).parents(".drp-calendar");r.hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(i){var r=a(i.target).parents(".drp-calendar");r.hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(i){if(a(i.target).hasClass("available")){var r=a(i.target).attr("data-title"),n=r.substr(1,1),l=r.substr(3,1),h=a(i.target).parents(".drp-calendar"),d=h.hasClass("left")?this.leftCalendar.calendar[n][l]:this.rightCalendar.calendar[n][l],u=this.leftCalendar,D=this.rightCalendar,k=this.startDate;this.endDate||this.container.find(".drp-calendar tbody td").each(function(p,L){if(!a(L).hasClass("week")){var P=a(L).attr("data-title"),O=P.substr(1,1),y=P.substr(3,1),R=a(L).parents(".drp-calendar"),U=R.hasClass("left")?u.calendar[O][y]:D.calendar[O][y];U.isAfter(k)&&U.isBefore(d)||U.isSame(d,"day")?a(L).addClass("in-range"):a(L).removeClass("in-range")}})}},clickDate:function(i){if(a(i.target).hasClass("available")){var r=a(i.target).attr("data-title"),n=r.substr(1,1),l=r.substr(3,1),h=a(i.target).parents(".drp-calendar"),d=h.hasClass("left")?this.leftCalendar.calendar[n][l]:this.rightCalendar.calendar[n][l];if(this.endDate||d.isBefore(this.startDate,"day")){if(this.timePicker){var u=parseInt(this.container.find(".left .hourselect").val(),10);if(!this.timePicker24Hour){var D=this.container.find(".left .ampmselect").val();D==="PM"&&u<12&&(u+=12),D==="AM"&&u===12&&(u=0)}var k=parseInt(this.container.find(".left .minuteselect").val(),10);isNaN(k)&&(k=parseInt(this.container.find(".left .minuteselect option:last").val(),10));var p=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0;d=d.clone().hour(u).minute(k).second(p)}this.endDate=null,this.setStartDate(d.clone())}else if(!this.endDate&&d.isBefore(this.startDate))this.setEndDate(this.startDate.clone());else{if(this.timePicker){var u=parseInt(this.container.find(".right .hourselect").val(),10);if(!this.timePicker24Hour){var D=this.container.find(".right .ampmselect").val();D==="PM"&&u<12&&(u+=12),D==="AM"&&u===12&&(u=0)}var k=parseInt(this.container.find(".right .minuteselect").val(),10);isNaN(k)&&(k=parseInt(this.container.find(".right .minuteselect option:last").val(),10));var p=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0;d=d.clone().hour(u).minute(k).second(p)}this.setEndDate(d.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())}this.singleDatePicker&&(this.setEndDate(this.startDate),!this.timePicker&&this.autoApply&&this.clickApply()),this.updateView(),i.stopPropagation()}},calculateChosenLabel:function(){var i=!0,r=0;for(var n in this.ranges){if(this.timePicker){var l=this.timePickerSeconds?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD HH:mm";if(this.startDate.format(l)==this.ranges[n][0].format(l)&&this.endDate.format(l)==this.ranges[n][1].format(l)){i=!1,this.chosenLabel=this.container.find(".ranges li:eq("+r+")").addClass("active").attr("data-range-key");break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[n][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[n][1].format("YYYY-MM-DD")){i=!1,this.chosenLabel=this.container.find(".ranges li:eq("+r+")").addClass("active").attr("data-range-key");break}r++}i&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").attr("data-range-key"):this.chosenLabel=null,this.showCalendars())},clickApply:function(i){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(i){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(i){var r=a(i.target).closest(".drp-calendar").hasClass("left"),n=r?"left":"right",l=this.container.find(".drp-calendar."+n),h=parseInt(l.find(".monthselect").val(),10),d=l.find(".yearselect").val();r||(d<this.startDate.year()||d==this.startDate.year()&&h<this.startDate.month())&&(h=this.startDate.month(),d=this.startDate.year()),this.minDate&&(d<this.minDate.year()||d==this.minDate.year()&&h<this.minDate.month())&&(h=this.minDate.month(),d=this.minDate.year()),this.maxDate&&(d>this.maxDate.year()||d==this.maxDate.year()&&h>this.maxDate.month())&&(h=this.maxDate.month(),d=this.maxDate.year()),r?(this.leftCalendar.month.month(h).year(d),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(h).year(d),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(i){var r=a(i.target).closest(".drp-calendar"),n=r.hasClass("left"),l=parseInt(r.find(".hourselect").val(),10),h=parseInt(r.find(".minuteselect").val(),10);isNaN(h)&&(h=parseInt(r.find(".minuteselect option:last").val(),10));var d=this.timePickerSeconds?parseInt(r.find(".secondselect").val(),10):0;if(!this.timePicker24Hour){var u=r.find(".ampmselect").val();u==="PM"&&l<12&&(l+=12),u==="AM"&&l===12&&(l=0)}if(n){var D=this.startDate.clone();D.hour(l),D.minute(h),D.second(d),this.setStartDate(D),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==D.format("YYYY-MM-DD")&&this.endDate.isBefore(D)&&this.setEndDate(D.clone())}else if(this.endDate){var k=this.endDate.clone();k.hour(l),k.minute(h),k.second(d),this.setEndDate(k)}this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},elementChanged:function(){if(this.element.is("input")&&this.element.val().length){var i=this.element.val().split(this.locale.separator),r=null,n=null;i.length===2&&(r=t(i[0],this.locale.format),n=t(i[1],this.locale.format)),(this.singleDatePicker||r===null||n===null)&&(r=t(this.element.val(),this.locale.format),n=r),!(!r.isValid()||!n.isValid())&&(this.setStartDate(r),this.setEndDate(n),this.updateView())}},keydown:function(i){(i.keyCode===9||i.keyCode===13)&&this.hide(),i.keyCode===27&&(i.preventDefault(),i.stopPropagation(),this.hide())},updateElement:function(){if(this.element.is("input")&&this.autoUpdateInput){var i=this.startDate.format(this.locale.format);this.singleDatePicker||(i+=this.locale.separator+this.endDate.format(this.locale.format)),i!==this.element.val()&&this.element.val(i).trigger("change")}},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},a.fn.daterangepicker=function(i,r){var n=a.extend(!0,{},a.fn.daterangepicker.defaultOptions,i);return this.each(function(){var l=a(this);l.data("daterangepicker")&&l.data("daterangepicker").remove(),l.data("daterangepicker",new s(l,n,r))}),this},s})}(Be)),Be.exports}ol();const hl=$.fn.daterangepicker;$.fn.daterangepicker=function(e,t){return hl.call(this,e,t),e&&(e.showWeekNumbers||e.showISOWeekNumbers)&&this.each(function(){const a=$(this).data("daterangepicker");a&&a.container&&a.container.addClass("with-week-numbers")}),this}});export default dl();
