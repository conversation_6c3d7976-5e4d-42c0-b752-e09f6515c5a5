document.addEventListener("DOMContentLoaded",function(){(function(){const o=document.querySelector(".email-list"),C=[].slice.call(document.querySelectorAll(".email-list-item")),s=[].slice.call(document.querySelectorAll(".email-list-item-input")),d=document.querySelector(".app-email-view-content"),m=document.querySelector(".email-filters"),u=[].slice.call(document.querySelectorAll(".email-filter-folders li")),w=document.querySelector(".email-editor"),A=document.querySelector(".app-email-sidebar"),x=document.querySelector(".app-overlay"),H=document.querySelector(".email-reply-editor"),p=[].slice.call(document.querySelectorAll(".email-list-item-bookmark")),r=document.getElementById("email-select-all"),f=document.querySelector(".email-search-input"),v=document.querySelector(".email-compose-toggle-cc"),g=document.querySelector(".email-compose-toggle-bcc"),P=document.querySelector(".app-email-compose"),h=document.querySelector(".email-list-delete"),S=document.querySelector(".email-list-read"),y=document.querySelector(".email-list-empty"),k=document.querySelector(".email-refresh"),T=document.getElementById("app-email-view"),L=[].slice.call(document.querySelectorAll(".email-filter-folders li")),q=[].slice.call(document.querySelectorAll(".email-list-item-actions li"));if(o&&new PerfectScrollbar(o,{wheelPropagation:!1,suppressScrollX:!0}),m&&new PerfectScrollbar(m,{wheelPropagation:!1,suppressScrollX:!0}),d&&new PerfectScrollbar(d,{wheelPropagation:!1,suppressScrollX:!0}),w&&new Quill(".email-editor",{modules:{toolbar:".email-editor-toolbar"},placeholder:"Message ",theme:"snow"}),H&&new Quill(".email-reply-editor",{modules:{toolbar:".email-reply-toolbar"},placeholder:"Write your message... ",theme:"snow"}),p&&p.forEach(t=>{t.addEventListener("click",l=>{let e=l.currentTarget.parentNode.parentNode,a=e.getAttribute("data-starred");l.stopPropagation(),a?e.removeAttribute("data-starred"):e.setAttribute("data-starred","true")})}),r&&r.addEventListener("click",t=>{t.currentTarget.checked?s.forEach(l=>l.checked=1):s.forEach(l=>l.checked=0)}),s&&s.forEach(t=>{t.addEventListener("click",l=>{l.stopPropagation();let e=0;s.forEach(a=>{a.checked&&e++}),e<s.length?e==0?r.indeterminate=!1:r.indeterminate=!0:e==s.length?(r.indeterminate=!1,r.checked=!0):r.indeterminate=!1})}),f&&f.addEventListener("keyup",t=>{let l=t.currentTarget.value.toLowerCase(),e={},a=document.querySelector(".email-filter-folders .active").getAttribute("data-target");a!="inbox"?e=[].slice.call(document.querySelectorAll(".email-list-item[data-"+a+'="true"]')):e=[].slice.call(document.querySelectorAll(".email-list-item")),e.forEach(i=>{let _=i.textContent.toLowerCase();l?-1<_.indexOf(l)?i.classList.add("d-block"):i.classList.add("d-none"):i.classList.remove("d-none")})}),u.forEach(t=>{t.addEventListener("click",l=>{let e=l.currentTarget,a=e.getAttribute("data-target");A.classList.remove("show"),x.classList.remove("show"),Helpers._removeClass("active",u),e.classList.add("active"),C.forEach(i=>{a=="inbox"||i.hasAttribute("data-"+a)?(i.classList.add("d-block"),i.classList.remove("d-none")):(i.classList.add("d-none"),i.classList.remove("d-block"))})})}),g&&g.addEventListener("click",t=>{Helpers._toggleClass(document.querySelector(".email-compose-bcc"),"d-block","d-none")}),v&&v.addEventListener("click",t=>{Helpers._toggleClass(document.querySelector(".email-compose-cc"),"d-block","d-none")}),P.addEventListener("hidden.bs.modal",t=>{document.querySelector(".email-editor .ql-editor").innerHTML="",$("#emailContacts").val(""),E()}),h&&h.addEventListener("click",t=>{s.forEach(e=>{e.checked&&e.parentNode.closest("li.email-list-item").remove()}),r.indeterminate=!1,r.checked=!1;var l=document.querySelectorAll(".email-list-item");l.length==0&&y.classList.remove("d-none")}),S&&S.addEventListener("click",t=>{s.forEach(l=>{if(l.checked){l.checked=!1,l.parentNode.closest("li.email-list-item").classList.add("email-marked-read");let e=l.parentNode.closest("li.email-list-item").querySelector(".email-list-item-actions li");Helpers._hasClass("email-read",e)&&(e.classList.remove("email-read"),e.classList.add("email-unread"),e.querySelector("i").classList.remove("ti-mail-opened"),e.querySelector("i").classList.add("ti-mail"))}}),r.indeterminate=!1,r.checked=!1}),k&&o){let t=$(".email-list"),l=new PerfectScrollbar(o,{wheelPropagation:!1,suppressScrollX:!0});k.addEventListener("click",e=>{t.block({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#000",opacity:.1},onBlock:function(){l.settings.suppressScrollY=!0},onUnblock:function(){l.settings.suppressScrollY=!1}})})}let b=$(".email-earlier-msgs");b.length&&b.on("click",function(){let t=$(this);t.parents().find(".email-card-last").addClass("hide-pseudo"),t.next(".email-card-prev").slideToggle(),t.remove()});let n=$("#emailContacts");function E(){if(n.length){let l=function(e){return e.id?"<div class='d-flex flex-wrap align-items-center'><div class='avatar avatar-xs me-2 w-px-20 h-px-20'><img src='"+assetsPath+"img/avatars/"+$(e.element).data("avatar")+"' alt='avatar' class='rounded-circle' /></div>"+e.text+"</div>":e.text};var t=l;n.wrap('<div class="position-relative"></div>').select2({placeholder:"Select value",dropdownParent:n.parent(),closeOnSelect:!1,templateResult:l,templateSelection:l,escapeMarkup:function(e){return e}})}}E();let c=$(".app-email-view-content");c.find(".scroll-to-reply").on("click",function(){c[0].scrollTop===0&&c.animate({scrollTop:c[0].scrollHeight},1500)}),L&&L.forEach(t=>{t.addEventListener("click",l=>{T.classList.remove("show")})}),q&&q.forEach(t=>{t.addEventListener("click",l=>{l.stopPropagation();let e=l.currentTarget;if(Helpers._hasClass("email-delete",e)){e.parentNode.closest("li.email-list-item").remove();var a=document.querySelectorAll(".email-list-item");a.length==0&&y.classList.remove("d-none")}else Helpers._hasClass("email-read",e)?(e.parentNode.closest("li.email-list-item").classList.add("email-marked-read"),Helpers._toggleClass(e,"email-read","email-unread"),Helpers._toggleClass(e.querySelector("i"),"ti-mail-opened","ti-mail")):Helpers._hasClass("email-unread",e)&&(e.parentNode.closest("li.email-list-item").classList.remove("email-marked-read"),Helpers._toggleClass(e,"email-read","email-unread"),Helpers._toggleClass(e.querySelector("i"),"ti-mail-opened","ti-mail"))})})})()});
