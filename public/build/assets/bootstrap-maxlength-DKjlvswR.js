var b={},p;function O(){return p||(p=1,function(i){i.event.special.destroyed||(i.event.special.destroyed={remove:function(l){l.handler&&l.handler()}}),i.fn.extend({maxlength:function(l,d){var x=i("body"),y={showOnReady:!1,alwaysShow:!0,threshold:0,warningClass:"small form-text text-muted",limitReachedClass:"small form-text text-danger",limitExceededClass:"",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom-right-inside",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,customMaxAttribute:null,customMaxClass:"overmax",allowOverMax:!1,zIndex:1099};i.isFunction(l)&&!d&&(d=l,l={}),l=i.extend(y,l);function o(a){var t=a.charCodeAt();return t?t<128?1:t<2048?2:3:0}function g(a){return a.split("").map(o).concat(0).reduce(function(t,e){return t+e})}function v(a){var t=a.val();l.twoCharLinebreak?t=t.replace(/\r(?!\n)|\n(?!\r)/g,`\r
`):t=t.replace(/(?:\r\n|\r|\n)/g,`
`);var e=0;return l.utf8?e=g(t):e=t.length,a.prop("type")==="file"&&a.val()!==""&&(e-=12),e}function M(a,t){var e=a.val();if(l.twoCharLinebreak&&(e=e.replace(/\r(?!\n)|\n(?!\r)/g,`\r
`),e[e.length-1]===`
`&&(t-=e.length%2)),l.utf8){for(var s=e.split("").map(o),r=0,c=g(e)-t;r<c;r+=s.pop());t-=t-s.length}a.val(e.substr(0,t))}function k(a,t,e){var s=!0;return!l.alwaysShow&&e-v(a)>t&&(s=!1),s}function m(a,t){var e=t-v(a);return e}function h(a,t){t.css({display:"block"}),a.trigger("maxlength.shown")}function R(a,t){l.alwaysShow||(t.css({display:"none"}),a.trigger("maxlength.hidden"))}function C(a,t,e){var s="";return l.message?typeof l.message=="function"?s=l.message(a,t):s=l.message.replace("%charsTyped%",e).replace("%charsRemaining%",t-e).replace("%charsTotal%",t):(l.preText&&(s+=l.preText),l.showCharsTyped?s+=e:s+=t-e,l.showMaxLength&&(s+=l.separator+t),l.postText&&(s+=l.postText)),s}function w(a,t,e,s){s&&(s.html(C(t.val(),e,e-a)),a>0?k(t,l.threshold,e)?h(t,s.removeClass(l.limitReachedClass+" "+l.limitExceededClass).addClass(l.warningClass)):R(t,s):l.limitExceededClass?a===0?h(t,s.removeClass(l.warningClass+" "+l.limitExceededClass).addClass(l.limitReachedClass)):h(t,s.removeClass(l.warningClass+" "+l.limitReachedClass).addClass(l.limitExceededClass)):h(t,s.removeClass(l.warningClass).addClass(l.limitReachedClass))),l.customMaxAttribute&&(a<0?t.addClass(l.customMaxClass):t.removeClass(l.customMaxClass))}function T(a){var t=a[0];return i.extend({},typeof t.getBoundingClientRect=="function"?t.getBoundingClientRect():{width:t.offsetWidth,height:t.offsetHeight},a.offset())}function z(a,t){if(!(!a||!t)){var e=["top","bottom","left","right","position"],s={};i.each(e,function(r,c){var f=l.placement[c];typeof f<"u"&&(s[c]=f)}),t.css(s)}}function u(a,t){var e=T(a);if(i.type(l.placement)==="function"){l.placement(a,t,e);return}if(i.isPlainObject(l.placement)){z(l.placement,t);return}var s=a.outerWidth(),r=t.outerWidth(),c=t.width(),f=t.height();switch(l.appendToParent&&(e.top-=a.parent().offset().top,e.left-=a.parent().offset().left),l.placement){case"bottom":t.css({top:e.top+e.height,left:e.left+e.width/2-c/2});break;case"top":t.css({top:e.top-f,left:e.left+e.width/2-c/2});break;case"left":t.css({top:e.top+e.height/2-f/2,left:e.left-c});break;case"right":t.css({top:e.top+e.height/2-f/2,left:e.left+e.width});break;case"bottom-right":t.css({top:e.top+e.height,left:e.left+e.width});break;case"top-right":t.css({top:e.top-f,left:e.left+s});break;case"top-left":t.css({top:e.top-f,left:e.left-r});break;case"bottom-left":t.css({top:e.top+a.outerHeight(),left:e.left-r});break;case"centered-right":t.css({top:e.top+f/2,left:e.left+s-r-3});break;case"bottom-right-inside":t.css({top:e.top+e.height,left:e.left+e.width-r});break;case"top-right-inside":t.css({top:e.top-f,left:e.left+s-r});break;case"top-left-inside":t.css({top:e.top-f,left:e.left});break;case"bottom-left-inside":t.css({top:e.top+a.outerHeight(),left:e.left});break}}function n(a){var t=a.attr("maxlength")||l.customMaxAttribute;if(l.customMaxAttribute&&!l.allowOverMax){var e=a.attr(l.customMaxAttribute);(!t||e<t)&&(t=e)}return t||(t=a.attr("size")),t}return this.each(function(){var a=i(this),t,e;i(window).resize(function(){e&&u(a,e)});function s(){var r=C(a.val(),t,"0");t=n(a),e||(e=i('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:l.zIndex}).html(r)),a.is("textarea")&&(a.data("maxlenghtsizex",a.outerWidth()),a.data("maxlenghtsizey",a.outerHeight()),a.mouseup(function(){(a.outerWidth()!==a.data("maxlenghtsizex")||a.outerHeight()!==a.data("maxlenghtsizey"))&&u(a,e),a.data("maxlenghtsizex",a.outerWidth()),a.data("maxlenghtsizey",a.outerHeight())})),l.appendToParent?(a.parent().append(e),a.parent().css("position","relative")):x.append(e);var c=m(a,n(a));w(c,a,t,e),u(a,e)}l.showOnReady?a.ready(function(){s()}):a.focus(function(){s()}),a.on("maxlength.reposition",function(){u(a,e)}),a.on("destroyed",function(){e&&e.remove()}),a.on("blur",function(){e&&!l.showOnReady&&e.remove()}),a.on("input",function(){var r=n(a),c=m(a,r),f=!0;return l.validate&&c<0?(M(a,r),f=!1):w(c,a,t,e),f})})}})}(jQuery)),b}O();
