var xo=Object.defineProperty;var _o=(i,t,e)=>t in i?xo(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var M=(i,t,e)=>_o(i,typeof t!="symbol"?t+"":t,e);/*!
 * @kurkle/color v0.3.2
 * https://github.com/kurkle/color#readme
 * (c) 2023 <PERSON><PERSON>
 * Released under the MIT License
 */function ye(i){return i+.5|0}const xt=(i,t,e)=>Math.max(Math.min(i,e),t);function ee(i){return xt(ye(i*2.55),0,255)}function Mt(i){return xt(ye(i*255),0,255)}function ft(i){return xt(ye(i/2.55)/100,0,1)}function Zi(i){return xt(ye(i*100),0,100)}const it={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},xi=[..."0123456789ABCDEF"],yo=i=>xi[i&15],vo=i=>xi[(i&240)>>4]+xi[i&15],we=i=>(i&240)>>4===(i&15),Mo=i=>we(i.r)&&we(i.g)&&we(i.b)&&we(i.a);function ko(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&it[i[1]]*17,g:255&it[i[2]]*17,b:255&it[i[3]]*17,a:t===5?it[i[4]]*17:255}:(t===7||t===9)&&(e={r:it[i[1]]<<4|it[i[2]],g:it[i[3]]<<4|it[i[4]],b:it[i[5]]<<4|it[i[6]],a:t===9?it[i[7]]<<4|it[i[8]]:255})),e}const wo=(i,t)=>i<255?t(i):"";function So(i){var t=Mo(i)?yo:vo;return i?"#"+t(i.r)+t(i.g)+t(i.b)+wo(i.a,t):void 0}const Po=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function vn(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function Do(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Oo(i,t,e){const s=vn(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function Co(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Ei(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Co(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function Fi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(Mt)}function Ii(i,t,e){return Fi(vn,i,t,e)}function Ao(i,t,e){return Fi(Oo,i,t,e)}function To(i,t,e){return Fi(Do,i,t,e)}function Mn(i){return(i%360+360)%360}function Lo(i){const t=Po.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?ee(+t[5]):Mt(+t[5]));const n=Mn(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=Ao(n,o,r):t[1]==="hsv"?s=To(n,o,r):s=Ii(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function Ro(i,t){var e=Ei(i);e[0]=Mn(e[0]+t),e=Ii(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Eo(i){if(!i)return;const t=Ei(i),e=t[0],s=Zi(t[1]),n=Zi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${ft(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const Ji={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Qi={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Fo(){const i={},t=Object.keys(Qi),e=Object.keys(Ji);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,Ji[o]);o=parseInt(Qi[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let Se;function Io(i){Se||(Se=Fo(),Se.transparent=[0,0,0,0]);const t=Se[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const zo=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Bo(i){const t=zo.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?ee(r):xt(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?ee(s):xt(s,0,255)),n=255&(t[4]?ee(n):xt(n,0,255)),o=255&(t[6]?ee(o):xt(o,0,255)),{r:s,g:n,b:o,a:e}}}function Vo(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${ft(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const ri=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Ht=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Wo(i,t,e){const s=Ht(ft(i.r)),n=Ht(ft(i.g)),o=Ht(ft(i.b));return{r:Mt(ri(s+e*(Ht(ft(t.r))-s))),g:Mt(ri(n+e*(Ht(ft(t.g))-n))),b:Mt(ri(o+e*(Ht(ft(t.b))-o))),a:i.a+e*(t.a-i.a)}}function Pe(i,t,e){if(i){let s=Ei(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=Ii(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function kn(i,t){return i&&Object.assign(t||{},i)}function ts(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=Mt(i[3]))):(t=kn(i,{r:0,g:0,b:0,a:1}),t.a=Mt(t.a)),t}function No(i){return i.charAt(0)==="r"?Bo(i):Lo(i)}class ue{constructor(t){if(t instanceof ue)return t;const e=typeof t;let s;e==="object"?s=ts(t):e==="string"&&(s=ko(t)||Io(t)||No(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=kn(this._rgb);return t&&(t.a=ft(t.a)),t}set rgb(t){this._rgb=ts(t)}rgbString(){return this._valid?Vo(this._rgb):void 0}hexString(){return this._valid?So(this._rgb):void 0}hslString(){return this._valid?Eo(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=Wo(this._rgb,t._rgb,e)),this}clone(){return new ue(this.rgb)}alpha(t){return this._rgb.a=Mt(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ye(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Pe(this._rgb,2,t),this}darken(t){return Pe(this._rgb,2,-t),this}saturate(t){return Pe(this._rgb,1,t),this}desaturate(t){return Pe(this._rgb,1,-t),this}rotate(t){return Ro(this._rgb,t),this}}/*!
 * Chart.js v4.4.3
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */function ht(){}const Ho=(()=>{let i=0;return()=>i++})();function L(i){return i===null||typeof i>"u"}function V(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function A(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function W(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function tt(i,t){return W(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const jo=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,wn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function F(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function R(i,t,e,s){let n,o,r;if(V(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(A(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function Ue(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Ke(i){if(V(i))return i.map(Ke);if(A(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=Ke(i[e[n]]);return t}return i}function Sn(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function $o(i,t,e,s){if(!Sn(i))return;const n=t[i],o=e[i];A(n)&&A(o)?fe(n,o,s):t[i]=Ke(o)}function fe(i,t,e){const s=V(t)?t:[t],n=s.length;if(!A(i))return i;e=e||{};const o=e.merger||$o;let r;for(let a=0;a<n;++a){if(r=s[a],!A(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function ae(i,t){return fe(i,t,{merger:Yo})}function Yo(i,t,e){if(!Sn(i))return;const s=t[i],n=e[i];A(s)&&A(n)?ae(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Ke(n))}const es={"":i=>i,x:i=>i.x,y:i=>i.y};function Xo(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Uo(i){const t=Xo(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function kt(i,t){return(es[t]||(es[t]=Uo(t)))(i)}function zi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const ge=i=>typeof i<"u",wt=i=>typeof i=="function",is=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Ko(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const z=Math.PI,I=2*z,qo=I+z,qe=Number.POSITIVE_INFINITY,Go=z/180,j=z/2,Ot=z/4,ss=z*2/3,_t=Math.log10,ct=Math.sign;function le(i,t,e){return Math.abs(i-t)<e}function ns(i){const t=Math.round(i);i=le(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(_t(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Zo(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Yt(i){return!isNaN(parseFloat(i))&&isFinite(i)}function Jo(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Pn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function nt(i){return i*(z/180)}function Bi(i){return i*(180/z)}function os(i){if(!W(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Dn(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*z&&(o+=I),{angle:o,distance:n}}function _i(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Qo(i,t){return(i-t+qo)%I-z}function et(i){return(i%I+I)%I}function pe(i,t,e,s){const n=et(i),o=et(t),r=et(e),a=et(o-n),l=et(r-n),c=et(n-o),h=et(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function Y(i,t,e){return Math.max(t,Math.min(e,i))}function tr(i){return Y(i,-32768,32767)}function pt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Vi(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const mt=(i,t,e,s)=>Vi(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),er=(i,t,e)=>Vi(i,e,s=>i[s][t]>=e);function ir(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const On=["push","pop","shift","splice","unshift"];function sr(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),On.forEach(e=>{const s="_onData"+zi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function rs(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(On.forEach(o=>{delete i[o]}),delete i._chartjs)}function Cn(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const An=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Tn(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,An.call(window,()=>{s=!1,i.apply(t,e)}))}}function nr(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const Wi=i=>i==="start"?"left":i==="end"?"right":"center",K=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,or=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function Ln(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:r,_parsed:a}=i,l=r.axis,{min:c,max:h,minDefined:d,maxDefined:u}=r.getUserBounds();d&&(n=Y(Math.min(mt(a,l,c).lo,e?s:mt(t,l,r.getPixelForValue(c)).lo),0,s-1)),u?o=Y(Math.max(mt(a,r.axis,h,!0).hi+1,e?0:mt(t,l,r.getPixelForValue(h),!0).hi+1),n,s)-n:o=s-n}return{start:n,count:o}}function Rn(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const De=i=>i===0||i===1,as=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*I/e)),ls=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*I/e)+1,ce={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*j)+1,easeOutSine:i=>Math.sin(i*j),easeInOutSine:i=>-.5*(Math.cos(z*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>De(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>De(i)?i:as(i,.075,.3),easeOutElastic:i=>De(i)?i:ls(i,.075,.3),easeInOutElastic(i){return De(i)?i:i<.5?.5*as(i*2,.1125,.45):.5+.5*ls(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-ce.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?ce.easeInBounce(i*2)*.5:ce.easeOutBounce(i*2-1)*.5+.5};function Ni(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function cs(i){return Ni(i)?i:new ue(i)}function ai(i){return Ni(i)?i:new ue(i).saturate(.5).darken(.1).hexString()}const rr=["x","y","borderWidth","radius","tension"],ar=["color","borderColor","backgroundColor"];function lr(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:ar},numbers:{type:"number",properties:rr}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function cr(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const hs=new Map;function hr(i,t){t=t||{};const e=i+JSON.stringify(t);let s=hs.get(e);return s||(s=new Intl.NumberFormat(i,t),hs.set(e,s)),s}function ve(i,t,e){return hr(t,e).format(i)}const En={values(i){return V(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=dr(i,e)}const r=_t(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ve(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(_t(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?En.numeric.call(this,i,t,e):""}};function dr(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var ei={formatters:En};function ur(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ei.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const zt=Object.create(null),yi=Object.create(null);function he(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function li(i,t,e){return typeof t=="string"?fe(he(i,t),e):fe(he(i,""),t)}class fr{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>ai(n.backgroundColor),this.hoverBorderColor=(s,n)=>ai(n.borderColor),this.hoverColor=(s,n)=>ai(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return li(this,t,e)}get(t){return he(this,t)}describe(t,e){return li(yi,t,e)}override(t,e){return li(zt,t,e)}route(t,e,s,n){const o=he(this,t),r=he(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return A(l)?Object.assign({},c,l):D(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var N=new fr({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[lr,cr,ur]);function gr(i){return!i||L(i.size)||L(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Ge(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function pr(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let r=0;const a=e.length;let l,c,h,d,u;for(l=0;l<a;l++)if(d=e[l],d!=null&&!V(d))r=Ge(i,n,o,r,d);else if(V(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!V(u)&&(r=Ge(i,n,o,r,u));i.restore();const f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return r}function Ct(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function ds(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function vi(i,t,e,s){Fn(i,t,e,s,null)}function Fn(i,t,e,s,n){let o,r,a,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Go;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,p,0,0,I):i.arc(e,s,p,0,I),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=ss,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=ss,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+Ot)*l,d=Math.cos(m+Ot)*(n?n/2-c:l),a=Math.sin(m+Ot)*l,u=Math.sin(m+Ot)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-z,m-j),i.arc(e+u,s-r,c,m-j,m),i.arc(e+d,s+a,c,m,m+j),i.arc(e-u,s+r,c,m+j,m+z),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=Ot;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+u,s-r),i.lineTo(e+d,s+a),i.lineTo(e-u,s+r),i.closePath();break;case"crossRot":m+=Ot;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r),m+=Ot,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function bt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function ii(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function si(i){i.restore()}function mr(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function br(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function xr(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),L(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function _r(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function yr(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Bt(i,t,e,s,n,o={}){const r=V(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,xr(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&yr(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),L(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),_r(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function me(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*z,z,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,z,j,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,j,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-j,!0),i.lineTo(e+r.topLeft,s)}const vr=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Mr=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function kr(i,t){const e=(""+i).match(vr);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const wr=i=>+i||0;function Hi(i,t){const e={},s=A(t),n=s?Object.keys(t):t,o=A(i)?s?r=>D(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=wr(o(r));return e}function In(i){return Hi(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Ft(i){return Hi(i,["topLeft","topRight","bottomLeft","bottomRight"])}function G(i){const t=In(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function $(i,t){i=i||{},t=t||N.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(Mr)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:kr(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=gr(n),n}function ie(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function Sr(i,t,e){const{min:s,max:n}=i,o=wn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function St(i,t){return Object.assign(Object.create(i),t)}function ji(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=Wn("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>ji([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Bn(a,l,()=>Rr(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return fs(a).includes(l)},ownKeys(a){return fs(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Xt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:zn(i,s),setContext:o=>Xt(i,o,e,s),override:o=>Xt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Bn(o,r,()=>Dr(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function zn(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:wt(e)?e:()=>e,isIndexable:wt(s)?s:()=>s}}const Pr=(i,t)=>i?i+zi(t):t,$i=(i,t)=>A(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Bn(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function Dr(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return wt(a)&&r.isScriptable(t)&&(a=Or(t,a,i,e)),V(a)&&a.length&&(a=Cr(t,a,i,r.isIndexable)),$i(t,a)&&(a=Xt(a,n,o&&o[t],r)),a}function Or(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),$i(i,l)&&(l=Yi(n._scopes,n,i,l)),l}function Cr(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(A(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=Yi(c,n,i,h);t.push(Xt(d,o,r&&r[i],a))}}return t}function Vn(i,t,e){return wt(i)?i(t,e):i}const Ar=(i,t)=>i===!0?t:typeof i=="string"?kt(t,i):void 0;function Tr(i,t,e,s,n){for(const o of t){const r=Ar(e,o);if(r){i.add(r);const a=Vn(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function Yi(i,t,e,s){const n=t._rootScopes,o=Vn(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=us(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=us(a,r,o,l,s),l===null)?!1:ji(Array.from(a),[""],n,o,()=>Lr(t,e,s))}function us(i,t,e,s,n){for(;e;)e=Tr(i,t,e,s,n);return e}function Lr(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return V(n)&&A(e)?e:n||{}}function Rr(i,t,e,s){let n;for(const o of t)if(n=Wn(Pr(o,i),e),typeof n<"u")return $i(i,n)?Yi(e,s,i,n):n}function Wn(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function fs(i){let t=i._keys;return t||(t=i._keys=Er(i._scopes)),t}function Er(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function Nn(i,t,e,s){const{iScale:n}=i,{key:o="r"}=this._parsing,r=new Array(s);let a,l,c,h;for(a=0,l=s;a<l;++a)c=a+e,h=t[c],r[a]={r:n.parse(kt(h,o),c)};return r}const Fr=Number.EPSILON||1e-14,Ut=(i,t)=>t<i.length&&!i[t].skip&&i[t],Hn=i=>i==="x"?"y":"x";function Ir(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=_i(o,n),l=_i(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function zr(i,t,e){const s=i.length;let n,o,r,a,l,c=Ut(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Ut(i,h+1),!(!l||!c)){if(le(t[h],0,Fr)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function Br(i,t,e="x"){const s=Hn(e),n=i.length;let o,r,a,l=Ut(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Ut(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function Vr(i,t="x"){const e=Hn(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Ut(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Ut(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?ct(n[r-1])!==ct(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}zr(i,n,o),Br(i,o,t)}function Oe(i,t,e){return Math.max(Math.min(i,e),t)}function Wr(i,t){let e,s,n,o,r,a=bt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&bt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=Oe(n.cp1x,t.left,t.right),n.cp1y=Oe(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=Oe(n.cp2x,t.left,t.right),n.cp2y=Oe(n.cp2y,t.top,t.bottom)))}function Nr(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Vr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=Ir(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&Wr(i,e)}function Xi(){return typeof window<"u"&&typeof document<"u"}function Ui(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ze(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const ni=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Hr(i,t){return ni(i).getPropertyValue(t)}const jr=["top","right","bottom","left"];function It(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=jr[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const $r=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Yr(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if($r(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function Lt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=ni(e),o=n.boxSizing==="border-box",r=It(n,"padding"),a=It(n,"border","width"),{x:l,y:c,box:h}=Yr(i,e),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function Xr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&Ui(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=ni(o),l=It(a,"border","width"),c=It(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Ze(a.maxWidth,o,"clientWidth"),n=Ze(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||qe,maxHeight:n||qe}}const Ce=i=>Math.round(i*10)/10;function Ur(i,t,e,s){const n=ni(i),o=It(n,"margin"),r=Ze(n.maxWidth,i,"clientWidth")||qe,a=Ze(n.maxHeight,i,"clientHeight")||qe,l=Xr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=It(n,"border","width"),f=It(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=Ce(Math.min(c,r,l.maxWidth)),h=Ce(Math.min(h,a,l.maxHeight)),c&&!h&&(h=Ce(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=Ce(Math.floor(h*s))),{width:c,height:h}}function gs(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Kr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};Xi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function ps(i,t){const e=Hr(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Rt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function qr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Gr(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=Rt(i,n,e),a=Rt(n,o,e),l=Rt(o,t,e),c=Rt(r,a,e),h=Rt(a,l,e);return Rt(c,h,e)}const Zr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Jr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function $t(i,t,e){return i?Zr(t,e):Jr()}function jn(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function $n(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function Yn(i){return i==="angle"?{between:pe,compare:Qo,normalize:et}:{between:pt,compare:(t,e)=>t-e,normalize:t=>t}}function ms({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Qr(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=Yn(s),l=t.length;let{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&r(a(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function Xn(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Yn(s),{start:h,end:d,loop:u,style:f}=Qr(i,t,e),g=[];let p=!1,m=null,b,x,v;const y=()=>l(n,v,b)&&a(n,v)!==0,_=()=>a(o,b)===0||l(o,v,b),k=()=>p||y(),w=()=>!p||_();for(let S=h,P=h;S<=d;++S)x=t[S%r],!x.skip&&(b=c(x[s]),b!==v&&(p=l(b,n,o),m===null&&k()&&(m=a(b,n)===0?S:P),m!==null&&w()&&(g.push(ms({start:m,end:S,loop:u,count:r,style:f})),m=null),P=S,v=b));return m!==null&&g.push(ms({start:m,end:d,loop:u,count:r,style:f})),g}function Un(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=Xn(s[n],i.points,t);o.length&&e.push(...o)}return e}function ta(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function ea(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function ia(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=ta(e,n,o,s);if(s===!0)return bs(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return bs(i,ea(e,r,l,c),e,t)}function bs(i,t,e,s){return!s||!s.setContext||!e?t:sa(i,t,e,s)}function sa(i,t,e,s){const n=i._chart.getContext(),o=xs(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,u=d;function f(g,p,m,b){const x=a?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=x;for(;e[p%l].skip;)p+=x;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=a?d:g.start;let p=e[d%l],m;for(u=d+1;u<=g.end;u++){const b=e[u%l];m=xs(s.setContext(St(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),na(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function xs(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function na(i,t){if(!t)return!1;const e=[],s=function(n,o){return Ni(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}/*!
 * Chart.js v4.4.3
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */class oa{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=An.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var dt=new oa;const _s="transparent",ra={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=cs(i||_s),n=s.valid&&cs(t||_s);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class aa{constructor(t,e,s,n){const o=e[s];n=ie([t.to,n,o,t.from]);const r=ie([t.from,o,n]);this._active=!0,this._fn=t.fn||ra[t.type||typeof r],this._easing=ce[t.easing]||ce.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ie([t.to,e,n,t.from]),this._from=ie([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class Kn{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!A(t))return;const e=Object.keys(N.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!A(o))return;const r={};for(const a of e)r[a]=o[a];(V(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=ca(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&la(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new aa(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return dt.add(this._chart,s),!0}}function la(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function ca(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function ys(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function ha(i,t,e){if(e===!1)return!1;const s=ys(i,e),n=ys(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function da(i){let t,e,s,n;return A(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function qn(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function vs(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t!==null){for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(s.all)continue;break}c=i.values[l],W(c)&&(o||t===0||ct(t)===ct(c))&&(t+=c)}return t}}function ua(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function Ms(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function fa(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function ga(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function pa(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function ks(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function ws(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=fa(o,r,s),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=pa(n,h,p),u[a]=m,u._top=ks(u,r,!0,s.type),u._bottom=ks(u,r,!1,s.type);const x=u._visualValues||(u._visualValues={});x[a]=m}}function ci(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function ma(i,t){return St(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function ba(i,t,e){return St(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Gt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const hi=i=>i==="reset"||i==="none",Ss=(i,t)=>t?i:Object.assign({},i),xa=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:qn(e,!0),values:null};class ot{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ms(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Gt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=D(s.xAxisID,ci(t,"x")),r=e.yAxisID=D(s.yAxisID,ci(t,"y")),a=e.rAxisID=D(s.rAxisID,ci(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&rs(this._data,this),t._stacked&&Gt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(A(e)){const n=this._cachedMeta;this._data=ua(e,n)}else if(s!==e){if(s){rs(s,this);const n=this._cachedMeta;Gt(n),n._parsed=[]}e&&Object.isExtensible(e)&&sr(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ms(e.vScale,e),e.stack!==s.stack&&(n=!0,Gt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&ws(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{V(n[t])?u=this.parseArrayData(s,n,t,e):A(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}r&&ws(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(kt(f,a),u),y:r.parse(kt(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:qn(n,!0),values:e._stacks[t.axis]._visualValues};return vs(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=vs(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=xa(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=ga(a);let u,f;function g(){f=n[u];const p=f[a.axis];return!W(f[t.axis])||h>p||d<p}for(u=0;u<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],W(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=da(D(this.options.clip,ha(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=ba(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=ma(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&ge(s);if(a)return Ss(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(N.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(Ss(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new Kn(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||hi(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){hi(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!hi(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Gt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}M(ot,"defaults",{}),M(ot,"datasetElementType",null),M(ot,"dataElementType",null);function _a(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Cn(s.sort((n,o)=>n-o))}return i._cache.$bar}function ya(i){const t=i.iScale,e=_a(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(ge(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function va(i,t,e,s){const n=e.barThickness;let o,r;return L(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function Ma(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function ka(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function Gn(i,t,e,s){return V(i)?ka(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Ps(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(Gn(u,d,o,c));return l}function di(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function wa(i,t,e){return i!==0?ct(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Sa(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function Pa(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=Sa(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Ds(h,r,a,l)]=!0,n=c)),o[Ds(n,r,a,l)]=!0,i.borderSkipped=o}function Ds(i,t,e,s){return s?(i=Da(i,t,e),i=Os(i,e,t)):i=Os(i,t,e),i}function Da(i,t,e){return i===t?e:i===e?t:i}function Os(i,t,e){return i==="start"?t:i==="end"?e:i}function Oa(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Be extends ot{parsePrimitiveData(t,e,s,n){return Ps(t,e,s,n)}parseArrayData(t,e,s,n){return Ps(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let u,f,g,p;for(u=s,f=s+n;u<f;++u)p=e[u],g={},g[o.axis]=o.parse(kt(p,c),u),d.push(Gn(kt(p,h),g,r,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=di(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const g=this.getParsed(f),p=o||L(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[a.axis],x={horizontal:c,base:p.base,enableBorderRadius:!b||di(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};u&&(x.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const v=x.options||t[f].options;Pa(x,v,b,r),Oa(x,v,h.ratio),this.updateElement(t[f],f,x,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(l=>l.controller.options.grouped),o=s.options.stacked,r=[],a=l=>{const c=l.controller.getParsed(e),h=c&&c[l.vScale.axis];if(L(h)||isNaN(h))return!0};for(const l of n)if(!(e!==void 0&&a(l))&&((o===!1||r.indexOf(l.stack)===-1||o===void 0&&l.stack===void 0)&&r.push(l.stack),l.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||ya(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=di(c);let d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,g,p;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&ct(d)!==ct(c.barEnd)&&(u=0),u+=d);const m=!L(o)&&!h?o:u;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(u+f):g=b,p=g-b,Math.abs(p)<r){p=wa(p,e,a)*r,d===a&&(b-=p/2);const x=e.getPixelForDecimal(0),v=e.getPixelForDecimal(1),y=Math.min(x,v),_=Math.max(x,v);b=Math.max(Math.min(b,_),y),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const x=ct(p)*e.getLineWidthForValue(a)/2;b+=x,p-=x}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=D(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?Ma(t,e,n,c):va(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}M(Be,"id","bar"),M(Be,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),M(Be,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Ve extends ot{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,s,n){const o=super.parsePrimitiveData(t,e,s,n);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+s).radius;return o}parseArrayData(t,e,s,n){const o=super.parseArrayData(t,e,s,n);for(let r=0;r<o.length;r++){const a=e[s+r];o[r]._custom=D(a[2],this.resolveDataElementOptions(r+s).radius)}return o}parseObjectData(t,e,s,n){const o=super.parseObjectData(t,e,s,n);for(let r=0;r<o.length;r++){const a=e[s+r];o[r]._custom=D(a&&a.r&&+a.r,this.resolveDataElementOptions(r+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:s[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=r.axis,d=a.axis;for(let u=e;u<e+s;u++){const f=t[u],g=!o&&this.getParsed(u),p={},m=p[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(g[h]),b=p[d]=o?a.getBasePixel():a.getPixelForValue(g[d]);p.skip=isNaN(m)||isNaN(b),c&&(p.options=l||this.resolveDataElementOptions(u,f.active?"active":n),o&&(p.options.radius=0)),this.updateElement(f,u,p,n)}}resolveDataElementOptions(t,e){const s=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=D(s&&s._custom,o),n}}M(Ve,"id","bubble"),M(Ve,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),M(Ve,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function Ca(i,t,e){let s=1,n=1,o=0,r=0;if(t<I){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(v,y,_)=>pe(v,a,l,!0)?1:Math.max(y,y*e,_,_*e),g=(v,y,_)=>pe(v,a,l,!0)?-1:Math.min(y,y*e,_,_*e),p=f(0,c,d),m=f(j,h,u),b=g(z,c,d),x=g(z+j,h,u);s=(p-b)/2,n=(m-x)/2,o=-(p+b)/2,r=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class Et extends ot{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(A(s[t])){const{key:l="value"}=this._parsing;o=c=>+kt(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return nt(this.options.rotation-90)}_getCircumference(){return nt(this.options.circumference)}_getRotationExtents(){let t=I,e=-I;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(jo(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:p}=Ca(d,h,l),m=(s.width-r)/u,b=(s.height-r)/f,x=Math.max(Math.min(m,b)/2,0),v=wn(this.options.radius,x),y=Math.max(v*l,0),_=(v-y)/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=p*v,n.total=this.calculateTotal(),this.outerRadius=v-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/I)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),x;for(x=0;x<e;++x)b+=this._circumference(x,o);for(x=e;x<e+s;++x){const v=this._circumference(x,o),y=t[x],_={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+v,circumference:v,outerRadius:g,innerRadius:f};m&&(_.options=p||this.resolveDataElementOptions(x,y.active?"active":n)),b+=v,this.updateElement(y,x,_,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?I*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=ve(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(D(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}M(Et,"id","doughnut"),M(Et,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),M(Et,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),M(Et,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});class We extends ot{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=Ln(e,n,r);this._drawStart=a,this._drawCount=l,Rn(e)&&(a=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=r.axis,f=a.axis,{spanGaps:g,segment:p}=this.options,m=Yt(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,v=t.length;let y=e>0&&this.getParsed(e-1);for(let _=0;_<v;++_){const k=t[_],w=b?k:{};if(_<e||_>=x){w.skip=!0;continue}const S=this.getParsed(_),P=L(S[f]),O=w[u]=r.getPixelForValue(S[u],_),C=w[f]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,S,l):S[f],_);w.skip=isNaN(O)||isNaN(C)||P,w.stop=_>0&&Math.abs(S[u]-y[u])>m,p&&(w.parsed=S,w.raw=c.data[_]),d&&(w.options=h||this.resolveDataElementOptions(_,k.active?"active":n)),b||this.updateElement(k,_,w,n),y=S}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}M(We,"id","line"),M(We,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),M(We,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class de extends ot{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=ve(e._parsed[t].r,s.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,s,n){return Nn.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,n)=>{const o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,s=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),r=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,u=c.getIndexAngle(0)-.5*z;let f=u,g;const p=360/this.countVisibleElements();for(g=0;g<e;++g)f+=this._computeAngle(g,n,p);for(g=e;g<e+s;g++){const m=t[g];let b=f,x=f+this._computeAngle(g,n,p),v=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=x,o&&(l.animateScale&&(v=0),l.animateRotate&&(b=x=u));const y={x:h,y:d,innerRadius:0,outerRadius:v,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(g,m.active?"active":n)};this.updateElement(m,g,y,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((s,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?nt(this.resolveDataElementOptions(t,e).angle||s):0}}M(de,"id","polarArea"),M(de,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),M(de,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Mi extends Et{}M(Mi,"id","pie"),M(Mi,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Ne extends ot{getLabelAndValue(t){const e=this._cachedMeta.vScale,s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,n){return Nn.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta,s=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(s.points=n,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===n.length,options:r};this.updateElement(s,void 0,a,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,s,n){const o=this._cachedMeta.rScale,r=n==="reset";for(let a=e;a<e+s;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":n),h=o.getPointPositionForValue(a,this.getParsed(a).r),d=r?o.xCenter:h.x,u=r?o.yCenter:h.y,f={x:d,y:u,angle:h.angle,skip:isNaN(d)||isNaN(u),options:c};this.updateElement(l,a,f,n)}}}M(Ne,"id","radar"),M(Ne,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),M(Ne,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class He extends ot{getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:s[t]||"",value:"("+a+", "+l+")"}}update(t){const e=this._cachedMeta,{data:s=[]}=e,n=this.chart._animationsDisabled;let{start:o,count:r}=Ln(e,s,n);if(this._drawStart=o,this._drawCount=r,Rn(e)&&(o=0,r=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(s,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),d=this.getSharedOptions(h),u=this.includeOptions(n,d),f=r.axis,g=a.axis,{spanGaps:p,segment:m}=this.options,b=Yt(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||n==="none";let v=e>0&&this.getParsed(e-1);for(let y=e;y<e+s;++y){const _=t[y],k=this.getParsed(y),w=x?_:{},S=L(k[g]),P=w[f]=r.getPixelForValue(k[f],y),O=w[g]=o||S?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,k,l):k[g],y);w.skip=isNaN(P)||isNaN(O)||S,w.stop=y>0&&Math.abs(k[f]-v[f])>b,m&&(w.parsed=k,w.raw=c.data[y]),u&&(w.options=d||this.resolveDataElementOptions(y,_.active?"active":n)),x||this.updateElement(_,y,w,n),v=k}this.updateSharedOptions(d,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const s=t.dataset,n=s.options&&s.options.borderWidth||0;if(!e.length)return n;const o=e[0].size(this.resolveDataElementOptions(0)),r=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,r)/2}}M(He,"id","scatter"),M(He,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),M(He,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var Aa=Object.freeze({__proto__:null,BarController:Be,BubbleController:Ve,DoughnutController:Et,LineController:We,PieController:Mi,PolarAreaController:de,RadarController:Ne,ScatterController:He});function At(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ki{constructor(t){M(this,"options");this.options=t||{}}static override(t){Object.assign(Ki.prototype,t)}init(){}formats(){return At()}parse(){return At()}format(){return At()}add(){return At()}diff(){return At()}startOf(){return At()}endOf(){return At()}}var Ta={_date:Ki};function La(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const l=a._reversePixels?er:mt;if(s){if(n._sharedOptions){const c=o[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){const d=l(o,t,e-h),u=l(o,t,e+h);return{lo:d.lo,hi:u.hi}}}}else return l(o,t,e)}return{lo:0,hi:o.length-1}}function Me(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=La(o[a],t,r,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function Ra(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function ui(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Me(i,e,t,function(a,l,c){!n&&!bt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Ea(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=Dn(r,{x:t.x,y:t.y});pe(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Me(i,e,t,o),n}function Fa(i,t,e,s,n,o){let r=[];const a=Ra(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return Me(i,e,t,c),r}function fi(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Ea(i,t,e,n):Fa(i,t,e,s,n,o)}function Cs(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Me(i,e,t,(l,c,h)=>{l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var Ia={evaluateInteractionItems:Me,modes:{index(i,t,e,s){const n=Lt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?ui(i,n,o,s,r):fi(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=Lt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?ui(i,n,o,s,r):fi(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=Lt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return ui(i,n,o,s,r)},nearest(i,t,e,s){const n=Lt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return fi(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=Lt(t,i);return Cs(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=Lt(t,i);return Cs(i,n,"y",e.intersect,s)}}};const Zn=["left","top","right","bottom"];function Zt(i,t){return i.filter(e=>e.pos===t)}function As(i,t){return i.filter(e=>Zn.indexOf(e.pos)===-1&&e.box.axis===t)}function Jt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function za(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Ba(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!Zn.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Va(i,t){const e=Ba(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Wa(i){const t=za(i),e=Jt(t.filter(c=>c.box.fullSize),!0),s=Jt(Zt(t,"left"),!0),n=Jt(Zt(t,"right")),o=Jt(Zt(t,"top"),!0),r=Jt(Zt(t,"bottom")),a=As(t,"x"),l=As(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Zt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Ts(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Jn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Na(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!A(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Jn(r,o.getPadding());const a=Math.max(0,t.outerWidth-Ts(r,i,"left","right")),l=Math.max(0,t.outerHeight-Ts(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function Ha(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function ja(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function se(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,ja(a.horizontal,t));const{same:d,other:u}=Na(t,e,a,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(a)}return c&&se(n,t,e,s)||h}function Ae(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Ls(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;ge(c.start)&&(r=c.start),l.fullSize?Ae(l,n.left,r,e.outerWidth-n.right-n.left,u):Ae(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;ge(c.start)&&(o=c.start),l.fullSize?Ae(l,o,n.top,u,e.outerHeight-n.bottom-n.top):Ae(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var q={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=G(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Wa(i.boxes),l=a.vertical,c=a.horizontal;R(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},n);Jn(u,G(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=Va(l.concat(c),d);se(a.fullSize,f,d,g),se(l,f,d,g),se(c,f,d,g)&&se(l,f,d,g),Ha(f),Ls(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Ls(a.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},R(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Qn{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class $a extends Qn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const je="$chartjs",Ya={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Rs=i=>i===null||i==="";function Xa(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[je]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Rs(n)){const o=ps(i,"width");o!==void 0&&(i.width=o)}if(Rs(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=ps(i,"height");o!==void 0&&(i.height=o)}return i}const to=Kr?{passive:!0}:!1;function Ua(i,t,e){i&&i.addEventListener(t,e,to)}function Ka(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,to)}function qa(i,t){const e=Ya[i.type]||i.type,{x:s,y:n}=Lt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Je(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Ga(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Je(a.addedNodes,s),r=r&&!Je(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Za(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Je(a.removedNodes,s),r=r&&!Je(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const be=new Map;let Es=0;function eo(){const i=window.devicePixelRatio;i!==Es&&(Es=i,be.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Ja(i,t){be.size||window.addEventListener("resize",eo),be.set(i,t)}function Qa(i){be.delete(i),be.size||window.removeEventListener("resize",eo)}function tl(i,t,e){const s=i.canvas,n=s&&Ui(s);if(!n)return;const o=Tn((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),Ja(i,o),r}function gi(i,t,e){e&&e.disconnect(),t==="resize"&&Qa(i)}function el(i,t,e){const s=i.canvas,n=Tn(o=>{i.ctx!==null&&e(qa(o,i))},i);return Ua(s,t,n),n}class il extends Qn{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Xa(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[je])return!1;const s=e[je].initial;["height","width"].forEach(o=>{const r=s[o];L(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[je],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Ga,detach:Za,resize:tl}[e]||el;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:gi,detach:gi,resize:gi}[e]||Ka)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Ur(t,e,s,n)}isAttached(t){const e=t&&Ui(t);return!!(e&&e.isConnected)}}function sl(i){return!Xi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?$a:il}class rt{constructor(){M(this,"x");M(this,"y");M(this,"active",!1);M(this,"options");M(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Yt(this.x)&&Yt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}M(rt,"defaults",{}),M(rt,"defaultRoutes");function nl(i,t){const e=i.options.ticks,s=ol(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?al(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return ll(t,c,o,r/n),c;const h=rl(o,t,n);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(Te(t,c,h,L(f)?0:a-f,a),d=0,u=r-1;d<u;d++)Te(t,c,h,o[d],o[d+1]);return Te(t,c,h,l,L(f)?t.length:l+f),c}return Te(t,c,h),c}function ol(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function rl(i,t,e){const s=cl(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Zo(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function al(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function ll(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function Te(i,t,e,s,n){const o=D(s,0),r=Math.min(D(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function cl(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const hl=i=>i==="left"?"right":i==="right"?"left":i,Fs=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Is=(i,t)=>Math.min(t||i,i);function zs(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function dl(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function ul(i,t){R(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Qt(i){return i.drawTicks?i.tickLength:0}function Bs(i,t){if(!i.display)return 0;const e=$(i.font,t),s=G(i.padding);return(V(i.text)?i.text.length:1)*e.lineHeight+s.height}function fl(i,t){return St(i,{scale:t,type:"scale"})}function gl(i,t,e){return St(i,{tick:e,index:t,type:"tick"})}function pl(i,t,e){let s=Wi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=hl(s)),s}function ml(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(f=K(s,o,a),A(e)){const b=Object.keys(e)[0],x=e[b];g=h[b].getPixelForValue(x)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=Fs(i,e,t);u=a-o}else{if(A(e)){const b=Object.keys(e)[0],x=e[b];f=h[b].getPixelForValue(x)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=Fs(i,e,t);g=K(s,r,n),d=e==="left"?-j:j}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class Vt extends rt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=tt(t,Number.POSITIVE_INFINITY),e=tt(e,Number.NEGATIVE_INFINITY),s=tt(s,Number.POSITIVE_INFINITY),n=tt(n,Number.NEGATIVE_INFINITY),{min:tt(t,s),max:tt(e,n),minDefined:W(t),maxDefined:W(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:tt(e,tt(s,e)),max:tt(s,tt(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){F(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Sr(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?zs(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=nl(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){F(this.options.afterUpdate,[this])}beforeSetDimensions(){F(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){F(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),F(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){F(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=F(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){F(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){F(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Is(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=Y(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:f/(s-1),d+6>a&&(a=f/(s-(t.offset?.5:1)),l=this.maxHeight-Qt(t.grid)-e.padding-Bs(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=Bi(Math.min(Math.asin(Y((h.highest.height+6)/a,-1,1)),Math.asin(Y(l/c,-1,1))-Math.asin(Y(u/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){F(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){F(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Bs(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Qt(o)+l):(t.height=this.maxHeight,t.width=Qt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=nt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){F(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)L(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=zs(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Is(e,s));let c=0,h=0,d,u,f,g,p,m,b,x,v,y,_;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},x=p.lineHeight,v=y=0,!L(g)&&!V(g))v=Ge(n,b.data,b.gc,v,g),y=x;else if(V(g))for(u=0,f=g.length;u<f;++u)_=g[u],!L(_)&&!V(_)&&(v=Ge(n,b.data,b.gc,v,_),y+=x);r.push(v),a.push(y),c=Math.max(v,c),h=Math.max(y,h)}ul(o,e);const k=r.indexOf(c),w=a.indexOf(h),S=P=>({width:r[P]||0,height:a[P]||0});return{first:S(0),last:S(e-1),widest:S(k),highest:S(w),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return tr(this._alignToPixels?Ct(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=gl(this.getContext(),t,s))}return this.$context||(this.$context=fl(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=nt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Qt(o),f=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(B){return Ct(s,B,p)};let x,v,y,_,k,w,S,P,O,C,T,X;if(r==="top")x=b(this.bottom),w=this.bottom-u,P=x-m,C=b(t.top)+m,X=t.bottom;else if(r==="bottom")x=b(this.top),C=t.top,X=b(t.bottom)-m,w=x+m,P=this.top+u;else if(r==="left")x=b(this.right),k=this.right-u,S=x-m,O=b(t.left)+m,T=t.right;else if(r==="right")x=b(this.left),O=t.left,T=b(t.right)-m,k=x+m,S=this.left+u;else if(e==="x"){if(r==="center")x=b((t.top+t.bottom)/2+.5);else if(A(r)){const B=Object.keys(r)[0],H=r[B];x=b(this.chart.scales[B].getPixelForValue(H))}C=t.top,X=t.bottom,w=x+m,P=w+u}else if(e==="y"){if(r==="center")x=b((t.left+t.right)/2);else if(A(r)){const B=Object.keys(r)[0],H=r[B];x=b(this.chart.scales[B].getPixelForValue(H))}k=x-m,S=k-u,O=t.left,T=t.right}const Q=D(n.ticks.maxTicksLimit,d),E=Math.max(1,Math.ceil(d/Q));for(v=0;v<d;v+=E){const B=this.getContext(v),H=o.setContext(B),st=a.setContext(B),U=H.lineWidth,Wt=H.color,ke=st.dash||[],Nt=st.dashOffset,Kt=H.tickWidth,Pt=H.tickColor,qt=H.tickBorderDash||[],Dt=H.tickBorderDashOffset;y=dl(this,v,l),y!==void 0&&(_=Ct(s,y,U),c?k=S=O=T=_:w=P=C=X=_,f.push({tx1:k,ty1:w,tx2:S,ty2:P,x1:O,y1:C,x2:T,y2:X,width:U,color:Wt,borderDash:ke,borderDashOffset:Nt,tickWidth:Kt,tickColor:Pt,tickBorderDash:qt,tickBorderDashOffset:Dt}))}return this._ticksLength=d,this._borderValue=x,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Qt(s.grid),f=u+h,g=d?-h:f,p=-nt(this.labelRotation),m=[];let b,x,v,y,_,k,w,S,P,O,C,T,X="middle";if(n==="top")k=this.bottom-g,w=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+g,w=this._getXAxisLabelAlignment();else if(n==="left"){const E=this._getYAxisLabelAlignment(u);w=E.textAlign,_=E.x}else if(n==="right"){const E=this._getYAxisLabelAlignment(u);w=E.textAlign,_=E.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+f;else if(A(n)){const E=Object.keys(n)[0],B=n[E];k=this.chart.scales[E].getPixelForValue(B)+f}w=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")_=(t.left+t.right)/2-f;else if(A(n)){const E=Object.keys(n)[0],B=n[E];_=this.chart.scales[E].getPixelForValue(B)}w=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?X="top":l==="end"&&(X="bottom"));const Q=this._getLabelSizes();for(b=0,x=a.length;b<x;++b){v=a[b],y=v.label;const E=o.setContext(this.getContext(b));S=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),O=P.lineHeight,C=V(y)?y.length:1;const B=C/2,H=E.color,st=E.textStrokeColor,U=E.textStrokeWidth;let Wt=w;r?(_=S,w==="inner"&&(b===x-1?Wt=this.options.reverse?"left":"right":b===0?Wt=this.options.reverse?"right":"left":Wt="center"),n==="top"?c==="near"||p!==0?T=-C*O+O/2:c==="center"?T=-Q.highest.height/2-B*O+O:T=-Q.highest.height+O/2:c==="near"||p!==0?T=O/2:c==="center"?T=Q.highest.height/2-B*O:T=Q.highest.height-C*O,d&&(T*=-1),p!==0&&!E.showLabelBackdrop&&(_+=O/2*Math.sin(p))):(k=S,T=(1-C)*O/2);let ke;if(E.showLabelBackdrop){const Nt=G(E.backdropPadding),Kt=Q.heights[b],Pt=Q.widths[b];let qt=T-Nt.top,Dt=0-Nt.left;switch(X){case"middle":qt-=Kt/2;break;case"bottom":qt-=Kt;break}switch(w){case"center":Dt-=Pt/2;break;case"right":Dt-=Pt;break;case"inner":b===x-1?Dt-=Pt:b>0&&(Dt-=Pt/2);break}ke={left:Dt,top:qt,width:Pt+Nt.width,height:Kt+Nt.height,color:E.backdropColor}}m.push({label:y,font:P,textOffset:T,options:{rotation:p,color:H,strokeColor:st,strokeWidth:U,textAlign:Wt,textBaseline:X,translation:[_,k],backdrop:ke}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-nt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=Ct(t,this.left,r)-r/2,h=Ct(t,this.right,a)+a/2,d=u=l):(d=Ct(t,this.top,r)-r/2,u=Ct(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&ii(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Bt(s,c,0,h,l,a)}n&&si(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=$(s.font),r=G(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||A(e)?(l+=r.bottom,V(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=ml(this,l,e,a);Bt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:pl(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Vt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return $(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Le{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;_l(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,bl(t,r,s),this.override&&N.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in N[n]&&(delete N[n][s],this.override&&delete zt[s])}}function bl(i,t,e){const s=fe(Object.create(null),[e?N.get(e):{},N.get(t),i.defaults]);N.set(t,s),i.defaultRoutes&&xl(t,i.defaultRoutes),i.descriptors&&N.describe(t,i.descriptors)}function xl(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");N.route(o,n,l,a)})}function _l(i){return"id"in i&&"defaults"in i}class yl{constructor(){this.controllers=new Le(ot,"datasets",!0),this.elements=new Le(rt,"elements"),this.plugins=new Le(Object,"plugins"),this.scales=new Le(Vt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):R(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=zi(t);F(s["before"+n],[],s),e[t](s),F(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var lt=new yl;class vl{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(F(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){L(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=Ml(s);return n===!1&&!e?[]:wl(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function Ml(i){const t={},e=[],s=Object.keys(lt.plugins.items);for(let o=0;o<s.length;o++)e.push(lt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function kl(i,t){return!t&&i===!1?null:i===!0?{}:i}function wl(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=kl(s[l],n);c!==null&&o.push({plugin:a,options:Sl(i.config,{plugin:a,local:e[l]},c,r)})}return o}function Sl(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ki(i,t){const e=N.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Pl(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Dl(i,t){return i===t?"_index_":"_value_"}function Vs(i){if(i==="x"||i==="y"||i==="r")return i}function Ol(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function wi(i,...t){if(Vs(i))return i;for(const e of t){const s=e.axis||Ol(e.position)||i.length>1&&Vs(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Ws(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Cl(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Ws(i,"x",e[0])||Ws(i,"y",e[0])}return{}}function Al(i,t){const e=zt[i.type]||{scales:{}},s=t.scales||{},n=ki(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!A(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=wi(r,a,Cl(r,i),N.scales[a.type]),c=Dl(l,n),h=e.scales||{};o[r]=ae(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||ki(a,t),h=(zt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=Pl(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),ae(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];ae(a,[N.scales[a.type],N.scale])}),o}function io(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=Al(i,t)}function so(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Tl(i){return i=i||{},i.data=so(i.data),io(i),i}const Ns=new Map,no=new Set;function Re(i,t){let e=Ns.get(i);return e||(e=t(),Ns.set(i,e),no.add(e)),e}const te=(i,t,e)=>{const s=kt(t,e);s!==void 0&&i.add(s)};class Ll{constructor(t){this._config=Tl(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=so(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),io(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Re(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Re(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Re(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return Re(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>te(l,t,d))),h.forEach(d=>te(l,n,d)),h.forEach(d=>te(l,zt[o]||{},d)),h.forEach(d=>te(l,N,d)),h.forEach(d=>te(l,yi,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),no.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,zt[e]||{},N.datasets[e]||{},{type:e},N,yi]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Hs(this._resolverCache,t,n);let l=r;if(El(r,e)){o.$shared=!1,s=wt(s)?s():s;const c=this.createResolver(t,s,a);l=Xt(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=Hs(this._resolverCache,t,s);return A(e)?Xt(o,e,void 0,n):o}}function Hs(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:ji(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Rl=i=>A(i)&&Object.getOwnPropertyNames(i).some(t=>wt(i[t]));function El(i,t){const{isScriptable:e,isIndexable:s}=zn(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(wt(a)||Rl(a))||r&&V(a))return!0}return!1}var Fl="4.4.3";const Il=["top","bottom","left","right","chartArea"];function js(i,t){return i==="top"||i==="bottom"||Il.indexOf(i)===-1&&t==="x"}function $s(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function Ys(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),F(e&&e.onComplete,[i],t)}function zl(i){const t=i.chart,e=t.options.animation;F(e&&e.onProgress,[i],t)}function oo(i){return Xi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const $e={},Xs=i=>{const t=oo(i);return Object.values($e).filter(e=>e.canvas===t).pop()};function Bl(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Vl(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function Ee(i,t,e){return i.options.clip?i[e]:t[e]}function Wl(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:Ee(e,t,"left"),right:Ee(e,t,"right"),top:Ee(s,t,"top"),bottom:Ee(s,t,"bottom")}:t}class gt{static register(...t){lt.add(...t),Us()}static unregister(...t){lt.remove(...t),Us()}constructor(t,e){const s=this.config=new Ll(e),n=oo(t),o=Xs(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||sl(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Ho(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new vl,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=nr(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],$e[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}dt.listen(this,"complete",Ys),dt.listen(this,"progress",zl),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return L(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return lt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():gs(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ds(this.canvas,this.ctx),this}stop(){return dt.stop(this),this}resize(t,e){dt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,gs(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),F(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};R(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=wi(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),R(o,r=>{const a=r.options,l=a.id,c=wi(l,a),h=D(a.type,r.dtype);(a.position===void 0||js(a.position,c)!==js(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=lt.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),R(n,(r,a)=>{r||delete s[a]}),R(s,r=>{q.configure(this,r,r.options),q.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort($s("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||ki(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=lt.getController(a),{datasetElementType:c,dataElementType:h}=N.datasets[a];Object.assign(l,{dataElementType:lt.getElement(h),datasetElementType:c&&lt.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){R(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||R(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort($s("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){R(this.scales,t=>{q.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!is(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Bl(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!is(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;q.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],R(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,wt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(dt.has(this)?this.attached&&!dt.running(this)&&dt.start(this):(this.draw(),Ys({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resize(s,n),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Wl(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(n&&ii(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&si(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return bt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=Ia.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=St(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);ge(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),dt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ds(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete $e[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};R(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){R(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},R(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Ue(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Ko(t),c=Vl(t,this._lastEvent,s,l);s&&(this._lastEvent=null,F(o.onHover,[t,a,this],this),l&&F(o.onClick,[t,a,this],this));const h=!Ue(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}M(gt,"defaults",N),M(gt,"instances",$e),M(gt,"overrides",zt),M(gt,"registry",lt),M(gt,"version",Fl),M(gt,"getChart",Xs);function Us(){return R(gt.instances,i=>i._plugins.invalidate())}function Nl(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+j,s-j),i.closePath(),i.clip()}function Hl(i){return Hi(i,["outerStart","outerEnd","innerStart","innerEnd"])}function jl(i,t,e,s){const n=Hl(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return Y(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:Y(n.innerStart,0,r),innerEnd:Y(n.innerEnd,0,r)}}function jt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function Qe(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0;let f=0;const g=n-l;if(s){const E=h>0?h-s:0,B=d>0?d-s:0,H=(E+B)/2,st=H!==0?g*H/(H+s):g;f=(g-st)/2}const p=Math.max(.001,g*d-e/z)/d,m=(g-p)/2,b=l+m+f,x=n-m-f,{outerStart:v,outerEnd:y,innerStart:_,innerEnd:k}=jl(t,u,d,x-b),w=d-v,S=d-y,P=b+v/w,O=x-y/S,C=u+_,T=u+k,X=b+_/C,Q=x-k/T;if(i.beginPath(),o){const E=(P+O)/2;if(i.arc(r,a,d,P,E),i.arc(r,a,d,E,O),y>0){const U=jt(S,O,r,a);i.arc(U.x,U.y,y,O,x+j)}const B=jt(T,x,r,a);if(i.lineTo(B.x,B.y),k>0){const U=jt(T,Q,r,a);i.arc(U.x,U.y,k,x+j,Q+Math.PI)}const H=(x-k/u+(b+_/u))/2;if(i.arc(r,a,u,x-k/u,H,!0),i.arc(r,a,u,H,b+_/u,!0),_>0){const U=jt(C,X,r,a);i.arc(U.x,U.y,_,X+Math.PI,b-j)}const st=jt(w,b,r,a);if(i.lineTo(st.x,st.y),v>0){const U=jt(w,P,r,a);i.arc(U.x,U.y,v,b-j,P)}}else{i.moveTo(r,a);const E=Math.cos(P)*d+r,B=Math.sin(P)*d+a;i.lineTo(E,B);const H=Math.cos(O)*d+r,st=Math.sin(O)*d+a;i.lineTo(H,st)}i.closePath()}function $l(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Qe(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%I||I))}return Qe(i,t,e,s,l,n),i.fill(),l}function Yl(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){Qe(i,t,e,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(a)||(g=r+(a%I||I))}f&&Nl(i,t,g),o||(Qe(i,t,e,s,g,n),i.stroke())}class ne extends rt{constructor(e){super();M(this,"circumference");M(this,"endAngle");M(this,"fullCircles");M(this,"innerRadius");M(this,"outerRadius");M(this,"pixelMargin");M(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.getProps(["x","y"],n),{angle:r,distance:a}=Dn(o,{x:e,y:s}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),f=(this.options.spacing+this.options.borderWidth)/2,p=D(u,c-l)>=I||pe(r,l,c),m=pt(a,h+f,d+f);return p&&m}getCenterPoint(e){const{x:s,y:n,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,d=(o+r)/2,u=(a+l+h+c)/2;return{x:s+Math.cos(d)*u,y:n+Math.sin(d)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:s,circumference:n}=this,o=(s.offset||0)/4,r=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>I?Math.floor(n/I):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(z,n||0)),h=o*c;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,$l(e,this,h,r,a),Yl(e,this,h,r,a),e.restore()}}M(ne,"id","arc"),M(ne,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),M(ne,"defaultRoutes",{backgroundColor:"backgroundColor"}),M(ne,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function ro(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function Xl(i,t,e){i.lineTo(e.x,e.y)}function Ul(i){return i.stepped?mr:i.tension||i.cubicInterpolationMode==="monotone"?br:Xl}function ao(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function Kl(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=ao(n,e,s),h=Ul(o);let{move:d=!0,reverse:u}=s||{},f,g,p;for(f=0;f<=c;++f)g=n[(a+(u?c-f:f))%r],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,u,o.stepped),p=g);return l&&(g=n[(a+(u?c:0))%r],h(i,p,g,u,o.stepped)),!!l}function ql(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=ao(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,u,f,g,p,m,b;const x=y=>(r+(c?a-y:y))%o,v=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(f=n[x(0)],i.moveTo(f.x,f.y)),u=0;u<=a;++u){if(f=n[x(u)],f.skip)continue;const y=f.x,_=f.y,k=y|0;k===g?(_<p?p=_:_>m&&(m=_),h=(d*h+y)/++d):(v(),i.lineTo(y,_),g=k,d=0,p=m=_),b=_}v()}function Si(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?ql:Kl}function Gl(i){return i.stepped?qr:i.tension||i.cubicInterpolationMode==="monotone"?Gr:Rt}function Zl(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),ro(i,t.options),i.stroke(n)}function Jl(i,t,e,s){const{segments:n,options:o}=t,r=Si(t);for(const a of n)ro(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Ql=typeof Path2D=="function";function tc(i,t,e,s){Ql&&!t.options.segment?Zl(i,t,e,s):Jl(i,t,e,s)}class yt extends rt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;Nr(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=ia(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=Un(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Gl(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:u}=r[c],f=o[d],g=o[u];if(f===g){a.push(f);continue}const p=Math.abs((n-f[e])/(g[e]-f[e])),m=l(f,g,p,s.stepped);m[e]=t[e],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,e,s){return Si(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=Si(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),tc(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}M(yt,"id","line"),M(yt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),M(yt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),M(yt,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function Ks(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class Ye extends rt{constructor(e){super();M(this,"parsed");M(this,"skip");M(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return Ks(this,e,"x",s)}inYRange(e,s){return Ks(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!bt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,vi(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}M(Ye,"id","point"),M(Ye,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),M(Ye,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function lo(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function vt(i,t,e,s){return i?0:Y(t,e,s)}function ec(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=In(s);return{t:vt(n.top,o.top,0,e),r:vt(n.right,o.right,0,t),b:vt(n.bottom,o.bottom,0,e),l:vt(n.left,o.left,0,t)}}function ic(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Ft(n),r=Math.min(t,e),a=i.borderSkipped,l=s||A(n);return{topLeft:vt(!l||a.top||a.left,o.topLeft,0,r),topRight:vt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:vt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:vt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function sc(i){const t=lo(i),e=t.right-t.left,s=t.bottom-t.top,n=ec(i,e/2,s/2),o=ic(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function pi(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&lo(i,s);return a&&(n||pt(t,a.left,a.right))&&(o||pt(e,a.top,a.bottom))}function nc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function oc(i,t){i.rect(t.x,t.y,t.w,t.h)}function mi(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Xe extends rt{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=sc(this),a=nc(r.radius)?me:oc;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,mi(r,e,o)),t.clip(),a(t,mi(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,mi(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return pi(this,t,e,s)}inXRange(t,e){return pi(this,t,null,e)}inYRange(t,e){return pi(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}M(Xe,"id","bar"),M(Xe,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),M(Xe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var rc=Object.freeze({__proto__:null,ArcElement:ne,BarElement:Xe,LineElement:yt,PointElement:Ye});const Pi=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],qs=Pi.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function co(i){return Pi[i%Pi.length]}function ho(i){return qs[i%qs.length]}function ac(i,t){return i.borderColor=co(t),i.backgroundColor=ho(t),++t}function lc(i,t){return i.backgroundColor=i.data.map(()=>co(t++)),t}function cc(i,t){return i.backgroundColor=i.data.map(()=>ho(t++)),t}function hc(i){let t=0;return(e,s)=>{const n=i.getDatasetMeta(s).controller;n instanceof Et?t=lc(e,t):n instanceof de?t=cc(e,t):n&&(t=ac(e,t))}}function Gs(i){let t;for(t in i)if(i[t].borderColor||i[t].backgroundColor)return!0;return!1}function dc(i){return i&&(i.borderColor||i.backgroundColor)}var uc={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,t,e){if(!e.enabled)return;const{data:{datasets:s},options:n}=i.config,{elements:o}=n;if(!e.forceOverride&&(Gs(s)||dc(n)||o&&Gs(o)))return;const r=hc(i);s.forEach(r)}};function fc(i,t,e,s,n){const o=n.samples||s;if(o>=e)return i.slice(t,t+e);const r=[],a=(e-2)/(o-2);let l=0;const c=t+e-1;let h=t,d,u,f,g,p;for(r[l++]=i[h],d=0;d<o-2;d++){let m=0,b=0,x;const v=Math.floor((d+1)*a)+1+t,y=Math.min(Math.floor((d+2)*a)+1,e)+t,_=y-v;for(x=v;x<y;x++)m+=i[x].x,b+=i[x].y;m/=_,b/=_;const k=Math.floor(d*a)+1+t,w=Math.min(Math.floor((d+1)*a)+1,e)+t,{x:S,y:P}=i[h];for(f=g=-1,x=k;x<w;x++)g=.5*Math.abs((S-m)*(i[x].y-P)-(S-i[x].x)*(b-P)),g>f&&(f=g,u=i[x],p=x);r[l++]=u,h=p}return r[l++]=i[c],r}function gc(i,t,e,s){let n=0,o=0,r,a,l,c,h,d,u,f,g,p;const m=[],b=t+e-1,x=i[t].x,y=i[b].x-x;for(r=t;r<t+e;++r){a=i[r],l=(a.x-x)/y*s,c=a.y;const _=l|0;if(_===h)c<g?(g=c,d=r):c>p&&(p=c,u=r),n=(o*n+a.x)/++o;else{const k=r-1;if(!L(d)&&!L(u)){const w=Math.min(d,u),S=Math.max(d,u);w!==f&&w!==k&&m.push({...i[w],x:n}),S!==f&&S!==k&&m.push({...i[S],x:n})}r>0&&k!==f&&m.push(i[k]),m.push(a),h=_,o=0,g=p=c,d=u=f=r}}return m}function uo(i){if(i._decimated){const t=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function Zs(i){i.data.datasets.forEach(t=>{uo(t)})}function pc(i,t){const e=t.length;let s=0,n;const{iScale:o}=i,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=Y(mt(t,o.axis,r).lo,0,e-1)),c?n=Y(mt(t,o.axis,a).hi+1,s,e)-s:n=e-s,{start:s,count:n}}var mc={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,t,e)=>{if(!e.enabled){Zs(i);return}const s=i.width;i.data.datasets.forEach((n,o)=>{const{_data:r,indexAxis:a}=n,l=i.getDatasetMeta(o),c=r||n.data;if(ie([a,i.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||i.options.parsing)return;let{start:d,count:u}=pc(l,c);const f=e.threshold||4*s;if(u<=f){uo(n);return}L(r)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(e.algorithm){case"lttb":g=fc(c,d,u,s,e);break;case"min-max":g=gc(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=g})},destroy(i){Zs(i)}};function bc(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=qi(l,c,n);const h=Di(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=Un(t,h);for(const u of d){const f=Di(e,o[u.start],o[u.end],u.loop),g=Xn(a,n,f);for(const p of g)r.push({source:p,target:u,start:{[e]:Js(h,f,"start",Math.max)},end:{[e]:Js(h,f,"end",Math.min)}})}}return r}function Di(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=et(n),o=et(o)),{property:i,start:n,end:o}}function xc(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=qi(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function qi(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function Js(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function fo(i,t){let e=[],s=!1;return V(i)?(s=!0,e=i):e=xc(i,t),e.length?new yt({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function Qs(i){return i&&i.fill!==!1}function _c(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!W(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function yc(i,t,e){const s=wc(i);if(A(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return W(n)&&Math.floor(n)===n?vc(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function vc(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Mc(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:A(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function kc(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:A(i)?s=i.value:s=t.getBaseValue(),s}function wc(i){const t=i.options,e=t.fill;let s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Sc(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=Pc(t,e);a.push(fo({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Dc(n,r[h],a)}return new yt({points:n,options:{}})}function Pc(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Dc(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=Oc(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function Oc(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],u=r[h.end][e];if(pt(n,d,u)){a=n===d,l=n===u;break}}return{first:a,last:l,point:s}}class go{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:I},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function Cc(i){const{chart:t,fill:e,line:s}=i;if(W(e))return Ac(t,e);if(e==="stack")return Sc(i);if(e==="shape")return!0;const n=Tc(i);return n instanceof go?n:fo(n,s)}function Ac(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function Tc(i){return(i.scale||{}).getPointPositionForValue?Rc(i):Lc(i)}function Lc(i){const{scale:t={},fill:e}=i,s=Mc(e,t);if(W(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function Rc(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=kc(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new go({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function bi(i,t,e){const s=Cc(t),{line:n,scale:o,axis:r}=t,a=n.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:d=c}=l||{};s&&n.points.length&&(ii(i,e),Ec(i,{line:n,target:s,above:h,below:d,area:e,scale:o,axis:r}),si(i))}function Ec(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a}=t,l=e._loop?"angle":t.axis;i.save(),l==="x"&&o!==n&&(tn(i,s,r.top),en(i,{line:e,target:s,color:n,scale:a,property:l}),i.restore(),i.save(),tn(i,s,r.bottom)),en(i,{line:e,target:s,color:o,scale:a,property:l}),i.restore()}function tn(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[qi(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function en(i,t){const{line:e,target:s,property:n,color:o,scale:r}=t,a=bc(e,s,n);for(const{source:l,target:c,start:h,end:d}of a){const{style:{backgroundColor:u=o}={}}=l,f=s!==!0;i.save(),i.fillStyle=u,Fc(i,r,f&&Di(n,h,d)),i.beginPath();const g=!!e.pathSegment(i,l);let p;if(f){g?i.closePath():sn(i,s,d,n);const m=!!s.pathSegment(i,c,{move:g,reverse:!0});p=g&&m,p||sn(i,s,h,n)}i.closePath(),i.fill(p?"evenodd":"nonzero"),i.restore()}}function Fc(i,t,e){const{top:s,bottom:n}=t.chart.chartArea,{property:o,start:r,end:a}=e||{};o==="x"&&(i.beginPath(),i.rect(r,s,a-r,n-s),i.clip())}function sn(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var Ic={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof yt&&(l={visible:i.isDatasetVisible(r),index:r,fill:yc(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=_c(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&bi(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;Qs(o)&&bi(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!Qs(s)||e.drawTime!=="beforeDatasetDraw"||bi(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const nn=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},zc=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class on extends rt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=F(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=$(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=nn(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:x,itemHeight:v}=Bc(s,e,o,m,n);b>0&&f+v+2*a>h&&(d+=u+a,c.push({width:u,height:f}),g+=u+a,p++,u=f=0),l[b]={left:g,top:f,col:p,width:x,height:v},u=Math.max(u,x),f+=v+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=$t(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=K(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=K(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=K(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=K(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;ii(t,this),this._draw(),si(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=N.color,l=$t(t.rtl,this.left,this.width),c=$(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=nn(r,d),b=function(k,w,S){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const P=D(S.lineWidth,1);if(n.fillStyle=D(S.fillStyle,a),n.lineCap=D(S.lineCap,"butt"),n.lineDashOffset=D(S.lineDashOffset,0),n.lineJoin=D(S.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=D(S.strokeStyle,a),n.setLineDash(D(S.lineDash,[])),r.usePointStyle){const O={radius:p*Math.SQRT2/2,pointStyle:S.pointStyle,rotation:S.rotation,borderWidth:P},C=l.xPlus(k,g/2),T=w+u;Fn(n,O,C,T,r.pointStyleWidth&&g)}else{const O=w+Math.max((d-p)/2,0),C=l.leftForLtr(k,g),T=Ft(S.borderRadius);n.beginPath(),Object.values(T).some(X=>X!==0)?me(n,{x:C,y:O,w:g,h:p,radius:T}):n.rect(C,O,g,p),n.fill(),P!==0&&n.stroke()}n.restore()},x=function(k,w,S){Bt(n,S.text,k,w+m/2,c,{strikethrough:S.hidden,textAlign:l.textAlign(S.textAlign)})},v=this.isHorizontal(),y=this._computeTitleHeight();v?f={x:K(o,this.left+h,this.right-s[0]),y:this.top+h+y,line:0}:f={x:this.left+h,y:K(o,this.top+y+h,this.bottom-e[0].height),line:0},jn(this.ctx,t.textDirection);const _=m+h;this.legendItems.forEach((k,w)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const S=n.measureText(k.text).width,P=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),O=g+u+S;let C=f.x,T=f.y;l.setWidth(this.width),v?w>0&&C+O+h>this.right&&(T=f.y+=_,f.line++,C=f.x=K(o,this.left+h,this.right-s[f.line])):w>0&&T+_>this.bottom&&(C=f.x=C+e[f.line].width+h,f.line++,T=f.y=K(o,this.top+y+h,this.bottom-e[f.line].height));const X=l.x(C);if(b(X,T,k),C=or(P,C+g+u,v?C+O:this.right,t.rtl),x(l.x(C),T,k),v)f.x+=O+h;else if(typeof k.text!="string"){const Q=c.lineHeight;f.y+=po(k,Q)+h}else f.y+=_}),$n(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=$(e.font),n=G(e.padding);if(!e.display)return;const o=$t(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=K(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+K(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=K(a,d,d+u);r.textAlign=o.textAlign(Wi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Bt(r,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=$(t.font),s=G(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(pt(t,this.left,this.right)&&pt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],pt(t,n.left,n.left+n.width)&&pt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Nc(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=zc(n,s);n&&!o&&F(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&F(e.onHover,[t,s,this],this)}else s&&F(e.onClick,[t,s,this],this)}}function Bc(i,t,e,s,n){const o=Vc(s,i,t,e),r=Wc(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Vc(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Wc(i,t,e){let s=i;return typeof t.text!="string"&&(s=po(t,e)),s}function po(i,t){const e=i.text?i.text.length:0;return t*e}function Nc(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Hc={id:"legend",_element:on,start(i,t,e){const s=i.legend=new on({ctx:i.ctx,options:e,chart:i});q.configure(i,s,e),q.addBox(i,s)},stop(i){q.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;q.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=G(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class Gi extends rt{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=V(s.text)?s.text.length:1;this._padding=G(s.padding);const o=n*$(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=K(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=K(a,n,e),l=z*-.5):(h=o-t,d=K(a,e,n),l=z*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=$(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Bt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:Wi(e.align),textBaseline:"middle",translation:[r,a]})}}function jc(i,t){const e=new Gi({ctx:i.ctx,options:t,chart:i});q.configure(i,e,t),q.addBox(i,e),i.titleBlock=e}var $c={id:"title",_element:Gi,start(i,t,e){jc(i,e)},stop(i){const t=i.titleBlock;q.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Fe=new WeakMap;var Yc={id:"subtitle",start(i,t,e){const s=new Gi({ctx:i.ctx,options:e,chart:i});q.configure(i,s,e),q.addBox(i,s),Fe.set(i,s)},stop(i){q.removeBox(i,Fe.get(i)),Fe.delete(i)},beforeUpdate(i,t,e){const s=Fe.get(i);q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const oe={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=_i(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function at(i,t){return t&&(V(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function ut(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Xc(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function rn(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=$(t.bodyFont),c=$(t.titleFont),h=$(t.footerFont),d=o.length,u=n.length,f=s.length,g=G(t.padding);let p=g.height,m=0,b=s.reduce((y,_)=>y+_.before.length+_.lines.length+_.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const y=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=f*y+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let x=0;const v=function(y){m=Math.max(m,e.measureText(y).width+x)};return e.save(),e.font=c.string,R(i.title,v),e.font=l.string,R(i.beforeBody.concat(i.afterBody),v),x=t.displayColors?r+2+t.boxPadding:0,R(s,y=>{R(y.before,v),R(y.lines,v),R(y.after,v)}),x=0,e.font=h.string,R(i.footer,v),e.restore(),m+=g.width,{width:m,height:p}}function Uc(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Kc(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function qc(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Kc(c,i,t,e)&&(c="center"),c}function an(i,t,e){const s=e.yAlign||t.yAlign||Uc(i,e);return{xAlign:e.xAlign||t.xAlign||qc(i,t,e,s),yAlign:s}}function Gc(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Zc(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function ln(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Ft(r);let g=Gc(t,a);const p=Zc(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,u)+n:a==="right"&&(g+=Math.max(d,f)+n),{x:Y(g,0,s.width-t.width),y:Y(p,0,s.height-t.height)}}function Ie(i,t,e){const s=G(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function cn(i){return at([],ut(i))}function Jc(i,t,e){return St(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function hn(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const mo={beforeTitle:ht,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:ht,beforeBody:ht,beforeLabel:ht,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return L(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ht,afterBody:ht,beforeFooter:ht,footer:ht,afterFooter:ht};function Z(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?mo[t].call(e,s):n}class Oi extends rt{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new Kn(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Jc(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=Z(s,"beforeTitle",this,t),o=Z(s,"title",this,t),r=Z(s,"afterTitle",this,t);let a=[];return a=at(a,ut(n)),a=at(a,ut(o)),a=at(a,ut(r)),a}getBeforeBody(t,e){return cn(Z(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return R(t,o=>{const r={before:[],lines:[],after:[]},a=hn(s,o);at(r.before,ut(Z(a,"beforeLabel",this,o))),at(r.lines,Z(a,"label",this,o)),at(r.after,ut(Z(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return cn(Z(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=Z(s,"beforeFooter",this,t),o=Z(s,"footer",this,t),r=Z(s,"afterFooter",this,t);let a=[];return a=at(a,ut(n)),a=at(a,ut(o)),a=at(a,ut(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Xc(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),R(a,h=>{const d=hn(t.callbacks,h);n.push(Z(d,"labelColor",this,h)),o.push(Z(d,"labelPointStyle",this,h)),r.push(Z(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=oe[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=rn(this,s),c=Object.assign({},a,l),h=an(this.chart,s,c),d=ln(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Ft(a),{x:u,y:f}=t,{width:g,height:p}=e;let m,b,x,v,y,_;return o==="center"?(y=f+p/2,n==="left"?(m=u,b=m-r,v=y+r,_=y-r):(m=u+g,b=m+r,v=y-r,_=y+r),x=m):(n==="left"?b=u+Math.max(l,h)+r:n==="right"?b=u+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(v=f,y=v-r,m=b-r,x=b+r):(v=f+p,y=v+r,m=b+r,x=b-r),_=v),{x1:m,x2:b,x3:x,y1:v,y2:y,y3:_}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=$t(s.rtl,this.x,this.width);for(t.x=Ie(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=$(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=$(o.bodyFont),d=Ie(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,vi(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,vi(t,p,m,b)}else{t.lineWidth=A(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=Ft(r.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,me(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),me(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=$(s.bodyFont);let u=d.lineHeight,f=0;const g=$t(s.rtl,this.x,this.width),p=function(S){e.fillText(S,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(r);let b,x,v,y,_,k,w;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=Ie(this,m,s),e.fillStyle=s.bodyColor,R(this.beforeBody,p),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,y=0,k=n.length;y<k;++y){for(b=n[y],x=this.labelTextColors[y],e.fillStyle=x,R(b.before,p),v=b.lines,a&&v.length&&(this._drawColorBox(e,t,y,g,s),u=Math.max(d.lineHeight,l)),_=0,w=v.length;_<w;++_)p(v[_]),u=d.lineHeight;R(b.after,p)}f=0,u=d.lineHeight,R(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=$t(s.rtl,this.x,this.width);for(t.x=Ie(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=$(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=Ft(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-u,l),e.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=oe[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=rn(this,t),l=Object.assign({},r,this._size),c=an(e,t,l),h=ln(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=G(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),jn(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),$n(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!Ue(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!Ue(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=oe[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}M(Oi,"positioners",oe);var Qc={id:"tooltip",_element:Oi,positioners:oe,afterInit(i,t,e){e&&(i.tooltip=new Oi({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:mo},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},th=Object.freeze({__proto__:null,Colors:uc,Decimation:mc,Filler:Ic,Legend:Hc,SubTitle:Yc,Title:$c,Tooltip:Qc});const eh=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function ih(i,t,e,s){const n=i.indexOf(t);if(n===-1)return eh(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const sh=(i,t)=>i===null?null:Y(Math.round(i),0,t);function dn(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ci extends Vt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(L(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:ih(s,t,D(e,t),this._addedLabels),sh(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return dn.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}M(Ci,"id","category"),M(Ci,"defaults",{ticks:{callback:dn}});function nh(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=t,b=!L(r),x=!L(a),v=!L(c),y=(m-p)/(d+1);let _=ns((m-p)/g/f)*f,k,w,S,P;if(_<1e-14&&!b&&!x)return[{value:p},{value:m}];P=Math.ceil(m/_)-Math.floor(p/_),P>g&&(_=ns(P*_/g/f)*f),L(l)||(k=Math.pow(10,l),_=Math.ceil(_*k)/k),n==="ticks"?(w=Math.floor(p/_)*_,S=Math.ceil(m/_)*_):(w=p,S=m),b&&x&&o&&Jo((a-r)/o,_/1e3)?(P=Math.round(Math.min((a-r)/_,h)),_=(a-r)/P,w=r,S=a):v?(w=b?r:w,S=x?a:S,P=c-1,_=(S-w)/P):(P=(S-w)/_,le(P,Math.round(P),_/1e3)?P=Math.round(P):P=Math.ceil(P));const O=Math.max(os(_),os(w));k=Math.pow(10,L(l)?O:l),w=Math.round(w*k)/k,S=Math.round(S*k)/k;let C=0;for(b&&(u&&w!==r?(e.push({value:r}),w<r&&C++,le(Math.round((w+C*_)*k)/k,r,un(r,y,i))&&C++):w<r&&C++);C<P;++C){const T=Math.round((w+C*_)*k)/k;if(x&&T>a)break;e.push({value:T})}return x&&u&&S!==a?e.length&&le(e[e.length-1].value,a,un(a,y,i))?e[e.length-1].value=a:e.push({value:a}):(!x||S===a)&&e.push({value:S}),e}function un(i,t,{horizontal:e,minRotation:s}){const n=nt(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class ti extends Vt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return L(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=ct(n),c=ct(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=nh(n,o);return t.bounds==="ticks"&&Pn(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return ve(t,this.chart.options.locale,this.options.ticks.format)}}class Ai extends ti{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?t:0,this.max=W(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=nt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}M(Ai,"id","linear"),M(Ai,"defaults",{ticks:{callback:ei.formatters.numeric}});const xe=i=>Math.floor(_t(i)),Tt=(i,t)=>Math.pow(10,xe(i)+t);function fn(i){return i/Math.pow(10,xe(i))===1}function gn(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function oh(i,t){const e=t-i;let s=xe(e);for(;gn(i,t,s)>10;)s++;for(;gn(i,t,s)<10;)s--;return Math.min(s,xe(i))}function rh(i,{min:t,max:e}){t=tt(i.min,t);const s=[],n=xe(t);let o=oh(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=tt(i.min,Math.round((l+h+d*Math.pow(10,o))*r)/r);for(;u<e;)s.push({value:u,major:fn(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),u=Math.round((l+h+d*Math.pow(10,o))*r)/r;const f=tt(i.max,u);return s.push({value:f,major:fn(f),significand:d}),s}class Ti extends Vt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=ti.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return W(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?Math.max(0,t):null,this.max=W(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!W(this._userMin)&&(this.min=t===Tt(this.min,0)?Tt(this.min,-1):Tt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=a=>s=t?s:a,r=a=>n=e?n:a;s===n&&(s<=0?(o(1),r(10)):(o(Tt(s,-1)),r(Tt(n,1)))),s<=0&&o(Tt(n,-1)),n<=0&&r(Tt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=rh(e,this);return t.bounds==="ticks"&&Pn(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":ve(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=_t(t),this._valueRange=_t(this.max)-_t(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(_t(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}M(Ti,"id","logarithmic"),M(Ti,"defaults",{ticks:{callback:ei.formatters.logarithmic,major:{enabled:!0}}});function Li(i){const t=i.ticks;if(t.display&&i.display){const e=G(t.backdropPadding);return D(t.font&&t.font.size,N.font.size)+e.height}return 0}function ah(i,t,e){return e=V(e)?e:[e],{w:pr(i,t.string,e),h:e.length*t.lineHeight}}function pn(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function lh(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,r=i.options.pointLabels,a=r.centerPointLabels?z/o:0;for(let l=0;l<o;l++){const c=r.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],a),d=$(c.font),u=ah(i.ctx,d,i._pointLabels[l]);s[l]=u;const f=et(i.getIndexAngle(l)+a),g=Math.round(Bi(f)),p=pn(g,h.x,u.w,0,180),m=pn(g,h.y,u.h,90,270);ch(e,t,f,p,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=uh(i,s,n)}function ch(i,t,e,s,n){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,i.b=Math.max(i.b,t.b+l))}function hh(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=e,l=i.getPointPosition(t,s+n+r,o),c=Math.round(Bi(et(l.angle+j))),h=ph(l.y,a.h,c),d=fh(c),u=gh(l.x,a.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+a.w,bottom:h+a.h}}function dh(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(bt({x:e,y:s},t)||bt({x:e,y:o},t)||bt({x:n,y:s},t)||bt({x:n,y:o},t))}function uh(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Li(o)/2,additionalAngle:r?z/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const d=hh(i,h,l);s.push(d),a==="auto"&&(d.visible=dh(d,c),d.visible&&(c=d))}return s}function fh(i){return i===0||i===180?"center":i<180?"left":"right"}function gh(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function ph(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function mh(i,t,e){const{left:s,top:n,right:o,bottom:r}=e,{backdropColor:a}=t;if(!L(a)){const l=Ft(t.borderRadius),c=G(t.backdropPadding);i.fillStyle=a;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=r-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),me(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function bh(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const r=s.setContext(i.getPointLabelContext(n));mh(e,r,o);const a=$(r.font),{x:l,y:c,textAlign:h}=o;Bt(e,i._pointLabels[n],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function bo(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,I);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=i.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function xh(i,t,e,s,n){const o=i.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash),o.lineDashOffset=n.dashOffset,o.beginPath(),bo(i,e,r,s),o.closePath(),o.stroke(),o.restore())}function _h(i,t,e){return St(i,{label:e,index:t,type:"pointLabel"})}class re extends ti{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=G(Li(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=W(t)&&!isNaN(t)?t:0,this.max=W(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Li(this.options))}generateTickLabels(t){ti.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=F(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?lh(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=I/(this._pointLabels.length||1),s=this.options.startAngle||0;return et(t*e+nt(s))}getDistanceFromCenterForValue(t){if(L(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(L(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return _h(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-j+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),bo(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,r=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&bh(this,r),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);xh(this,f,l,r,g)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const h=s.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=$(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const d=G(c.backdropPadding);t.fillRect(-r/2-d.left,-o-h.size/2-d.top,r+d.width,h.size+d.height)}Bt(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}M(re,"id","radialLinear"),M(re,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ei.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),M(re,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),M(re,"descriptors",{angleLines:{_fallback:"grid"}});const oi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},J=Object.keys(oi);function mn(i,t){return i-t}function bn(i,t){if(L(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),W(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Yt(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function xn(i,t,e,s){const n=J.length;for(let o=J.indexOf(i);o<n-1;++o){const r=oi[J[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return J[o]}return J[n-1]}function yh(i,t,e,s,n){for(let o=J.length-1;o>=J.indexOf(e);o--){const r=J[o];if(oi[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return J[e?J.indexOf(e):0]}function vh(i){for(let t=J.indexOf(i)+1,e=J.length;t<e;++t)if(oi[J[t]].common)return J[t]}function _n(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Vi(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Mh(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function yn(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Mh(i,s,n,e)}class _e extends Vt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Ta._date(t.adapters.date);n.init(e),ae(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:bn(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=W(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=W(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=ir(n,o,r);return this._unit=e.unit||(s.autoSkip?xn(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):yh(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:vh(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),yn(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=Y(e,0,r),s=Y(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||xn(o.minUnit,e,s,this._getLabelCapacity(e)),a=D(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Yt(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,a,r),f++)_n(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&_n(h,u,g),Object.keys(h).sort(mn).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return F(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=nt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,yn(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(bn(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Cn(t.sort(mn))}}M(_e,"id","time"),M(_e,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function ze(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=mt(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=mt(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Ri extends _e{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=ze(e,this.min),this._tableRange=ze(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(ze(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return ze(this._table,s*this._tableRange+this._minPos,!0)}}M(Ri,"id","timeseries"),M(Ri,"defaults",_e.defaults);var kh=Object.freeze({__proto__:null,CategoryScale:Ci,LinearScale:Ai,LogarithmicScale:Ti,RadialLinearScale:re,TimeScale:_e,TimeSeriesScale:Ri});const wh=[Aa,rc,th,kh];gt.register(...wh);try{window.Chart=gt}catch{}
