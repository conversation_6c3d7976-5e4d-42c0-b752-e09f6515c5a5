$(function(){var c=$("#section-block"),o=$(".btn-section-block"),t=$(".btn-section-block-overlay"),r=$(".btn-section-block-spinner"),a=$(".btn-section-block-custom"),l=$(".btn-section-block-multiple"),e=$("#card-block"),i=$(".btn-card-block"),n=$(".btn-card-block-overlay"),v=$(".btn-card-block-spinner"),d=$(".btn-card-block-custom"),k=$(".btn-card-block-multiple"),b=$(".btn-page-block"),f=$(".btn-page-block-overlay"),u=$(".btn-page-block-spinner"),p=$(".btn-page-block-custom"),m=$(".btn-page-block-multiple"),s=$(".form-block"),g=$(".btn-form-block"),w=$(".btn-form-block-overlay"),y=$(".btn-form-block-spinner"),S=$(".btn-form-block-custom"),C=$(".btn-form-block-multiple");o.length&&c.length&&o.on("click",function(){$("#section-block").block({message:'<div class="spinner-border text-white" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.5}})}),t.length&&c.length&&t.on("click",function(){$("#section-block").block({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#fff",opacity:.8}})}),r.length&&c.length&&r.on("click",function(){$("#section-block").block({message:'<div class="sk-wave mx-auto"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.5}})}),a.length&&c.length&&a.on("click",function(){$("#section-block").block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),l.length&&c.length&&l.on("click",function(){$("#section-block").block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5},timeout:1e3,onUnblock:function(){$("#section-block").block({message:'<p class="mb-0">Almost Done...</p>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.25},onUnblock:function(){$("#section-block").block({message:'<div class="p-3 bg-success">Success</div>',timeout:500,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.25}})}})}})}),i.length&&e.length&&i.on("click",function(){$("#card-block").block({message:'<div class="spinner-border text-white" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.5}})}),n.length&&e.length&&n.on("click",function(){$("#card-block").block({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#fff",opacity:.8}})}),v.length&&e.length&&v.on("click",function(){$("#card-block").block({message:'<div class="sk-wave mx-auto"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),d.length&&e.length&&d.on("click",function(){$("#card-block").block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),k.length&&e.length&&k.on("click",function(){$("#card-block").block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5},timeout:1e3,onUnblock:function(){$("#card-block").block({message:'<p class="mb-0">Almost Done...</p>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.25},onUnblock:function(){$("#card-block").block({message:'<div class="p-3 bg-success">Success</div>',timeout:500,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.25}})}})}})}),b.length&&b.on("click",function(){$.blockUI({message:'<div class="spinner-border text-white" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.5}})}),f.length&&f.on("click",function(){$.blockUI({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#fff",opacity:.8}})}),u.length&&u.on("click",function(){$.blockUI({message:'<div class="sk-wave mx-auto"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.5}})}),p.length&&p.on("click",function(){$.blockUI({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),m.length&&m.on("click",function(){$.blockUI({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5},timeout:1e3,onUnblock:function(){$.blockUI({message:'<p class="mb-0">Almost Done...</p>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5},onUnblock:function(){$.blockUI({message:'<div class="p-3 bg-success">Success</div>',timeout:500,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}})}})}),g.length&&s.length&&g.on("click",function(){s.block({message:'<div class="spinner-border text-white" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),w.length&&s.length&&w.on("click",function(){s.block({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#fff",opacity:.8}})}),y.length&&s.length&&y.on("click",function(){s.block({message:'<div class="sk-wave mx-auto"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),S.length&&s.length&&S.on("click",function(){s.block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',timeout:1e3,css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5}})}),C.length&&s.length&&C.on("click",function(){s.block({message:'<div class="d-flex justify-content-center"><p class="mb-0">Please wait...</p> <div class="sk-wave m-0"><div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div> <div class="sk-rect sk-wave-rect"></div></div> </div>',css:{backgroundColor:"transparent",color:"#fff",border:"0"},overlayCSS:{opacity:.5},timeout:1e3,onUnblock:function(){s.block({message:'<p class="mb-0">Almost Done...</p>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.25},onUnblock:function(){s.block({message:'<div class="p-3 bg-success">Success</div>',timeout:500,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{opacity:.25}})}})}})})});
