(function(){window.Helpers.initCustomOptionCheck();const l=[].slice.call(document.querySelectorAll(".flatpickr-validation"));l&&l.forEach(a=>{a.flatpickr({allowInput:!0,monthSelectorType:"static"})});const o=document.querySelectorAll(".needs-validation");Array.prototype.slice.call(o).forEach(function(a){a.addEventListener("submit",function(i){a.checkValidity()?alert("Submitted!!!"):(i.preventDefault(),i.stopPropagation()),a.classList.add("was-validated")},!1)})})();document.addEventListener("DOMContentLoaded",function(l){(function(){const o=document.getElementById("formValidationExamples"),a=jQuery(o.querySelector('[name="formValidationSelect2"]')),i=jQuery(o.querySelector('[name="formValidationTech"]')),r=o.querySelector('[name="formValidationLang"]'),p=jQuery(o.querySelector(".selectpicker")),g=["ReactJS","Angular","VueJS","Html","Css","Sass","Pug","Gulp","Php","Laravel","Python","Bootstrap","Material Design","NodeJS"],n=FormValidation.formValidation(o,{fields:{formValidationName:{validators:{notEmpty:{message:"Please enter your name"},stringLength:{min:6,max:30,message:"The name must be more than 6 and less than 30 characters long"},regexp:{regexp:/^[a-zA-Z0-9 ]+$/,message:"The name can only consist of alphabetical, number and space"}}},formValidationEmail:{validators:{notEmpty:{message:"Please enter your email"},emailAddress:{message:"The value is not a valid email address"}}},formValidationPass:{validators:{notEmpty:{message:"Please enter your password"}}},formValidationConfirmPass:{validators:{notEmpty:{message:"Please confirm password"},identical:{compare:function(){return o.querySelector('[name="formValidationPass"]').value},message:"The password and its confirm are not the same"}}},formValidationFile:{validators:{notEmpty:{message:"Please select the file"}}},formValidationDob:{validators:{notEmpty:{message:"Please select your DOB"},date:{format:"YYYY/MM/DD",message:"The value is not a valid date"}}},formValidationSelect2:{validators:{notEmpty:{message:"Please select your country"}}},formValidationLang:{validators:{notEmpty:{message:"Please add your language"}}},formValidationTech:{validators:{notEmpty:{message:"Please select technology"}}},formValidationHobbies:{validators:{notEmpty:{message:"Please select your hobbies"}}},formValidationBio:{validators:{notEmpty:{message:"Please enter your bio"},stringLength:{min:100,max:500,message:"The bio must be more than 100 and less than 500 characters long"}}},formValidationGender:{validators:{notEmpty:{message:"Please select your gender"}}},formValidationPlan:{validators:{notEmpty:{message:"Please select your preferred plan"}}},formValidationSwitch:{validators:{notEmpty:{message:"Please select your preference"}}},formValidationCheckbox:{validators:{notEmpty:{message:"Please confirm our T&C"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(t,e){switch(t){case"formValidationName":case"formValidationEmail":case"formValidationPass":case"formValidationConfirmPass":case"formValidationFile":case"formValidationDob":case"formValidationSelect2":case"formValidationLang":case"formValidationTech":case"formValidationHobbies":case"formValidationBio":case"formValidationGender":return".col-md-6";case"formValidationPlan":return".col-xl-3";case"formValidationSwitch":case"formValidationCheckbox":return".col-12";default:return".row"}}}),submitButton:new FormValidation.plugins.SubmitButton,defaultSubmit:new FormValidation.plugins.DefaultSubmit,autoFocus:new FormValidation.plugins.AutoFocus},init:t=>{t.on("plugins.message.placed",function(e){e.element.parentElement.classList.contains("input-group")&&e.element.parentElement.insertAdjacentElement("afterend",e.messageElement),e.element.parentElement.parentElement.classList.contains("custom-option")&&e.element.closest(".row").insertAdjacentElement("afterend",e.messageElement)})}}),m=document.querySelector('[name="formValidationDob"]');m&&m.flatpickr({enableTime:!1,dateFormat:"Y/m/d",onChange:function(){n.revalidateField("formValidationDob")}}),a.length&&(a.wrap('<div class="position-relative"></div>'),a.select2({placeholder:"Select country",dropdownParent:a.parent()}).on("change",function(){n.revalidateField("formValidationSelect2")}));const h=function(t){return function(d,c){var s,u;s=[],u=new RegExp(d,"i"),$.each(t,function(y,f){u.test(f)&&s.push(f)}),c(s)}};if(isRtl){const t=[].slice.call(document.querySelectorAll(".typeahead"));t&&t.forEach(e=>{e.setAttribute("dir","rtl")})}i.typeahead({hint:!isRtl,highlight:!0,minLength:1},{name:"tech",source:h(g)}),new Tagify(r),r.addEventListener("change",V);function V(){n.revalidateField("formValidationLang")}p.on("changed.bs.select",function(t,e,d,c){n.revalidateField("formValidationHobbies")})})()});
