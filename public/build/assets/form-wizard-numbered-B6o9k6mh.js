$(function(){const r=$(".select2"),a=$(".selectpicker");a.length&&a.selectpicker(),r.length&&r.each(function(){var n=$(this);n.wrap('<div class="position-relative"></div>'),n.select2({placeholder:"Select value",dropdownParent:n.parent()})})});(function(){const r=document.querySelector(".wizard-numbered"),a=[].slice.call(r.querySelectorAll(".btn-next")),n=[].slice.call(r.querySelectorAll(".btn-prev")),o=r.querySelector(".btn-submit");if(typeof r!==void 0&&r!==null){const t=new Stepper(r,{linear:!1});a&&a.forEach(e=>{e.addEventListener("click",i=>{t.next()})}),n&&n.forEach(e=>{e.addEventListener("click",i=>{t.previous()})}),o&&o.addEventListener("click",e=>{alert("Submitted..!!")})}const l=document.querySelector(".wizard-vertical"),s=[].slice.call(l.querySelectorAll(".btn-next")),u=[].slice.call(l.querySelectorAll(".btn-prev")),v=l.querySelector(".btn-submit");if(typeof l!==void 0&&l!==null){const t=new Stepper(l,{linear:!1});s&&s.forEach(e=>{e.addEventListener("click",i=>{t.next()})}),u&&u.forEach(e=>{e.addEventListener("click",i=>{t.previous()})}),v&&v.addEventListener("click",e=>{alert("Submitted..!!")})}const c=document.querySelector(".wizard-modern-example"),f=[].slice.call(c.querySelectorAll(".btn-next")),p=[].slice.call(c.querySelectorAll(".btn-prev")),S=c.querySelector(".btn-submit");if(typeof c!==void 0&&c!==null){const t=new Stepper(c,{linear:!1});f&&f.forEach(e=>{e.addEventListener("click",i=>{t.next()})}),p&&p.forEach(e=>{e.addEventListener("click",i=>{t.previous()})}),S&&S.addEventListener("click",e=>{alert("Submitted..!!")})}const d=document.querySelector(".wizard-modern-vertical"),b=[].slice.call(d.querySelectorAll(".btn-next")),m=[].slice.call(d.querySelectorAll(".btn-prev")),w=d.querySelector(".btn-submit");if(typeof d!==void 0&&d!==null){const t=new Stepper(d,{linear:!1});b&&b.forEach(e=>{e.addEventListener("click",i=>{t.next()})}),m&&m.forEach(e=>{e.addEventListener("click",i=>{t.previous()})}),w&&w.addEventListener("click",e=>{alert("Submitted..!!")})}})();
