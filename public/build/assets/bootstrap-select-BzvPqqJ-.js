(function(p){var me=["sanitize","whiteList","sanitizeFn"],ve=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],ge=/^aria-[\w-]*$/i,be={"*":["class","dir","id","lang","role","tabindex","style",ge],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},we=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,ke=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i,xe=["title","placeholder"];function Ee(e,i){var s=e.nodeName.toLowerCase();if(p.inArray(s,i)!==-1)return p.inArray(s,ve)!==-1?!!(e.nodeValue.match(we)||e.nodeValue.match(ke)):!0;for(var t=p(i).filter(function(r,c){return c instanceof RegExp}),n=0,o=t.length;n<o;n++)if(s.match(t[n]))return!0;return!1}function ie(e,i,s){if(s&&typeof s=="function")return s(e);for(var t=Object.keys(i),n=0,o=e.length;n<o;n++)for(var r=e[n].querySelectorAll("*"),c=0,l=r.length;c<l;c++){var a=r[c],d=a.nodeName.toLowerCase();if(t.indexOf(d)===-1){a.parentNode.removeChild(a);continue}for(var g=[].slice.call(a.attributes),f=[].concat(i["*"]||[],i[d]||[]),w=0,k=g.length;w<k;w++){var I=g[w];Ee(I,f)||a.removeAttribute(I.nodeName)}}}function se(e){var i={},s;return xe.forEach(function(t){s=e.attr(t),s&&(i[t]=s)}),!i.placeholder&&i.title&&(i.placeholder=i.title),i}"classList"in document.createElement("_")||function(e){if("Element"in e){var i="classList",s="prototype",t=e.Element[s],n=Object,o=function(){var c=p(this);return{add:function(l){return l=Array.prototype.slice.call(arguments).join(" "),c.addClass(l)},remove:function(l){return l=Array.prototype.slice.call(arguments).join(" "),c.removeClass(l)},toggle:function(l,a){return c.toggleClass(l,a)},contains:function(l){return c.hasClass(l)}}};if(n.defineProperty){var r={get:o,enumerable:!0,configurable:!0};try{n.defineProperty(t,i,r)}catch(c){(c.number===void 0||c.number===-2146823252)&&(r.enumerable=!1,n.defineProperty(t,i,r))}}else n[s].__defineGetter__&&t.__defineGetter__(i,o)}}(window);var G=document.createElement("_");if(G.classList.add("c1","c2"),!G.classList.contains("c2")){var Ie=DOMTokenList.prototype.add,ye=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){Array.prototype.forEach.call(arguments,Ie.bind(this))},DOMTokenList.prototype.remove=function(){Array.prototype.forEach.call(arguments,ye.bind(this))}}if(G.classList.toggle("c3",!1),G.classList.contains("c3")){var Se=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,i){return 1 in arguments&&!this.contains(e)==!i?i:Se.call(this,e)}}G=null,Object.values=typeof Object.values=="function"?Object.values:function(e){return Object.keys(e).map(function(i){return e[i]})};function Ce(e,i){return e.length===i.length&&e.every(function(s,t){return s===i[t]})}String.prototype.startsWith||function(){var e={}.toString,i=function(s){if(this==null)throw new TypeError;var t=String(this);if(s&&e.call(s)=="[object RegExp]")throw new TypeError;var n=t.length,o=String(s),r=o.length,c=arguments.length>1?arguments[1]:void 0,l=c?Number(c):0;l!=l&&(l=0);var a=Math.min(Math.max(l,0),n);if(r+a>n)return!1;for(var d=-1;++d<r;)if(t.charCodeAt(a+d)!=o.charCodeAt(d))return!1;return!0};Object.defineProperty?Object.defineProperty(String.prototype,"startsWith",{value:i,configurable:!0,writable:!0}):String.prototype.startsWith=i}();function Ae(e){return e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,function(i,s){return(s?"-":"")+i.toLowerCase()})}function Q(){var e=this.selectpicker.main.data;(this.options.source.data||this.options.source.search)&&(e=Object.values(this.selectpicker.optionValuesDataMap));var i=e.filter(function(t){return t.selected?!(this.options.hideDisabled&&t.disabled):!1},this);if(this.options.source.data&&!this.multiple&&i.length>1){for(var s=0;s<i.length-1;s++)i[s].selected=!1;i=[i[i.length-1]]}return i}function Y(e){for(var i=[],s=e||Q.call(this),t,n=0,o=s.length;n<o;n++)t=s[n],t.disabled||i.push(t.value===void 0?t.text:t.value);return this.multiple?i:i.length?i[0]:null}var K={useDefault:!1,_set:p.valHooks.select.set};p.valHooks.select.set=function(e,i){return i&&!K.useDefault&&p(e).data("selected",!0),K._set.apply(this,arguments)};var B=null,Te=function(){try{return new Event("change"),!0}catch{return!1}}();p.fn.triggerNative=function(e){var i=this[0],s;i.dispatchEvent&&(Te?s=new Event(e,{bubbles:!0}):(s=document.createEvent("Event"),s.initEvent(e,!0,!1)),i.dispatchEvent(s))};function ne(e,i,s,t){for(var n=["display","subtext","tokens"],o=!1,r=0;r<n.length;r++){var c=n[r],l=e[c];if(l&&(l=l.toString(),c==="display"&&(l=l.replace(/<[^>]+>/g,"")),t&&(l=oe(l)),l=l.toUpperCase(),typeof s=="function"?o=s(l,i):s==="contains"?o=l.indexOf(i)>=0:o=l.startsWith(i),o))break}return o}function P(e){return parseInt(e,10)||0}var $e={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Oe=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ze="\\u0300-\\u036f",Le="\\ufe20-\\ufe2f",De="\\u20d0-\\u20ff",Ne="\\u1ab0-\\u1aff",He="\\u1dc0-\\u1dff",Pe=ze+Le+De+Ne+He,Re="["+Pe+"]",We=RegExp(Re,"g");function Me(e){return $e[e]}function oe(e){return e=e.toString(),e&&e.replace(Oe,Me).replace(We,"")}var Be={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},Ve=function(e){var i=function(o){return e[o]},s="(?:"+Object.keys(e).join("|")+")",t=RegExp(s),n=RegExp(s,"g");return function(o){return o=o==null?"":""+o,t.test(o)?o.replace(n,i):o}},Z=Ve(Be),Ue={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},D={ESCAPE:27,ENTER:13,SPACE:32,TAB:9,ARROW_UP:38,ARROW_DOWN:40},te=window.Dropdown||bootstrap.Dropdown;function re(){var e;try{e=p.fn.dropdown.Constructor.VERSION}catch{e=te.VERSION}return e}var S={success:!1,major:"3"};try{S.full=(re()||"").split(" ")[0].split("."),S.major=S.full[0],S.success=!0}catch{}var le=0,b=".bs.select",h={DISABLED:"disabled",DIVIDER:"divider",SHOW:"open",DROPUP:"dropup",MENU:"dropdown-menu",MENURIGHT:"dropdown-menu-right",MENULEFT:"dropdown-menu-left",BUTTONCLASS:"btn-default",POPOVERHEADER:"popover-title",ICONBASE:"glyphicon",TICKICON:"glyphicon-ok"},_={MENU:"."+h.MENU,DATA_TOGGLE:'data-toggle="dropdown"'},v={div:document.createElement("div"),span:document.createElement("span"),i:document.createElement("i"),subtext:document.createElement("small"),a:document.createElement("a"),li:document.createElement("li"),whitespace:document.createTextNode(" "),fragment:document.createDocumentFragment(),option:document.createElement("option")};v.selectedOption=v.option.cloneNode(!1),v.selectedOption.setAttribute("selected",!0),v.noResults=v.li.cloneNode(!1),v.noResults.className="no-results",v.a.setAttribute("role","option"),v.a.className="dropdown-item",v.subtext.className="text-muted",v.text=v.span.cloneNode(!1),v.text.className="text",v.checkMark=v.span.cloneNode(!1);var _e=new RegExp(D.ARROW_UP+"|"+D.ARROW_DOWN),je=new RegExp("^"+D.TAB+"$|"+D.ESCAPE),R={li:function(e,i,s){var t=v.li.cloneNode(!1);return e&&(e.nodeType===1||e.nodeType===11?t.appendChild(e):t.innerHTML=e),typeof i<"u"&&i!==""&&(t.className=i),typeof s<"u"&&s!==null&&t.classList.add("optgroup-"+s),t},a:function(e,i,s){var t=v.a.cloneNode(!0);return e&&(e.nodeType===11?t.appendChild(e):t.insertAdjacentHTML("beforeend",e)),typeof i<"u"&&i!==""&&t.classList.add.apply(t.classList,i.split(/\s+/)),s&&t.setAttribute("style",s),t},text:function(e,i){var s=v.text.cloneNode(!1),t,n;if(e.content)s.innerHTML=e.content;else{if(s.textContent=e.text,e.icon){var o=v.whitespace.cloneNode(!1);n=(i===!0?v.i:v.span).cloneNode(!1),n.className=this.options.iconBase+" "+e.icon,v.fragment.appendChild(n),v.fragment.appendChild(o)}e.subtext&&(t=v.subtext.cloneNode(!1),t.textContent=e.subtext,s.appendChild(t))}if(i===!0)for(;s.childNodes.length>0;)v.fragment.appendChild(s.childNodes[0]);else v.fragment.appendChild(s);return v.fragment},label:function(e){var i=v.text.cloneNode(!1),s,t;if(i.innerHTML=e.display,e.icon){var n=v.whitespace.cloneNode(!1);t=v.span.cloneNode(!1),t.className=this.options.iconBase+" "+e.icon,v.fragment.appendChild(t),v.fragment.appendChild(n)}return e.subtext&&(s=v.subtext.cloneNode(!1),s.textContent=e.subtext,i.appendChild(s)),v.fragment.appendChild(i),v.fragment}},X={fromOption:function(e,i){var s;switch(i){case"divider":s=e.getAttribute("data-divider")==="true";break;case"text":s=e.textContent;break;case"label":s=e.label;break;case"style":s=e.style.cssText;break;case"title":s=e.title;break;default:s=e.getAttribute("data-"+Ae(i));break}return s},fromDataSource:function(e,i){var s;switch(i){case"text":case"label":s=e.text||e.value||"";break;default:s=e[i];break}return s}};function ae(e,i){e.length||(v.noResults.innerHTML=this.options.noneResultsText.replace("{0}",'"'+Z(i)+'"'),this.$menuInner[0].firstChild.appendChild(v.noResults))}function ce(e){return!(e.hidden||this.options.hideDisabled&&e.disabled)}var A=function(e,i){var s=this;K.useDefault||(p.valHooks.select.set=K._set,K.useDefault=!0),this.$element=p(e),this.$newElement=null,this.$button=null,this.$menu=null,this.options=i,this.selectpicker={main:{data:[],optionQueue:v.fragment.cloneNode(!1),hasMore:!1},search:{data:[],hasMore:!1},current:{},view:{},optionValuesDataMap:{},isSearching:!1,keydown:{keyHistory:"",resetKeyHistory:{start:function(){return setTimeout(function(){s.selectpicker.keydown.keyHistory=""},800)}}}},this.sizeInfo={};var t=this.options.windowPadding;typeof t=="number"&&(this.options.windowPadding=[t,t,t,t]),this.val=A.prototype.val,this.render=A.prototype.render,this.refresh=A.prototype.refresh,this.setStyle=A.prototype.setStyle,this.selectAll=A.prototype.selectAll,this.deselectAll=A.prototype.deselectAll,this.destroy=A.prototype.destroy,this.remove=A.prototype.remove,this.show=A.prototype.show,this.hide=A.prototype.hide,this.init()};A.VERSION="1.14.0-beta3",A.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(e,i){return e==1?"{0} item selected":"{0} items selected"},maxOptionsText:function(e,i){return[e==1?"Limit reached ({n} item max)":"Limit reached ({n} items max)",i==1?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",source:{pageSize:40},chunkSize:40,doneButton:!1,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:h.BUTTONCLASS,size:"auto",title:null,placeholder:null,allowClear:!1,selectedTextFormat:"values",width:!1,container:!1,hideDisabled:!1,showSubtext:!1,showIcon:!0,showContent:!0,dropupAuto:!0,header:!1,liveSearch:!1,liveSearchPlaceholder:null,liveSearchNormalize:!1,liveSearchStyle:"contains",actionsBox:!1,iconBase:h.ICONBASE,tickIcon:h.TICKICON,showTick:!1,template:{caret:'<span class="caret"></span>'},maxOptions:!1,mobile:!1,selectOnTab:!0,dropdownAlignRight:!1,windowPadding:0,virtualScroll:600,display:!1,sanitize:!0,sanitizeFn:null,whiteList:be},A.prototype={constructor:A,init:function(){var e=this,i=this.$element.attr("id"),s=this.$element[0],t=s.form;le++,this.selectId="bs-select-"+le,s.classList.add("bs-select-hidden"),this.multiple=this.$element.prop("multiple"),this.autofocus=this.$element.prop("autofocus"),s.classList.contains("show-tick")&&(this.options.showTick=!0),this.$newElement=this.createDropdown(),this.$element.after(this.$newElement).prependTo(this.$newElement),t&&s.form===null&&(t.id||(t.id="form-"+this.selectId),s.setAttribute("form",t.id)),this.$button=this.$newElement.children("button"),this.options.allowClear&&(this.$clearButton=this.$button.children(".bs-select-clear-selected")),this.$menu=this.$newElement.children(_.MENU),this.$menuInner=this.$menu.children(".inner"),this.$searchbox=this.$menu.find("input"),s.classList.remove("bs-select-hidden"),this.fetchData(function(){e.render(!0),e.buildList(),requestAnimationFrame(function(){e.$element.trigger("loaded"+b)})}),this.options.dropdownAlignRight===!0&&this.$menu[0].classList.add(h.MENURIGHT),typeof i<"u"&&this.$button.attr("data-id",i),this.checkDisabled(),this.clickListener(),S.major>4&&(this.dropdown=new te(this.$button[0])),this.options.liveSearch?(this.liveSearchListener(),this.focusedParent=this.$searchbox[0]):this.focusedParent=this.$menuInner[0],this.setStyle(),this.setWidth(),this.options.container?this.selectPosition():this.$element.on("hide"+b,function(){if(e.isVirtual()){var n=e.$menuInner[0],o=n.firstChild.cloneNode(!1);n.replaceChild(o,n.firstChild),n.scrollTop=0}}),this.$menu.data("this",this),this.$newElement.data("this",this),this.options.mobile&&this.mobile(),this.$newElement.on({"hide.bs.dropdown":function(n){e.$element.trigger("hide"+b,n)},"hidden.bs.dropdown":function(n){e.$element.trigger("hidden"+b,n)},"show.bs.dropdown":function(n){e.$element.trigger("show"+b,n)},"shown.bs.dropdown":function(n){e.$element.trigger("shown"+b,n)}}),s.hasAttribute("required")&&this.$element.on("invalid"+b,function(){e.$button[0].classList.add("bs-invalid"),e.$element.on("shown"+b+".invalid",function(){e.$element.val(e.$element.val()).off("shown"+b+".invalid")}).on("rendered"+b,function(){this.validity.valid&&e.$button[0].classList.remove("bs-invalid"),e.$element.off("rendered"+b)}),e.$button.on("blur"+b,function(){e.$element.trigger("focus").trigger("blur"),e.$button.off("blur"+b)})}),t&&p(t).on("reset"+b,function(){requestAnimationFrame(function(){e.render()})})},createDropdown:function(){var e=this.multiple||this.options.showTick?" show-tick":"",i=this.multiple?' aria-multiselectable="true"':"",s="",t=this.autofocus?" autofocus":"";S.major<4&&this.$element.parent().hasClass("input-group")&&(s=" input-group-btn");var n,o="",r="",c="",l="",a="";return this.options.header&&(o='<div class="'+h.POPOVERHEADER+'"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>"),this.options.liveSearch&&(r='<div class="bs-searchbox"><input type="search" class="form-control" autocomplete="off"'+(this.options.liveSearchPlaceholder===null?"":' placeholder="'+Z(this.options.liveSearchPlaceholder)+'"')+' role="combobox" aria-label="Search" aria-controls="'+this.selectId+'" aria-autocomplete="list"></div>'),this.multiple&&this.options.actionsBox&&(c='<div class="bs-actionsbox"><div class="btn-group btn-group-sm"><button type="button" class="actions-btn bs-select-all btn '+h.BUTTONCLASS+'">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn '+h.BUTTONCLASS+'">'+this.options.deselectAllText+"</button></div></div>"),this.multiple&&this.options.doneButton&&(l='<div class="bs-donebutton"><div class="btn-group"><button type="button" class="btn btn-sm '+h.BUTTONCLASS+'">'+this.options.doneButtonText+"</button></div></div>"),this.options.allowClear&&(a='<span class="close bs-select-clear-selected" title="'+this.options.deselectAllText+'"><span>&times;</span>'),n='<div class="dropdown bootstrap-select'+e+s+'"><button type="button" tabindex="-1" class="'+this.options.styleBase+' dropdown-toggle" '+(this.options.display==="static"?'data-display="static"':"")+_.DATA_TOGGLE+t+' role="combobox" aria-owns="'+this.selectId+'" aria-haspopup="listbox" aria-expanded="false"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">&nbsp;</div></div> </div>'+a+"</span>"+(S.major>="4"?"":'<span class="bs-caret">'+this.options.template.caret+"</span>")+'</button><div class="'+h.MENU+" "+(S.major>="4"?"":h.SHOW)+'">'+o+r+c+'<div class="inner '+h.SHOW+'" role="listbox" id="'+this.selectId+'" tabindex="-1" '+i+'><ul class="'+h.MENU+" inner "+(S.major>="4"?h.SHOW:"")+'" role="presentation"></ul></div>'+l+"</div></div>",p(n)},setPositionData:function(){this.selectpicker.view.canHighlight=[],this.selectpicker.view.size=0,this.selectpicker.view.firstHighlightIndex=!1;for(var e=0;e<this.selectpicker.current.data.length;e++){var i=this.selectpicker.current.data[e],s=!0;i.type==="divider"?(s=!1,i.height=this.sizeInfo.dividerHeight):i.type==="optgroup-label"?(s=!1,i.height=this.sizeInfo.dropdownHeaderHeight):i.height=this.sizeInfo.liHeight,i.disabled&&(s=!1),this.selectpicker.view.canHighlight.push(s),s&&(this.selectpicker.view.size++,i.posinset=this.selectpicker.view.size,this.selectpicker.view.firstHighlightIndex===!1&&(this.selectpicker.view.firstHighlightIndex=e)),i.position=(e===0?0:this.selectpicker.current.data[e-1].position)+i.height}},isVirtual:function(){return this.options.virtualScroll!==!1&&this.selectpicker.main.data.length>=this.options.virtualScroll||this.options.virtualScroll===!0},createView:function(e,i,s){var t=this,n=0;if(this.selectpicker.isSearching=e,this.selectpicker.current=e?this.selectpicker.search:this.selectpicker.main,this.setPositionData(),i){if(s)n=this.$menuInner[0].scrollTop;else if(!t.multiple){var o=t.$element[0],r=(o.options[o.selectedIndex]||{}).liIndex;if(typeof r=="number"&&t.options.size!==!1){var c=t.selectpicker.main.data[r],l=c&&c.position;l&&(n=l-(t.sizeInfo.menuInnerHeight+t.sizeInfo.liHeight)/2)}}}a(n,!0),this.$menuInner.off("scroll.createView").on("scroll.createView",function(d,g){t.noScroll||a(this.scrollTop,g),t.noScroll=!1});function a(d,g){var f=t.selectpicker.current.data.length,w=[],k,I,E,m,u,C,z,T,L=!0,$=t.isVirtual();t.selectpicker.view.scrollTop=d,k=t.options.chunkSize,I=Math.ceil(f/k)||1;for(var x=0;x<I;x++){var y=(x+1)*k;if(x===I-1&&(y=f),w[x]=[x*k+(x?1:0),y],!f)break;u===void 0&&d-1<=t.selectpicker.current.data[y-1].position-t.sizeInfo.menuInnerHeight&&(u=x)}if(u===void 0&&(u=0),C=[t.selectpicker.view.position0,t.selectpicker.view.position1],E=Math.max(0,u-1),m=Math.min(I-1,u+1),t.selectpicker.view.position0=$===!1?0:Math.max(0,w[E][0])||0,t.selectpicker.view.position1=$===!1?f:Math.min(f,w[m][1])||0,z=C[0]!==t.selectpicker.view.position0||C[1]!==t.selectpicker.view.position1,t.activeElement!==void 0&&(g&&(t.activeElement!==t.selectedElement&&t.defocusItem(t.activeElement),t.activeElement=void 0),t.activeElement!==t.selectedElement&&t.defocusItem(t.selectedElement)),t.prevActiveElement!==void 0&&t.prevActiveElement!==t.activeElement&&t.prevActiveElement!==t.selectedElement&&t.defocusItem(t.prevActiveElement),g||z||t.selectpicker.current.hasMore){if(T=t.selectpicker.view.visibleElements?t.selectpicker.view.visibleElements.slice():[],$===!1?t.selectpicker.view.visibleElements=t.selectpicker.current.elements:t.selectpicker.view.visibleElements=t.selectpicker.current.elements.slice(t.selectpicker.view.position0,t.selectpicker.view.position1),t.setOptionStatus(),(e||$===!1&&g)&&(L=!Ce(T,t.selectpicker.view.visibleElements)),(g||$===!0)&&L){var O=t.$menuInner[0],N=document.createDocumentFragment(),H=O.firstChild.cloneNode(!1),V,J,j=t.selectpicker.view.visibleElements,W=[];O.replaceChild(H,O.firstChild);for(var x=0,q=j.length;x<q;x++){var F=j[x],M,U;t.options.sanitize&&(M=F.lastChild,M&&(U=t.selectpicker.current.data[x+t.selectpicker.view.position0],U&&U.content&&!U.sanitized&&(W.push(M),U.sanitized=!0))),N.appendChild(F)}if(t.options.sanitize&&W.length&&ie(W,t.options.whiteList,t.options.sanitizeFn),$===!0?(V=t.selectpicker.view.position0===0?0:t.selectpicker.current.data[t.selectpicker.view.position0-1].position,J=t.selectpicker.view.position1>f-1?0:t.selectpicker.current.data[f-1].position-t.selectpicker.current.data[t.selectpicker.view.position1-1].position,O.firstChild.style.marginTop=V+"px",O.firstChild.style.marginBottom=J+"px"):(O.firstChild.style.marginTop=0,O.firstChild.style.marginBottom=0),O.firstChild.appendChild(N),$===!0&&t.sizeInfo.hasScrollBar){var he=O.firstChild.offsetWidth;if(g&&he<t.sizeInfo.menuInnerInnerWidth&&t.sizeInfo.totalMenuWidth>t.sizeInfo.selectWidth)O.firstChild.style.minWidth=t.sizeInfo.menuInnerInnerWidth+"px";else if(he>t.sizeInfo.menuInnerInnerWidth){t.$menu[0].style.minWidth=0;var fe=O.firstChild.offsetWidth;fe>t.sizeInfo.menuInnerInnerWidth&&(t.sizeInfo.menuInnerInnerWidth=fe,O.firstChild.style.minWidth=t.sizeInfo.menuInnerInnerWidth+"px"),t.$menu[0].style.minWidth=""}}}if((!e&&t.options.source.data||e&&t.options.source.search)&&t.selectpicker.current.hasMore&&u===I-1&&d>0){var Ge=Math.floor(u*t.options.chunkSize/t.options.source.pageSize)+2;t.fetchData(function(){t.render(),t.buildList(f,e),t.setPositionData(),a(d)},e?"search":"data",Ge,e?t.selectpicker.search.previousValue:void 0)}}if(t.prevActiveElement=t.activeElement,!t.options.liveSearch)t.$menuInner.trigger("focus");else if(e&&g){var ee=0,pe;t.selectpicker.view.canHighlight[ee]||(ee=1+t.selectpicker.view.canHighlight.slice(1).indexOf(!0)),pe=t.selectpicker.view.visibleElements[ee],t.defocusItem(t.selectpicker.view.currentActive),t.activeElement=(t.selectpicker.current.data[ee]||{}).element,t.focusItem(pe)}}p(window).off("resize"+b+"."+this.selectId+".createView").on("resize"+b+"."+this.selectId+".createView",function(){var d=t.$newElement.hasClass(h.SHOW);d&&a(t.$menuInner[0].scrollTop)})},focusItem:function(e,i,s){if(e){i=i||this.selectpicker.current.data[this.selectpicker.current.elements.indexOf(this.activeElement)];var t=e.firstChild;t&&(t.setAttribute("aria-setsize",this.selectpicker.view.size),t.setAttribute("aria-posinset",i.posinset),s!==!0&&(this.focusedParent.setAttribute("aria-activedescendant",t.id),e.classList.add("active"),t.classList.add("active")))}},defocusItem:function(e){e&&(e.classList.remove("active"),e.firstChild&&e.firstChild.classList.remove("active"))},setPlaceholder:function(){var e=this,i=!1;if((this.options.placeholder||this.options.allowClear)&&!this.multiple){this.selectpicker.view.titleOption||(this.selectpicker.view.titleOption=document.createElement("option")),i=!0;var s=this.$element[0],t=!1,n=!this.selectpicker.view.titleOption.parentNode,o=s.selectedIndex,r=s.options[o],c=s.querySelector("select > *:not(:disabled)"),l=c?c.index:0,a=window.performance&&window.performance.getEntriesByType("navigation"),d=a&&a.length?a[0].type!=="back_forward":window.performance.navigation.type!==2;n&&(this.selectpicker.view.titleOption.className="bs-title-option",this.selectpicker.view.titleOption.value="",t=!r||o===l&&r.defaultSelected===!1&&this.$element.data("selected")===void 0),(n||this.selectpicker.view.titleOption.index!==0)&&s.insertBefore(this.selectpicker.view.titleOption,s.firstChild),t&&d?s.selectedIndex=0:document.readyState!=="complete"&&window.addEventListener("pageshow",function(){e.selectpicker.view.displayedValue!==s.value&&e.render()})}return i},fetchData:function(e,i,s,t){s=s||1,i=i||"data";var n=this,o=this.options.source[i],r;o?(this.options.virtualScroll=!0,typeof o=="function"?o.call(this,function(c,l,a){var d=n.selectpicker[i==="search"?"search":"main"];d.hasMore=l,d.totalItems=a,r=n.buildData(c,i),e.call(n,r),n.$element.trigger("fetched"+b)},s,t):Array.isArray(o)&&(r=n.buildData(o,i),e.call(n,r))):(r=this.buildData(!1,i),e.call(n,r))},buildData:function(e,i){var s=this,t=e===!1?X.fromOption:X.fromDataSource,n=':not([hidden]):not([data-hidden="true"]):not([style*="display: none"])',o=[],r=this.selectpicker.main.data?this.selectpicker.main.data.length:0,c=0,l=this.setPlaceholder()&&!e?1:0;i==="search"&&(r=this.selectpicker.search.data.length),this.options.hideDisabled&&(n+=":not(:disabled)");var a=e?e.filter(ce,this):this.$element[0].querySelectorAll("select > *"+n);function d(m){var u=o[o.length-1];u&&u.type==="divider"&&(u.optID||m.optID)||(m=m||{},m.type="divider",o.push(m))}function g(m,u){if(u=u||{},u.divider=t(m,"divider"),u.divider===!0)d({optID:u.optID});else{var C=o.length+r,z=t(m,"style"),T=z?Z(z):"",L=(m.className||"")+(u.optgroupClass||"");u.optID&&(L="opt "+L),u.optionClass=L.trim(),u.inlineStyle=T,u.text=t(m,"text"),u.title=t(m,"title"),u.content=t(m,"content"),u.tokens=t(m,"tokens"),u.subtext=t(m,"subtext"),u.icon=t(m,"icon"),u.display=u.content||u.text,u.value=m.value===void 0?m.text:m.value,u.type="option",u.index=C,u.option=m.option?m.option:m,u.option.liIndex=C,u.selected=!!m.selected,u.disabled=u.disabled||!!m.disabled,e!==!1&&(s.selectpicker.optionValuesDataMap[u.value]?u=p.extend(s.selectpicker.optionValuesDataMap[u.value],u):s.selectpicker.optionValuesDataMap[u.value]=u),o.push(u)}}function f(m,u){var C=u[m],z=m-1<l?!1:u[m-1],T=u[m+1],L=e?C.children.filter(ce,this):C.querySelectorAll("option"+n);if(L.length){var $={display:Z(t(I,"label")),subtext:t(C,"subtext"),icon:t(C,"icon"),type:"optgroup-label",optgroupClass:" "+(C.className||""),optgroup:C},x,y;c++,z&&d({optID:c}),$.optID=c,o.push($);for(var O=0,N=L.length;O<N;O++){var H=L[O];O===0&&(x=o.length-1,y=x+N),g(H,{headerIndex:x,lastIndex:y,optID:$.optID,optgroupClass:$.optgroupClass,disabled:C.disabled})}T&&d({optID:c})}}for(var w=a.length,k=l;k<w;k++){var I=a[k],E=I.children;E&&E.length?f.call(this,k,a):g.call(this,I,{})}switch(i){case"data":{this.selectpicker.main.data||(this.selectpicker.main.data=[]),Array.prototype.push.apply(this.selectpicker.main.data,o),this.selectpicker.current.data=this.selectpicker.main.data;break}case"search":{Array.prototype.push.apply(this.selectpicker.search.data,o);break}}return o},buildList:function(e,i){var s=this,t=i?this.selectpicker.search.data:this.selectpicker.main.data,n=[],o=0;(s.options.showTick||s.multiple)&&!v.checkMark.parentNode&&(v.checkMark.className=this.options.iconBase+" "+s.options.tickIcon+" check-mark",v.a.appendChild(v.checkMark));function r(g,f){var w,k=0;switch(f.type){case"divider":w=R.li(!1,h.DIVIDER,f.optID?f.optID+"div":void 0);break;case"option":w=R.li(R.a(R.text.call(s,f),f.optionClass,f.inlineStyle),"",f.optID),w.firstChild&&(w.firstChild.id=s.selectId+"-"+f.index);break;case"optgroup-label":w=R.li(R.label.call(s,f),"dropdown-header"+f.optgroupClass,f.optID);break}f.element?f.element.innerHTML=w.innerHTML:f.element=w,g.push(f.element),f.display&&(k+=f.display.length),f.subtext&&(k+=f.subtext.length),f.icon&&(k+=1),k>o&&(o=k,s.selectpicker.view.widestOption=g[g.length-1])}for(var c=e||0,l=t.length,a=c;a<l;a++){var d=t[a];r(n,d)}e?i?Array.prototype.push.apply(this.selectpicker.search.elements,n):(Array.prototype.push.apply(this.selectpicker.main.elements,n),this.selectpicker.current.elements=this.selectpicker.main.elements):i?this.selectpicker.search.elements=n:this.selectpicker.main.elements=this.selectpicker.current.elements=n},findLis:function(){return this.$menuInner.find(".inner > li")},render:function(e){var i=this,s=this.$element[0],t=this.setPlaceholder()&&s.selectedIndex===0,n=Q.call(this),o=n.length,r=Y.call(this,n),c=this.$button[0],l=c.querySelector(".filter-option-inner-inner"),a=document.createTextNode(this.options.multipleSeparator),d=v.fragment.cloneNode(!1),g,f,w=!1;function k($){$.selected?i.createOption($,!0):$.children&&$.children.length&&$.children.map(k)}if(this.options.source.data&&e&&(n.map(k),s.appendChild(this.selectpicker.main.optionQueue),t&&(t=s.selectedIndex===0)),c.classList.toggle("bs-placeholder",i.multiple?!o:!r&&r!==0),!i.multiple&&n.length===1&&(i.selectpicker.view.displayedValue=r),this.options.selectedTextFormat==="static")d=R.text.call(this,{text:this.options.placeholder},!0);else if(g=this.multiple&&this.options.selectedTextFormat.indexOf("count")!==-1&&o>0,g&&(f=this.options.selectedTextFormat.split(">"),g=f.length>1&&o>f[1]||f.length===1&&o>=2),g===!1){if(!t){for(var I=0;I<o&&I<50;I++){var E=n[I],m={};E&&(this.multiple&&I>0&&d.appendChild(a.cloneNode(!1)),E.title?m.text=E.title:E.content&&i.options.showContent?(m.content=E.content.toString(),w=!0):(i.options.showIcon&&(m.icon=E.icon),i.options.showSubtext&&!i.multiple&&E.subtext&&(m.subtext=" "+E.subtext),m.text=E.text.trim()),d.appendChild(R.text.call(this,m,!0)))}o>49&&d.appendChild(document.createTextNode("..."))}}else{var u=':not([hidden]):not([data-hidden="true"]):not([data-divider="true"]):not([style*="display: none"])';this.options.hideDisabled&&(u+=":not(:disabled)");var C=this.$element[0].querySelectorAll("select > option"+u+", optgroup"+u+" option"+u).length,z=typeof this.options.countSelectedText=="function"?this.options.countSelectedText(o,C):this.options.countSelectedText;d=R.text.call(this,{text:z.replace("{0}",o.toString()).replace("{1}",C.toString())},!0)}if(d.childNodes.length||(d=R.text.call(this,{text:this.options.placeholder?this.options.placeholder:this.options.noneSelectedText},!0)),c.title=d.textContent.replace(/<[^>]*>?/g,"").trim(),this.options.sanitize&&w&&ie([d],i.options.whiteList,i.options.sanitizeFn),l.innerHTML="",l.appendChild(d),S.major<4&&this.$newElement[0].classList.contains("bs3-has-addon")){var T=c.querySelector(".filter-expand"),L=l.cloneNode(!0);L.className="filter-expand",T?c.replaceChild(L,T):c.appendChild(L)}this.$element.trigger("rendered"+b)},setStyle:function(e,i){var s=this.$button[0],t=this.$newElement[0],n=this.options.style.trim(),o;this.$element.attr("class")&&this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,"")),S.major<4&&(t.classList.add("bs3"),t.parentNode.classList&&t.parentNode.classList.contains("input-group")&&(t.previousElementSibling||t.nextElementSibling)&&(t.previousElementSibling||t.nextElementSibling).classList.contains("input-group-addon")&&t.classList.add("bs3-has-addon")),e?o=e.trim():o=n,i=="add"?o&&s.classList.add.apply(s.classList,o.split(" ")):i=="remove"?o&&s.classList.remove.apply(s.classList,o.split(" ")):(n&&s.classList.remove.apply(s.classList,n.split(" ")),o&&s.classList.add.apply(s.classList,o.split(" ")))},liHeight:function(e){if(!(!e&&(this.options.size===!1||Object.keys(this.sizeInfo).length))){var i=v.div.cloneNode(!1),s=v.div.cloneNode(!1),t=v.div.cloneNode(!1),n=document.createElement("ul"),o=v.li.cloneNode(!1),r=v.li.cloneNode(!1),c,l=v.a.cloneNode(!1),a=v.span.cloneNode(!1),d=this.options.header&&this.$menu.find("."+h.POPOVERHEADER).length>0?this.$menu.find("."+h.POPOVERHEADER)[0].cloneNode(!0):null,g=this.options.liveSearch?v.div.cloneNode(!1):null,f=this.options.actionsBox&&this.multiple&&this.$menu.find(".bs-actionsbox").length>0?this.$menu.find(".bs-actionsbox")[0].cloneNode(!0):null,w=this.options.doneButton&&this.multiple&&this.$menu.find(".bs-donebutton").length>0?this.$menu.find(".bs-donebutton")[0].cloneNode(!0):null,k=this.$element[0].options[0];if(this.sizeInfo.selectWidth=this.$newElement[0].offsetWidth,a.className="text",l.className="dropdown-item "+(k?k.className:""),i.className=this.$menu[0].parentNode.className+" "+h.SHOW,i.style.width=0,this.options.width==="auto"&&(s.style.minWidth=0),s.className=h.MENU+" "+h.SHOW,t.className="inner "+h.SHOW,n.className=h.MENU+" inner "+(S.major>="4"?h.SHOW:""),o.className=h.DIVIDER,r.className="dropdown-header",a.appendChild(document.createTextNode("​")),this.selectpicker.current.data.length)for(var I=0;I<this.selectpicker.current.data.length;I++){var E=this.selectpicker.current.data[I];if(E.type==="option"&&p(E.element.firstChild).css("display")!=="none"){c=E.element;break}}else c=v.li.cloneNode(!1),l.appendChild(a),c.appendChild(l);if(r.appendChild(a.cloneNode(!0)),this.selectpicker.view.widestOption&&n.appendChild(this.selectpicker.view.widestOption.cloneNode(!0)),n.appendChild(c),n.appendChild(o),n.appendChild(r),d&&s.appendChild(d),g){var m=document.createElement("input");g.className="bs-searchbox",m.className="form-control",g.appendChild(m),s.appendChild(g)}f&&s.appendChild(f),t.appendChild(n),s.appendChild(t),w&&s.appendChild(w),i.appendChild(s),document.body.appendChild(i);var u=c.offsetHeight,C=r?r.offsetHeight:0,z=d?d.offsetHeight:0,T=g?g.offsetHeight:0,L=f?f.offsetHeight:0,$=w?w.offsetHeight:0,x=p(o).outerHeight(!0),y=window.getComputedStyle(s),O=s.offsetWidth,N={vert:P(y.paddingTop)+P(y.paddingBottom)+P(y.borderTopWidth)+P(y.borderBottomWidth),horiz:P(y.paddingLeft)+P(y.paddingRight)+P(y.borderLeftWidth)+P(y.borderRightWidth)},H={vert:N.vert+P(y.marginTop)+P(y.marginBottom)+2,horiz:N.horiz+P(y.marginLeft)+P(y.marginRight)+2},V;t.style.overflowY="scroll",V=s.offsetWidth-O,document.body.removeChild(i),this.sizeInfo.liHeight=u,this.sizeInfo.dropdownHeaderHeight=C,this.sizeInfo.headerHeight=z,this.sizeInfo.searchHeight=T,this.sizeInfo.actionsHeight=L,this.sizeInfo.doneButtonHeight=$,this.sizeInfo.dividerHeight=x,this.sizeInfo.menuPadding=N,this.sizeInfo.menuExtras=H,this.sizeInfo.menuWidth=O,this.sizeInfo.menuInnerInnerWidth=O-N.horiz,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth,this.sizeInfo.scrollBarWidth=V,this.sizeInfo.selectHeight=this.$newElement[0].offsetHeight,this.setPositionData()}},getSelectPosition:function(){var e=this,i=p(window),s=e.$newElement.offset(),t=p(e.options.container),n;e.options.container&&t.length&&!t.is("body")?(n=t.offset(),n.top+=parseInt(t.css("borderTopWidth")),n.left+=parseInt(t.css("borderLeftWidth"))):n={top:0,left:0};var o=e.options.windowPadding;this.sizeInfo.selectOffsetTop=s.top-n.top-i.scrollTop(),this.sizeInfo.selectOffsetBot=i.height()-this.sizeInfo.selectOffsetTop-this.sizeInfo.selectHeight-n.top-o[2],this.sizeInfo.selectOffsetLeft=s.left-n.left-i.scrollLeft(),this.sizeInfo.selectOffsetRight=i.width()-this.sizeInfo.selectOffsetLeft-this.sizeInfo.selectWidth-n.left-o[1],this.sizeInfo.selectOffsetTop-=o[0],this.sizeInfo.selectOffsetLeft-=o[3]},setMenuSize:function(e){this.getSelectPosition();var i=this.sizeInfo.selectWidth,s=this.sizeInfo.liHeight,t=this.sizeInfo.headerHeight,n=this.sizeInfo.searchHeight,o=this.sizeInfo.actionsHeight,r=this.sizeInfo.doneButtonHeight,c=this.sizeInfo.dividerHeight,l=this.sizeInfo.menuPadding,a,d,g=0,f,w,k,I,E,m;if(this.options.dropupAuto&&(E=s*this.selectpicker.current.data.length+l.vert,m=this.sizeInfo.selectOffsetTop-this.sizeInfo.selectOffsetBot>this.sizeInfo.menuExtras.vert&&E+this.sizeInfo.menuExtras.vert+50>this.sizeInfo.selectOffsetBot,this.selectpicker.isSearching===!0&&(m=this.selectpicker.dropup),this.$newElement.toggleClass(h.DROPUP,m),this.selectpicker.dropup=m),this.options.size==="auto")w=this.selectpicker.current.data.length>3?this.sizeInfo.liHeight*3+this.sizeInfo.menuExtras.vert-2:0,d=this.sizeInfo.selectOffsetBot-this.sizeInfo.menuExtras.vert,f=w+t+n+o+r,I=Math.max(w-l.vert,0),this.$newElement.hasClass(h.DROPUP)&&(d=this.sizeInfo.selectOffsetTop-this.sizeInfo.menuExtras.vert),k=d,a=d-t-n-o-r-l.vert;else if(this.options.size&&this.options.size!="auto"&&this.selectpicker.current.elements.length>this.options.size){for(var u=0;u<this.options.size;u++)this.selectpicker.current.data[u].type==="divider"&&g++;d=s*this.options.size+g*c+l.vert,a=d-l.vert,k=d+t+n+o+r,f=I=""}this.$menu.css({"max-height":k+"px",overflow:"hidden","min-height":f+"px"}),this.$menuInner.css({"max-height":a+"px",overflow:"hidden auto","min-height":I+"px"}),this.sizeInfo.menuInnerHeight=Math.max(a,1),this.selectpicker.current.data.length&&this.selectpicker.current.data[this.selectpicker.current.data.length-1].position>this.sizeInfo.menuInnerHeight&&(this.sizeInfo.hasScrollBar=!0,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth+this.sizeInfo.scrollBarWidth),this.options.dropdownAlignRight==="auto"&&this.$menu.toggleClass(h.MENURIGHT,this.sizeInfo.selectOffsetLeft>this.sizeInfo.selectOffsetRight&&this.sizeInfo.selectOffsetRight<this.sizeInfo.totalMenuWidth-i),this.dropdown&&this.dropdown._popper&&this.dropdown._popper.update()},setSize:function(e){if(this.liHeight(e),this.options.header&&this.$menu.css("padding-top",0),this.options.size!==!1){var i=this,s=p(window);this.setMenuSize(),this.options.liveSearch&&this.$searchbox.off("input.setMenuSize propertychange.setMenuSize").on("input.setMenuSize propertychange.setMenuSize",function(){return i.setMenuSize()}),this.options.size==="auto"?s.off("resize"+b+"."+this.selectId+".setMenuSize scroll"+b+"."+this.selectId+".setMenuSize").on("resize"+b+"."+this.selectId+".setMenuSize scroll"+b+"."+this.selectId+".setMenuSize",function(){return i.setMenuSize()}):this.options.size&&this.options.size!="auto"&&this.selectpicker.current.elements.length>this.options.size&&s.off("resize"+b+"."+this.selectId+".setMenuSize scroll"+b+"."+this.selectId+".setMenuSize")}this.createView(!1,!0,e)},setWidth:function(){var e=this;this.options.width==="auto"?requestAnimationFrame(function(){e.$menu.css("min-width","0"),e.$element.on("loaded"+b,function(){e.liHeight(),e.setMenuSize();var i=e.$newElement.clone().appendTo("body"),s=i.css("width","auto").children("button").outerWidth();i.remove(),e.sizeInfo.selectWidth=Math.max(e.sizeInfo.totalMenuWidth,s),e.$newElement.css("width",e.sizeInfo.selectWidth+"px")})}):this.options.width==="fit"?(this.$menu.css("min-width",""),this.$newElement.css("width","").addClass("fit-width")):this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width",this.options.width)):(this.$menu.css("min-width",""),this.$newElement.css("width","")),this.$newElement.hasClass("fit-width")&&this.options.width!=="fit"&&this.$newElement[0].classList.remove("fit-width")},selectPosition:function(){this.$bsContainer=p('<div class="bs-container" />');var e=this,i=p(this.options.container),s,t,n,o=function(r){var c={},l=e.options.display||(p.fn.dropdown.Constructor.Default?p.fn.dropdown.Constructor.Default.display:!1);e.$bsContainer.addClass(r.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass(h.DROPUP,r.hasClass(h.DROPUP)),s=r.offset(),i.is("body")?t={top:0,left:0}:(t=i.offset(),t.top+=parseInt(i.css("borderTopWidth"))-i.scrollTop(),t.left+=parseInt(i.css("borderLeftWidth"))-i.scrollLeft()),n=r.hasClass(h.DROPUP)?0:r[0].offsetHeight,(S.major<4||l==="static")&&(c.top=s.top-t.top+n,c.left=s.left-t.left),c.width=r[0].offsetWidth,e.$bsContainer.css(c)};this.$button.on("click.bs.dropdown.data-api",function(){e.isDisabled()||(o(e.$newElement),e.$bsContainer.appendTo(e.options.container).toggleClass(h.SHOW,!e.$button.hasClass(h.SHOW)).append(e.$menu))}),p(window).off("resize"+b+"."+this.selectId+" scroll"+b+"."+this.selectId).on("resize"+b+"."+this.selectId+" scroll"+b+"."+this.selectId,function(){var r=e.$newElement.hasClass(h.SHOW);r&&o(e.$newElement)}),this.$element.on("hide"+b,function(){e.$menu.data("height",e.$menu.height()),e.$bsContainer.detach()})},createOption:function(e,i){var s=e.option?e.option:e;if(s&&s.nodeType!==1){var t=(i?v.selectedOption:v.option).cloneNode(!0);s.value!==void 0&&(t.value=s.value),t.textContent=s.text,t.selected=!0,s.liIndex!==void 0?t.liIndex=s.liIndex:i||(t.liIndex=e.index),e.option=t,this.selectpicker.main.optionQueue.appendChild(t)}},setOptionStatus:function(e){var i=this;if(i.noScroll=!1,i.selectpicker.view.visibleElements&&i.selectpicker.view.visibleElements.length){for(var s=0;s<i.selectpicker.view.visibleElements.length;s++){var t=i.selectpicker.current.data[s+i.selectpicker.view.position0],n=t.option;n&&(e!==!0&&i.setDisabled(t),i.setSelected(t))}this.options.source.data&&this.$element[0].appendChild(this.selectpicker.main.optionQueue)}},setSelected:function(e,i){i=i===void 0?e.selected:i;var s=e.element,t=this.activeElement!==void 0,n=this.activeElement===s,o,r,c=n||i&&!this.multiple&&!t;s&&(i!==void 0&&(e.selected=i,e.option&&(e.option.selected=i)),i&&this.options.source.data&&this.createOption(e,!1),r=s.firstChild,i&&(this.selectedElement=s),s.classList.toggle("selected",i),c?(this.focusItem(s,e),this.selectpicker.view.currentActive=s,this.activeElement=s):this.defocusItem(s),r&&(r.classList.toggle("selected",i),i?r.setAttribute("aria-selected",!0):this.multiple?r.setAttribute("aria-selected",!1):r.removeAttribute("aria-selected")),!c&&!t&&i&&this.prevActiveElement!==void 0&&(o=this.prevActiveElement,this.defocusItem(o)))},setDisabled:function(e){var i=e.disabled,s=e.element,t;s&&(t=s.firstChild,s.classList.toggle(h.DISABLED,i),t&&(S.major>="4"&&t.classList.toggle(h.DISABLED,i),i?(t.setAttribute("aria-disabled",i),t.setAttribute("tabindex",-1)):(t.removeAttribute("aria-disabled"),t.setAttribute("tabindex",0))))},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){this.isDisabled()?(this.$newElement[0].classList.add(h.DISABLED),this.$button.addClass(h.DISABLED).attr("aria-disabled",!0)):this.$button[0].classList.contains(h.DISABLED)&&(this.$newElement[0].classList.remove(h.DISABLED),this.$button.removeClass(h.DISABLED).attr("aria-disabled",!1))},clickListener:function(){var e=this,i=p(document);i.data("spaceSelect",!1),this.$button.on("keyup",function(o){/(32)/.test(o.keyCode.toString(10))&&i.data("spaceSelect")&&(o.preventDefault(),i.data("spaceSelect",!1))}),this.$newElement.on("show.bs.dropdown",function(){!e.dropdown&&S.major==="4"&&(e.dropdown=e.$button.data("bs.dropdown"),e.dropdown._menu=e.$menu[0])});function s(o){if(e.multiple)e.deselectAll();else{var r=e.$element[0],c=r.value,l=r.selectedIndex,a=r.options[l],d=a?e.selectpicker.main.data[a.liIndex]:!1;d&&e.setSelected(d,!1),r.selectedIndex=0,B=[l,!1,c],e.$element.triggerNative("change")}e.$newElement.hasClass(h.SHOW)&&(e.options.liveSearch&&e.$searchbox.trigger("focus"),e.createView(!1))}this.$button.on("click.bs.dropdown.data-api",function(o){if(e.options.allowClear){var r=o.target,c=e.$clearButton[0];/MSIE|Trident/.test(window.navigator.userAgent)&&(r=document.elementFromPoint(o.clientX,o.clientY)),(r===c||r.parentElement===c)&&(o.stopImmediatePropagation(),s())}e.$newElement.hasClass(h.SHOW)||e.setSize()});function t(){e.options.liveSearch?e.$searchbox.trigger("focus"):e.$menuInner.trigger("focus")}function n(){e.dropdown&&e.dropdown._popper&&e.dropdown._popper.state?t():requestAnimationFrame(n)}this.$element.on("shown"+b,function(){e.$menuInner[0].scrollTop!==e.selectpicker.view.scrollTop&&(e.$menuInner[0].scrollTop=e.selectpicker.view.scrollTop),S.major>3?requestAnimationFrame(n):t()}),this.$menuInner.on("mouseenter","li a",function(o){var r=this.parentElement,c=e.isVirtual()?e.selectpicker.view.position0:0,l=Array.prototype.indexOf.call(r.parentElement.children,r),a=e.selectpicker.current.data[l+c];e.focusItem(r,a,!0)}),this.$menuInner.on("click","li a",function(o,r){var c=p(this),l=e.$element[0],a=e.isVirtual()?e.selectpicker.view.position0:0,d=e.selectpicker.current.data[c.parent().index()+a],g=d.element,f=Y.call(e),w=l.selectedIndex,k=l.options[w],I=k?e.selectpicker.main.data[k.liIndex]:!1,E=!0;if(e.multiple&&e.options.maxOptions!==1&&o.stopPropagation(),o.preventDefault(),!e.isDisabled()&&!c.parent().hasClass(h.DISABLED)){var m=d.option,u=p(m),C=m.selected,z=e.selectpicker.current.data.find(function(U){return U.optID===d.optID&&U.type==="optgroup-label"}),T=z?z.optgroup:void 0,L=T instanceof Element?X.fromOption:X.fromDataSource,$=T&&T.children,x=parseInt(e.options.maxOptions),y=T&&parseInt(L(T,"maxOptions"))||!1;if(g===e.activeElement&&(r=!0),r||(e.prevActiveElement=e.activeElement,e.activeElement=void 0),!e.multiple||x===1)I&&e.setSelected(I,!1),e.setSelected(d,!0);else if(e.setSelected(d,!C),e.focusedParent.focus(),x!==!1||y!==!1){var O=x<Q.call(e).length,N=0;if(T&&T.children)for(var H=0;H<T.children.length;H++)T.children[H].selected&&N++;var V=y<N;if(x&&O||y&&V)if(x&&x===1)l.selectedIndex=-1,e.setOptionStatus(!0);else if(y&&y===1){for(var H=0;H<$.length;H++){var J=$[H];e.setSelected(e.selectpicker.current.data[J.liIndex],!1)}e.setSelected(d,!0)}else{var j=typeof e.options.maxOptionsText=="string"?[e.options.maxOptionsText,e.options.maxOptionsText]:e.options.maxOptionsText,W=typeof j=="function"?j(x,y):j,q=W[0].replace("{n}",x),F=W[1].replace("{n}",y),M=p('<div class="notify"></div>');W[2]&&(q=q.replace("{var}",W[2][x>1?0:1]),F=F.replace("{var}",W[2][y>1?0:1])),e.$menu.append(M),x&&O&&(M.append(p("<div>"+q+"</div>")),E=!1,e.$element.trigger("maxReached"+b)),y&&V&&(M.append(p("<div>"+F+"</div>")),E=!1,e.$element.trigger("maxReachedGrp"+b)),setTimeout(function(){e.setSelected(d,!1)},10),M[0].classList.add("fadeOut"),setTimeout(function(){M.remove()},1050)}}e.options.source.data&&e.$element[0].appendChild(e.selectpicker.main.optionQueue),!e.multiple||e.multiple&&e.options.maxOptions===1?e.$button.trigger("focus"):e.options.liveSearch&&e.$searchbox.trigger("focus"),E&&(e.multiple||w!==l.selectedIndex)&&(B=[m.index,u.prop("selected"),f],e.$element.triggerNative("change"))}}),this.$menu.on("click","li."+h.DISABLED+" a, ."+h.POPOVERHEADER+", ."+h.POPOVERHEADER+" :not(.close)",function(o){o.currentTarget==this&&(o.preventDefault(),o.stopPropagation(),e.options.liveSearch&&!p(o.target).hasClass("close")?e.$searchbox.trigger("focus"):e.$button.trigger("focus"))}),this.$menuInner.on("click",".divider, .dropdown-header",function(o){o.preventDefault(),o.stopPropagation(),e.options.liveSearch?e.$searchbox.trigger("focus"):e.$button.trigger("focus")}),this.$menu.on("click","."+h.POPOVERHEADER+" .close",function(){e.$button.trigger("click")}),this.$searchbox.on("click",function(o){o.stopPropagation()}),this.$menu.on("click",".actions-btn",function(o){e.options.liveSearch?e.$searchbox.trigger("focus"):e.$button.trigger("focus"),o.preventDefault(),o.stopPropagation(),p(this).hasClass("bs-select-all")?e.selectAll():e.deselectAll()}),this.$button.on("focus"+b,function(o){var r=e.$element[0].getAttribute("tabindex");r!==void 0&&o.originalEvent&&o.originalEvent.isTrusted&&(this.setAttribute("tabindex",r),e.$element[0].setAttribute("tabindex",-1),e.selectpicker.view.tabindex=r)}).on("blur"+b,function(o){e.selectpicker.view.tabindex!==void 0&&o.originalEvent&&o.originalEvent.isTrusted&&(e.$element[0].setAttribute("tabindex",e.selectpicker.view.tabindex),this.setAttribute("tabindex",-1),e.selectpicker.view.tabindex=void 0)}),this.$element.on("change"+b,function(){e.render(),e.$element.trigger("changed"+b,B),B=null}).on("focus"+b,function(){e.options.mobile||e.$button[0].focus()})},liveSearchListener:function(){var e=this;this.$button.on("click.bs.dropdown.data-api",function(){e.$searchbox.val()&&(e.$searchbox.val(""),e.selectpicker.search.previousValue=void 0)}),this.$searchbox.on("click.bs.dropdown.data-api focus.bs.dropdown.data-api touchend.bs.dropdown.data-api",function(i){i.stopPropagation()}),this.$searchbox.on("input propertychange",function(){var i=e.$searchbox[0].value;if(e.selectpicker.search.elements=[],e.selectpicker.search.data=[],i)if(e.selectpicker.search.previousValue=i,e.options.source.search)e.fetchData(function(k){e.render(),e.buildList(void 0,!0),e.noScroll=!0,e.$menuInner.scrollTop(0),e.createView(!0),ae.call(e,k,i)},"search",0,i);else{var s,t=[],n=i.toUpperCase(),o={},r=[],c=e._searchStyle(),l=e.options.liveSearchNormalize;l&&(n=oe(n));for(var s=0;s<e.selectpicker.main.data.length;s++){var a=e.selectpicker.main.data[s];o[s]||(o[s]=ne(a,n,c,l)),o[s]&&a.headerIndex!==void 0&&r.indexOf(a.headerIndex)===-1&&(a.headerIndex>0&&(o[a.headerIndex-1]=!0,r.push(a.headerIndex-1)),o[a.headerIndex]=!0,r.push(a.headerIndex),o[a.lastIndex+1]=!0),o[s]&&a.type!=="optgroup-label"&&r.push(s)}for(var s=0,d=r.length;s<d;s++){var g=r[s],f=r[s-1],a=e.selectpicker.main.data[g],w=e.selectpicker.main.data[f];(a.type!=="divider"||a.type==="divider"&&w&&w.type!=="divider"&&d-1!==s)&&(e.selectpicker.search.data.push(a),t.push(e.selectpicker.main.elements[g]))}e.activeElement=void 0,e.noScroll=!0,e.$menuInner.scrollTop(0),e.selectpicker.search.elements=t,e.createView(!0),ae.call(e,t,i)}else e.selectpicker.search.previousValue&&(e.$menuInner.scrollTop(0),e.createView(!1))})},_searchStyle:function(){return this.options.liveSearchStyle||"contains"},val:function(e){var i=this.$element[0];if(typeof e<"u"){var s=Q.call(this),t=Y.call(this,s);B=[null,null,t],Array.isArray(e)||(e=[e]),e.map(String);for(var n=0;n<s.length;n++){var o=s[n];o&&e.indexOf(String(o.value))===-1&&this.setSelected(o,!1)}if(this.selectpicker.main.data.filter(function(c){return e.indexOf(String(c.value))!==-1?(this.setSelected(c,!0),!0):!1},this),this.options.source.data&&i.appendChild(this.selectpicker.main.optionQueue),this.$element.trigger("changed"+b,B),this.$newElement.hasClass(h.SHOW))if(this.multiple)this.setOptionStatus(!0);else{var r=(i.options[i.selectedIndex]||{}).liIndex;typeof r=="number"&&this.setSelected(this.selectpicker.current.data[r],!0)}return this.render(),B=null,this.$element}else return this.$element.val()},changeAll:function(e){if(this.multiple){typeof e>"u"&&(e=!0);var i=this.$element[0],s=0,t=0,n=Y.call(this);i.classList.add("bs-select-hidden");for(var o=0,r=this.selectpicker.current.data,c=r.length;o<c;o++){var l=r[o],a=l.option;a&&!l.disabled&&l.type!=="divider"&&(l.selected&&s++,a.selected=e,l.selected=e,e===!0&&t++)}i.classList.remove("bs-select-hidden"),s!==t&&(this.setOptionStatus(),B=[null,null,n],this.$element.triggerNative("change"))}},selectAll:function(){return this.changeAll(!0)},deselectAll:function(){return this.changeAll(!1)},toggle:function(e,i){var s,t=i===void 0;e=e||window.event,e&&e.stopPropagation(),t===!1&&(s=this.$newElement[0].classList.contains(h.SHOW),t=i===!0&&s===!1||i===!1&&s===!0),t&&this.$button.trigger("click.bs.dropdown.data-api")},open:function(e){this.toggle(e,!0)},close:function(e){this.toggle(e,!1)},keydown:function(e){var i=p(this),s=i.hasClass("dropdown-toggle"),t=s?i.closest(".dropdown"):i.closest(_.MENU),n=t.data("this"),o=n.findLis(),r,c,l,a,d,g=!1,f=e.which===D.TAB&&!s&&!n.options.selectOnTab,w=_e.test(e.which)||f,k=n.$menuInner[0].scrollTop,I=n.isVirtual(),E=I===!0?n.selectpicker.view.position0:0;if(!(e.which>=112&&e.which<=123)){if(c=n.$menu.hasClass(h.SHOW),!c&&(w||e.which>=48&&e.which<=57||e.which>=96&&e.which<=105||e.which>=65&&e.which<=90)&&(n.$button.trigger("click.bs.dropdown.data-api"),n.options.liveSearch)){n.$searchbox.trigger("focus");return}if(e.which===D.ESCAPE&&c&&(e.preventDefault(),n.$button.trigger("click.bs.dropdown.data-api").trigger("focus")),w){if(!o.length)return;l=n.activeElement,r=l?Array.prototype.indexOf.call(l.parentElement.children,l):-1,r!==-1&&n.defocusItem(l),e.which===D.ARROW_UP?(r!==-1&&r--,r+E<0&&(r+=o.length),n.selectpicker.view.canHighlight[r+E]||(r=n.selectpicker.view.canHighlight.slice(0,r+E).lastIndexOf(!0)-E,r===-1&&(r=o.length-1))):(e.which===D.ARROW_DOWN||f)&&(r++,r+E>=n.selectpicker.view.canHighlight.length&&(r=n.selectpicker.view.firstHighlightIndex),n.selectpicker.view.canHighlight[r+E]||(r=r+1+n.selectpicker.view.canHighlight.slice(r+E+1).indexOf(!0))),e.preventDefault();var m=E+r;e.which===D.ARROW_UP?E===0&&r===o.length-1?(n.$menuInner[0].scrollTop=n.$menuInner[0].scrollHeight,m=n.selectpicker.current.elements.length-1):(a=n.selectpicker.current.data[m],a&&(d=a.position-a.height,g=d<k)):(e.which===D.ARROW_DOWN||f)&&(r===n.selectpicker.view.firstHighlightIndex?(n.$menuInner[0].scrollTop=0,m=n.selectpicker.view.firstHighlightIndex):(a=n.selectpicker.current.data[m],a&&(d=a.position-n.sizeInfo.menuInnerHeight,g=d>k))),l=n.selectpicker.current.elements[m],n.activeElement=(n.selectpicker.current.data[m]||{}).element,n.focusItem(l),n.selectpicker.view.currentActive=l,g&&(n.$menuInner[0].scrollTop=d),n.options.liveSearch?n.$searchbox.trigger("focus"):i.trigger("focus")}else if(!i.is("input")&&!je.test(e.which)||e.which===D.SPACE&&n.selectpicker.keydown.keyHistory){var u,C=[],z;e.preventDefault(),n.selectpicker.keydown.keyHistory+=Ue[e.which],n.selectpicker.keydown.resetKeyHistory.cancel&&clearTimeout(n.selectpicker.keydown.resetKeyHistory.cancel),n.selectpicker.keydown.resetKeyHistory.cancel=n.selectpicker.keydown.resetKeyHistory.start(),z=n.selectpicker.keydown.keyHistory,/^(.)\1+$/.test(z)&&(z=z.charAt(0));for(var T=0;T<n.selectpicker.current.data.length;T++){var L=n.selectpicker.current.data[T],$;$=ne(L,z,"startsWith",!0),$&&n.selectpicker.view.canHighlight[T]&&C.push(L.element)}if(C.length){var x=0;o.removeClass("active").find("a").removeClass("active"),z.length===1&&(x=C.indexOf(n.activeElement),x===-1||x===C.length-1?x=0:x++),u=C[x],a=n.selectpicker.main.data[u],k-a.position>0?(d=a.position-a.height,g=!0):(d=a.position-n.sizeInfo.menuInnerHeight,g=a.position>k+n.sizeInfo.menuInnerHeight),l=n.selectpicker.main.elements[u],n.activeElement=l,n.focusItem(l),l&&l.firstChild.focus(),g&&(n.$menuInner[0].scrollTop=d),i.trigger("focus")}}c&&(e.which===D.SPACE&&!n.selectpicker.keydown.keyHistory||e.which===D.ENTER||e.which===D.TAB&&n.options.selectOnTab)&&(e.which!==D.SPACE&&e.preventDefault(),(!n.options.liveSearch||e.which!==D.SPACE)&&(n.$menuInner.find(".active a").trigger("click",!0),i.trigger("focus"),n.options.liveSearch||(e.preventDefault(),p(document).data("spaceSelect",!0))))}},mobile:function(){this.options.mobile=!0,this.$element[0].classList.add("mobile-device")},refresh:function(){var e=this,i=p.extend({},this.options,se(this.$element),this.$element.data());this.options=i,this.options.source.data?(this.render(),this.buildList()):this.fetchData(function(){e.render(),e.buildList()}),this.checkDisabled(),this.setStyle(),this.setWidth(),this.setSize(!0),this.$element.trigger("refreshed"+b)},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove(),this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove(),this.$bsContainer?this.$bsContainer.remove():this.$menu.remove(),this.selectpicker.view.titleOption&&this.selectpicker.view.titleOption.parentNode&&this.selectpicker.view.titleOption.parentNode.removeChild(this.selectpicker.view.titleOption),this.$element.off(b).removeData("selectpicker").removeClass("bs-select-hidden selectpicker mobile-device"),p(window).off(b+"."+this.selectId)}};function de(e){var i=arguments,s=e;if([].shift.apply(i),!S.success){try{S.full=(re()||"").split(" ")[0].split(".")}catch(c){A.BootstrapVersion?S.full=A.BootstrapVersion.split(" ")[0].split("."):(S.full=[S.major,"0","0"],console.warn("There was an issue retrieving Bootstrap's version. Ensure Bootstrap is being loaded before bootstrap-select and there is no namespace collision. If loading Bootstrap asynchronously, the version may need to be manually specified via $.fn.selectpicker.Constructor.BootstrapVersion.",c))}S.major=S.full[0],S.success=!0}if(S.major>="4"){var t=[];A.DEFAULTS.style===h.BUTTONCLASS&&t.push({name:"style",className:"BUTTONCLASS"}),A.DEFAULTS.iconBase===h.ICONBASE&&t.push({name:"iconBase",className:"ICONBASE"}),A.DEFAULTS.tickIcon===h.TICKICON&&t.push({name:"tickIcon",className:"TICKICON"}),h.DIVIDER="dropdown-divider",h.SHOW="show",h.BUTTONCLASS="btn-light",h.POPOVERHEADER="popover-header",h.ICONBASE="",h.TICKICON="bs-ok-default";for(var n=0;n<t.length;n++){var e=t[n];A.DEFAULTS[e.name]=h[e.className]}}S.major>"4"&&(_.DATA_TOGGLE='data-bs-toggle="dropdown"');var o,r=this.each(function(){var c=p(this);if(c.is("select")){var l=c.data("selectpicker"),a=typeof s=="object"&&s;if(a.title&&(a.placeholder=a.title),l){if(a)for(var w in a)Object.prototype.hasOwnProperty.call(a,w)&&(l.options[w]=a[w])}else{var d=c.data();for(var g in d)Object.prototype.hasOwnProperty.call(d,g)&&p.inArray(g,me)!==-1&&delete d[g];var f=p.extend({},A.DEFAULTS,p.fn.selectpicker.defaults||{},se(c),d,a);f.template=p.extend({},A.DEFAULTS.template,p.fn.selectpicker.defaults?p.fn.selectpicker.defaults.template:{},d.template,a.template),f.source=p.extend({},A.DEFAULTS.source,p.fn.selectpicker.defaults?p.fn.selectpicker.defaults.source:{},a.source),c.data("selectpicker",l=new A(this,f))}typeof s=="string"&&(l[s]instanceof Function?o=l[s].apply(l,i):o=l.options[s])}});return typeof o<"u"?o:r}var Fe=p.fn.selectpicker;p.fn.selectpicker=de,p.fn.selectpicker.Constructor=A,p.fn.selectpicker.noConflict=function(){return p.fn.selectpicker=Fe,this};function ue(){if(S.major<5){if(p.fn.dropdown){var e=p.fn.dropdown.Constructor._dataApiKeydownHandler||p.fn.dropdown.Constructor.prototype.keydown;return e.apply(this,arguments)}}else return te.dataApiKeydownHandler}p(document).off("keydown.bs.dropdown.data-api").on("keydown.bs.dropdown.data-api",":not(.bootstrap-select) > ["+_.DATA_TOGGLE+"]",ue).on("keydown.bs.dropdown.data-api",":not(.bootstrap-select) > .dropdown-menu",ue).on("keydown"+b,".bootstrap-select ["+_.DATA_TOGGLE+'], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',A.prototype.keydown).on("focusin.modal",".bootstrap-select ["+_.DATA_TOGGLE+'], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',function(e){e.stopPropagation()}),document.addEventListener("DOMContentLoaded",function(){p(".selectpicker").each(function(){var e=p(this);de.call(e,e.data())})})})(jQuery);
