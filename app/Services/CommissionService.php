<?php

namespace App\Services;

use App\Enums\CommissionStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Achievement;
use App\Models\Commission;
use App\Models\Product;
use App\Models\User;
use App\Notifications\CommissionApprovedNotification;
use App\Notifications\CommissionRejectedNotification;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class CommissionService
{
    /**
     * Create a new commission for a product.
     */
    public function createCommission(
        User $user,
        Product $product,
        PaymentMethodEnum $paymentMethod,
        ?UploadedFile $paymentProof = null,
        ?string $notes = null
    ): Commission {
        $amount = Commission::calculateCommission($product);

        $commission = new Commission([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'notes' => $notes,
        ]);

        // Handle payment proof upload
        if ($paymentProof && $paymentMethod->requiresProof($paymentMethod->value)) {
            $commission->payment_proof = $this->storePaymentProof($paymentProof, $user->id);
        }

        $commission->save();

        // Check and award achievements
        $this->checkAndAwardAchievements($user);

        return $commission;
    }

    /**
     * Approve a commission.
     */
    public function approveCommission(Commission $commission, User $admin, ?string $reviewNotes = null): bool
    {
        $commission->update([
            'status' => CommissionStatusEnum::APPROVED,
            'admin_id' => $admin->id,
            'review_notes' => $reviewNotes,
            'reviewed_at' => now(),
        ]);

        // Send notification to user
        $commission->user->notify(new CommissionApprovedNotification($commission));

        // Check and award achievements after approval
        $this->checkAndAwardAchievements($commission->user);

        return true;
    }

    /**
     * Reject a commission.
     */
    public function rejectCommission(Commission $commission, User $admin, ?string $reviewNotes = null): bool
    {
        $commission->update([
            'status' => CommissionStatusEnum::REJECTED,
            'admin_id' => $admin->id,
            'review_notes' => $reviewNotes,
            'reviewed_at' => now(),
        ]);

        // Send notification to user
        $commission->user->notify(new CommissionRejectedNotification($commission));

        return true;
    }

    /**
     * Store payment proof file.
     */
    private function storePaymentProof(UploadedFile $file, int $userId): string
    {
        $filename = time() . '_' . $userId . '_' . $file->getClientOriginalName();
        $file->storeAs('commissions', $filename, 'public');
        return $filename;
    }

    /**
     * Check and award commission-related achievements.
     */
    private function checkAndAwardAchievements(User $user): void
    {
        $currentYear = now()->year;
        
        // Get user's approved commissions for current year
        $approvedCommissions = Commission::where('user_id', $user->id)
            ->approved()
            ->byYear($currentYear)
            ->count();

        // Award "First Commission Paid" achievement
        if ($approvedCommissions >= 1) {
            $this->awardAchievement($user, 'first_commission_paid');
        }

        // Award "Loyal Seller" achievement (multiple commissions in same year)
        if ($approvedCommissions >= 3) {
            $this->awardAchievement($user, 'loyal_seller');
        }

        // Award "High Volume Seller" achievement (5+ commissions)
        if ($approvedCommissions >= 5) {
            $this->awardAchievement($user, 'high_volume_seller');
        }

        // Award "Professional Seller" achievement (10+ commissions)
        if ($approvedCommissions >= 10) {
            $this->awardAchievement($user, 'professional_seller');
        }

        // Award "Diamond Seller" achievement (20+ commissions)
        if ($approvedCommissions >= 20) {
            $this->awardAchievement($user, 'diamond_seller');
        }
    }

    /**
     * Award achievement to user if not already awarded.
     */
    private function awardAchievement(User $user, string $achievementSlug): void
    {
        $achievement = Achievement::where('slug', $achievementSlug)->first();
        
        if (!$achievement) {
            return;
        }

        // Check if user already has this achievement for current year
        $currentYear = now()->year;
        $hasAchievement = $user->achievements()
            ->where('achievement_id', $achievement->id)
            ->whereYear('user_achievements.achieved_at', $currentYear)
            ->exists();

        if (!$hasAchievement) {
            $user->achievements()->attach($achievement->id, [
                'achieved_at' => now(),
            ]);
        }
    }

    /**
     * Get commission statistics for dashboard.
     */
    public function getCommissionStats(?int $year = null): array
    {
        $year = $year ?? now()->year;

        $totalCommissions = Commission::byYear($year)->count();
        $approvedCommissions = Commission::byYear($year)->approved()->count();
        $rejectedCommissions = Commission::byYear($year)->rejected()->count();
        $pendingCommissions = Commission::byYear($year)->pending()->count();
        
        $totalRevenue = Commission::byYear($year)->approved()->sum('amount');

        return [
            'total_commissions' => $totalCommissions,
            'approved_commissions' => $approvedCommissions,
            'rejected_commissions' => $rejectedCommissions,
            'pending_commissions' => $pendingCommissions,
            'total_revenue' => $totalRevenue,
            'approval_rate' => $totalCommissions > 0 ? round(($approvedCommissions / $totalCommissions) * 100, 2) : 0,
        ];
    }

    /**
     * Get top commission paying users.
     */
    public function getTopCommissionUsers(?int $year = null, int $limit = 10): array
    {
        $year = $year ?? now()->year;

        return Commission::byYear($year)
            ->approved()
            ->selectRaw('user_id, COUNT(*) as commission_count, SUM(amount) as total_amount')
            ->with('user:id,name,email')
            ->groupBy('user_id')
            ->orderByDesc('total_amount')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get latest pending commissions.
     */
    public function getLatestPendingCommissions(int $limit = 5): array
    {
        return Commission::pending()
            ->with(['user:id,name,email', 'product:id,title'])
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
