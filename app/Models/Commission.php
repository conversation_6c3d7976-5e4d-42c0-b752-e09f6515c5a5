<?php

namespace App\Models;

use App\Enums\CommissionStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Commission extends Model
{
    use HasFactory, WithHashId;

    protected $fillable = [
        'user_id',
        'product_id',
        'amount',
        'payment_method',
        'payment_proof',
        'notes',
        'status',
        'admin_id',
        'review_notes',
        'reviewed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_method' => PaymentMethodEnum::class,
        'status' => CommissionStatusEnum::class,
        'reviewed_at' => 'datetime',
    ];

    /**
     * Bootstrap the model and its traits.
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            // Auto-approve online payments
            if ($model->payment_method === PaymentMethodEnum::ONLINE_PAYMENT) {
                $model->status = CommissionStatusEnum::APPROVED;
                $model->reviewed_at = now();
            }
        });
        
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }

    /**
     * Get the user that owns the commission.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product associated with the commission.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the admin who reviewed the commission.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Calculate commission amount based on product price.
     */
    public static function calculateCommission(Product $product): float
    {
        return round($product->price * 0.01, 2); // 1% commission
    }

    /**
     * Get the payment proof URL.
     */
    public function getPaymentProofUrlAttribute(): ?string
    {
        if (!$this->payment_proof) {
            return null;
        }

        return Storage::url('commissions/' . $this->payment_proof);
    }

    /**
     * Check if payment proof is required for this payment method.
     */
    public function requiresPaymentProof(): bool
    {
        return PaymentMethodEnum::requiresProof($this->payment_method->value);
    }

    /**
     * Scope for pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', CommissionStatusEnum::PENDING);
    }

    /**
     * Scope for approved commissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', CommissionStatusEnum::APPROVED);
    }

    /**
     * Scope for rejected commissions.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', CommissionStatusEnum::REJECTED);
    }

    /**
     * Scope for commissions by year.
     */
    public function scopeByYear($query, $year = null)
    {
        $year = $year ?? now()->year;
        return $query->whereYear('created_at', $year);
    }

    /**
     * Scope for commissions by payment method.
     */
    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return CommissionStatusEnum::getBadgeClass($this->status->value);
    }

    /**
     * Get payment method label in Arabic.
     */
    public function getPaymentMethodLabelAttribute(): string
    {
        return PaymentMethodEnum::getLabel($this->payment_method->value);
    }

    /**
     * Get status label in Arabic.
     */
    public function getStatusLabelAttribute(): string
    {
        return CommissionStatusEnum::getLabel($this->status->value);
    }
}
