<?php

// Simple script to publish the chat module's JavaScript files to the public directory

// Create the public directory if it doesn't exist
$publicDir = __DIR__ . '/public/modules/chat/js';
if (!is_dir($publicDir)) {
    mkdir($publicDir, 0755, true);
}

// Copy the chat.js file to the public directory
$sourceFile = __DIR__ . '/Modules/Chat/resources/js/chat.js';
$destFile = $publicDir . '/chat.js';

if (file_exists($sourceFile)) {
    if (copy($sourceFile, $destFile)) {
        echo "Successfully published chat.js to public/modules/chat/js/\n";
    } else {
        echo "Failed to copy chat.js\n";
    }
} else {
    echo "Source file chat.js not found\n";
}

echo "Done!\n";
